// Playground JavaScript

// Initialize playground
document.addEventListener('DOMContentLoaded', function() {
    initializePlayground();
});

function initializePlayground() {
    const componentType = document.getElementById('component-type');
    const generateCodeBtn = document.getElementById('generate-code');
    const copyCodeBtn = document.getElementById('copy-generated-code');
    
    // Component type change handler
    componentType.addEventListener('change', handleComponentTypeChange);
    
    // Generate code button
    generateCodeBtn.addEventListener('click', generateCode);
    
    // Copy code button
    copyCodeBtn.addEventListener('click', copyGeneratedCode);
    
    // Initialize control listeners
    initializeControlListeners();
    
    // Initial generation
    generateCode();
}

function handleComponentTypeChange() {
    const componentType = document.getElementById('component-type').value;
    const controls = document.querySelectorAll('.component-controls');
    
    // Hide all controls
    controls.forEach(control => control.classList.add('hidden'));
    
    // Show relevant controls
    const activeControl = document.getElementById(`${componentType.replace('-', '')}-controls`);
    if (activeControl) {
        activeControl.classList.remove('hidden');
    }
    
    // Generate new code
    generateCode();
}

function initializeControlListeners() {
    // Button controls
    const buttonControls = ['button-variant', 'button-size', 'button-shape', 'button-text'];
    buttonControls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', generateCode);
            element.addEventListener('input', generateCode);
        }
    });
    
    // Card controls
    const cardControls = ['card-variant', 'card-title', 'card-content'];
    cardControls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', generateCode);
            element.addEventListener('input', generateCode);
        }
    });
    
    // Glass effect controls
    const glassControls = ['glass-intensity', 'blur-amount', 'opacity-amount'];
    glassControls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', generateCode);
            element.addEventListener('input', generateCode);
        }
    });
    
    // Range input value display
    const blurAmount = document.getElementById('blur-amount');
    const opacityAmount = document.getElementById('opacity-amount');
    
    if (blurAmount) {
        blurAmount.addEventListener('input', () => {
            document.getElementById('blur-value').textContent = `${blurAmount.value}px`;
        });
    }
    
    if (opacityAmount) {
        opacityAmount.addEventListener('input', () => {
            document.getElementById('opacity-value').textContent = `${opacityAmount.value}%`;
        });
    }
}

function generateCode() {
    const componentType = document.getElementById('component-type').value;
    
    switch (componentType) {
        case 'button':
            generateButtonCode();
            break;
        case 'card':
            generateCardCode();
            break;
        case 'glass-effect':
            generateGlassEffectCode();
            break;
    }
    
    // Update CSS variables
    updateCSSVariables();
}

function generateButtonCode() {
    const variant = document.getElementById('button-variant').value;
    const size = document.getElementById('button-size').value;
    const shape = document.getElementById('button-shape').value;
    const text = document.getElementById('button-text').value;
    
    // Build class string
    let classes = ['glass-btn'];
    if (variant) classes.push(`glass-btn-${variant}`);
    if (size) classes.push(`glass-btn-${size}`);
    if (shape) classes.push(`glass-btn-${shape}`);
    
    const classString = classes.join(' ');
    
    // Generate HTML
    const html = `<button class="${classString}" data-glass-button>${text}</button>`;
    
    // Update preview
    updatePreview(html);
    
    // Update code display
    updateCodeDisplay(html);
}

function generateCardCode() {
    const variant = document.getElementById('card-variant').value;
    const title = document.getElementById('card-title').value;
    const content = document.getElementById('card-content').value;
    
    // Build class string
    let classes = ['glass-card'];
    if (variant) classes.push(`glass-card-${variant}`);
    
    const classString = classes.join(' ');
    
    // Generate HTML
    const html = `<div class="${classString}" data-glass-card style="max-width: 300px;">
  <div class="glass-card-header">
    <h3 class="glass-card-title">${title}</h3>
  </div>
  <div class="glass-card-body">
    <p class="glass-card-text">${content}</p>
  </div>
  <div class="glass-card-footer">
    <div class="glass-card-actions">
      <button class="glass-btn glass-btn-ghost glass-btn-sm">Cancel</button>
      <button class="glass-btn glass-btn-primary glass-btn-sm">Action</button>
    </div>
  </div>
</div>`;
    
    // Update preview
    updatePreview(html);
    
    // Update code display
    updateCodeDisplay(html);
}

function generateGlassEffectCode() {
    const intensity = document.getElementById('glass-intensity').value;
    const blurAmount = document.getElementById('blur-amount').value;
    const opacityAmount = document.getElementById('opacity-amount').value;
    
    // Build class string
    let classes = ['glass'];
    if (intensity) classes.push(`glass-${intensity}`);
    
    const classString = classes.join(' ');
    
    // Generate custom styles
    const customStyles = `backdrop-filter: blur(${blurAmount}px); background: rgba(255, 255, 255, ${opacityAmount / 100});`;
    
    // Generate HTML
    const html = `<div class="${classString}" style="${customStyles} padding: 2rem; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); max-width: 300px;">
  <h3 style="margin: 0 0 1rem 0; color: var(--text-primary);">Glass Effect</h3>
  <p style="margin: 0; color: var(--text-secondary);">This element demonstrates the customized glass effect with your chosen settings.</p>
</div>`;
    
    // Update preview
    updatePreview(html);
    
    // Update code display
    updateCodeDisplay(html);
}

function updatePreview(html) {
    const previewContainer = document.getElementById('preview-container');
    previewContainer.innerHTML = html;
    
    // Reinitialize components
    setTimeout(() => {
        if (window.LiquidGlassUI) {
            new window.LiquidGlassUI();
        }
    }, 100);
    
    // Add animation
    previewContainer.classList.add('control-change-animation');
    setTimeout(() => {
        previewContainer.classList.remove('control-change-animation');
    }, 300);
}

function updateCodeDisplay(html) {
    const codeElement = document.getElementById('generated-code');
    codeElement.textContent = html;
    
    // Re-highlight syntax
    if (window.Prism) {
        window.Prism.highlightElement(codeElement);
    }
}

function updateCSSVariables() {
    const componentType = document.getElementById('component-type').value;
    const cssVariablesElement = document.getElementById('css-variables');
    
    let cssVariables = ':root {\n';
    
    if (componentType === 'glass-effect') {
        const blurAmount = document.getElementById('blur-amount').value;
        const opacityAmount = document.getElementById('opacity-amount').value;
        
        cssVariables += `  --glass-blur-custom: ${blurAmount}px;\n`;
        cssVariables += `  --glass-opacity-custom: ${opacityAmount / 100};\n`;
        cssVariables += `  --glass-border-opacity: 0.2;\n`;
    } else {
        cssVariables += `  --glass-blur-md: 8px;\n`;
        cssVariables += `  --glass-opacity-medium: 0.15;\n`;
        cssVariables += `  --glass-border-opacity: 0.2;\n`;
        cssVariables += `  --glass-duration-normal: 250ms;\n`;
    }
    
    cssVariables += '}';
    
    cssVariablesElement.textContent = cssVariables;
    
    // Re-highlight syntax
    if (window.Prism) {
        window.Prism.highlightElement(cssVariablesElement);
    }
}

function copyGeneratedCode() {
    const codeElement = document.getElementById('generated-code');
    const text = codeElement.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        const copyBtn = document.getElementById('copy-generated-code');
        const originalHTML = copyBtn.innerHTML;
        
        copyBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Copied!
        `;
        copyBtn.classList.add('success-state');
        
        setTimeout(() => {
            copyBtn.innerHTML = originalHTML;
            copyBtn.classList.remove('success-state');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy code:', err);
        
        // Fallback: select text
        const range = document.createRange();
        range.selectNode(codeElement);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
    });
}

// Export functions for different formats
function exportAsHTML() {
    const html = document.getElementById('generated-code').textContent;
    const cssVariables = document.getElementById('css-variables').textContent;
    
    const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass UI Component</title>
    <link rel="stylesheet" href="https://unpkg.com/liquid-glass-ui/dist/style.css">
    <style>
        ${cssVariables}
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 2rem;
        }
    </style>
</head>
<body>
    ${html}
    <script src="https://unpkg.com/liquid-glass-ui/dist/index.umd.js"></script>
    <script>
        new LiquidGlassUI();
    </script>
</body>
</html>`;
    
    downloadFile('liquid-glass-component.html', fullHTML);
}

function exportAsCSS() {
    const cssVariables = document.getElementById('css-variables').textContent;
    downloadFile('liquid-glass-variables.css', cssVariables);
}

function downloadFile(filename, content) {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Add export buttons functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add export buttons to the playground
    const generateCodeBtn = document.getElementById('generate-code');
    if (generateCodeBtn) {
        const exportOptions = document.createElement('div');
        exportOptions.className = 'export-options';
        exportOptions.innerHTML = `
            <button class="export-btn" onclick="exportAsHTML()">Export HTML</button>
            <button class="export-btn" onclick="exportAsCSS()">Export CSS</button>
        `;
        generateCodeBtn.parentNode.appendChild(exportOptions);
    }
});

// Make functions global for onclick handlers
window.exportAsHTML = exportAsHTML;
window.exportAsCSS = exportAsCSS;
