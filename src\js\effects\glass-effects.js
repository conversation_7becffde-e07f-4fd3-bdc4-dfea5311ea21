/**
 * Glass Effects
 * Core glass effect management and utilities
 */

export class GlassEffects {
  constructor(options = {}) {
    this.options = {
      enableAnimations: true,
      enableInteractions: true,
      performanceMode: 'auto', // 'low', 'medium', 'high', 'auto'
      ...options
    }

    this.elements = new Map()
    this.observers = new Map()
    this.isInitialized = false
  }

  /**
   * Initialize glass effects system
   */
  init() {
    if (this.isInitialized) return

    this.detectPerformanceCapabilities()
    this.setupIntersectionObserver()
    this.bindGlobalEvents()
    this.scanForElements()
    
    this.isInitialized = true
  }

  /**
   * Detect device performance capabilities
   */
  detectPerformanceCapabilities() {
    if (this.options.performanceMode !== 'auto') return

    const deviceMemory = navigator.deviceMemory || 4
    const hardwareConcurrency = navigator.hardwareConcurrency || 4
    const connection = navigator.connection

    let performanceScore = 0

    // Memory score (0-3)
    if (deviceMemory >= 8) performanceScore += 3
    else if (deviceMemory >= 4) performanceScore += 2
    else if (deviceMemory >= 2) performanceScore += 1

    // CPU score (0-3)
    if (hardwareConcurrency >= 8) performanceScore += 3
    else if (hardwareConcurrency >= 4) performanceScore += 2
    else if (hardwareConcurrency >= 2) performanceScore += 1

    // Connection score (0-2)
    if (connection) {
      if (connection.effectiveType === '4g') performanceScore += 2
      else if (connection.effectiveType === '3g') performanceScore += 1
    } else {
      performanceScore += 2 // Assume good connection if unknown
    }

    // Set performance mode based on score
    if (performanceScore >= 6) this.options.performanceMode = 'high'
    else if (performanceScore >= 4) this.options.performanceMode = 'medium'
    else this.options.performanceMode = 'low'

    // Add performance class to document
    document.documentElement.classList.add(`performance-${this.options.performanceMode}`)
  }

  /**
   * Setup intersection observer for performance optimization
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) return

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const element = entry.target
          const effectData = this.elements.get(element)
          
          if (!effectData) return

          if (entry.isIntersecting) {
            this.activateElement(element, effectData)
          } else {
            this.deactivateElement(element, effectData)
          }
        })
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    )
  }

  /**
   * Bind global events
   */
  bindGlobalEvents() {
    // Handle visibility change for performance
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAllEffects()
      } else {
        this.resumeAllEffects()
      }
    })

    // Handle resize events
    let resizeTimeout
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        this.updateAllElements()
      }, 100)
    })
  }

  /**
   * Scan for elements with glass effects
   */
  scanForElements() {
    const selectors = [
      '[data-glass-effect]',
      '.glass',
      '.glass-subtle',
      '.glass-light',
      '.glass-medium',
      '.glass-strong',
      '.glass-intense'
    ]

    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        this.addElement(element)
      })
    })
  }

  /**
   * Add element to glass effects system
   */
  addElement(element, options = {}) {
    if (this.elements.has(element)) return

    const effectData = {
      element,
      options: { ...this.getDefaultOptions(), ...options },
      isActive: false,
      animations: new Set(),
      observers: new Set()
    }

    this.elements.set(element, effectData)
    
    // Add to intersection observer
    if (this.intersectionObserver) {
      this.intersectionObserver.observe(element)
    } else {
      this.activateElement(element, effectData)
    }

    // Initialize element
    this.initializeElement(element, effectData)
  }

  /**
   * Get default options based on performance mode
   */
  getDefaultOptions() {
    const baseOptions = {
      enableBlur: true,
      enableShadows: true,
      enableAnimations: this.options.enableAnimations,
      enableInteractions: this.options.enableInteractions
    }

    switch (this.options.performanceMode) {
      case 'low':
        return {
          ...baseOptions,
          enableBlur: false,
          enableAnimations: false,
          maxBlur: 4
        }
      case 'medium':
        return {
          ...baseOptions,
          maxBlur: 8
        }
      case 'high':
        return {
          ...baseOptions,
          maxBlur: 16
        }
      default:
        return baseOptions
    }
  }

  /**
   * Initialize element with glass effects
   */
  initializeElement(element, effectData) {
    const { options } = effectData

    // Add base glass classes
    element.classList.add('glass-element')
    
    // Add performance-specific classes
    element.classList.add(`glass-performance-${this.options.performanceMode}`)

    // Set up CSS custom properties
    this.updateElementProperties(element, effectData)

    // Add interaction listeners if enabled
    if (options.enableInteractions) {
      this.addInteractionListeners(element, effectData)
    }
  }

  /**
   * Update element CSS custom properties
   */
  updateElementProperties(element, effectData) {
    const { options } = effectData
    const rect = element.getBoundingClientRect()

    // Set size properties
    element.style.setProperty('--element-width', `${rect.width}px`)
    element.style.setProperty('--element-height', `${rect.height}px`)

    // Set performance properties
    element.style.setProperty('--max-blur', `${options.maxBlur || 8}px`)
    element.style.setProperty('--enable-animations', options.enableAnimations ? '1' : '0')
  }

  /**
   * Add interaction listeners to element
   */
  addInteractionListeners(element, effectData) {
    const listeners = {
      mouseenter: () => this.handleElementHover(element, effectData, true),
      mouseleave: () => this.handleElementHover(element, effectData, false),
      focus: () => this.handleElementFocus(element, effectData, true),
      blur: () => this.handleElementFocus(element, effectData, false),
      click: (event) => this.handleElementClick(element, effectData, event)
    }

    Object.entries(listeners).forEach(([event, handler]) => {
      element.addEventListener(event, handler)
      effectData.observers.add(() => {
        element.removeEventListener(event, handler)
      })
    })
  }

  /**
   * Handle element hover
   */
  handleElementHover(element, effectData, isHovering) {
    if (!effectData.options.enableInteractions) return

    element.classList.toggle('glass-hover', isHovering)
    
    if (isHovering) {
      this.addAnimation(element, 'hover-in')
    } else {
      this.addAnimation(element, 'hover-out')
    }
  }

  /**
   * Handle element focus
   */
  handleElementFocus(element, effectData, isFocused) {
    if (!effectData.options.enableInteractions) return

    element.classList.toggle('glass-focus', isFocused)
  }

  /**
   * Handle element click
   */
  handleElementClick(element, effectData, event) {
    if (!effectData.options.enableInteractions) return

    this.createRippleEffect(element, event)
    this.addAnimation(element, 'click')
  }

  /**
   * Create ripple effect
   */
  createRippleEffect(element, event) {
    const rect = element.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const ripple = document.createElement('div')
    ripple.className = 'glass-ripple'
    ripple.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: glass-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `

    element.style.position = 'relative'
    element.appendChild(ripple)

    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
    }, 600)
  }

  /**
   * Add animation to element
   */
  addAnimation(element, animationType) {
    const effectData = this.elements.get(element)
    if (!effectData || !effectData.options.enableAnimations) return

    effectData.animations.add(animationType)
    element.classList.add(`glass-animate-${animationType}`)

    setTimeout(() => {
      effectData.animations.delete(animationType)
      element.classList.remove(`glass-animate-${animationType}`)
    }, 300)
  }

  /**
   * Activate element effects
   */
  activateElement(element, effectData) {
    if (effectData.isActive) return

    effectData.isActive = true
    element.classList.add('glass-active')
  }

  /**
   * Deactivate element effects
   */
  deactivateElement(element, effectData) {
    if (!effectData.isActive) return

    effectData.isActive = false
    element.classList.remove('glass-active')
  }

  /**
   * Remove element from glass effects system
   */
  removeElement(element) {
    const effectData = this.elements.get(element)
    if (!effectData) return

    // Remove from intersection observer
    if (this.intersectionObserver) {
      this.intersectionObserver.unobserve(element)
    }

    // Clean up observers
    effectData.observers.forEach(cleanup => cleanup())

    // Remove classes
    element.classList.remove('glass-element', 'glass-active', 'glass-hover', 'glass-focus')

    // Remove from elements map
    this.elements.delete(element)
  }

  /**
   * Update all elements
   */
  updateAllElements() {
    this.elements.forEach((effectData, element) => {
      this.updateElementProperties(element, effectData)
    })
  }

  /**
   * Pause all effects
   */
  pauseAllEffects() {
    this.elements.forEach((effectData, element) => {
      element.classList.add('glass-paused')
    })
  }

  /**
   * Resume all effects
   */
  resumeAllEffects() {
    this.elements.forEach((effectData, element) => {
      element.classList.remove('glass-paused')
    })
  }

  /**
   * Destroy glass effects system
   */
  destroy() {
    // Clean up all elements
    this.elements.forEach((effectData, element) => {
      this.removeElement(element)
    })

    // Clean up observers
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
    }

    this.elements.clear()
    this.observers.clear()
    this.isInitialized = false
  }
}
