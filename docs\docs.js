// Documentation JavaScript

// Initialize documentation
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeNavigation();
    initializeComponentTabs();
    initializeCopyButtons();
    loadDefaultComponent();
});

// Theme Management
function initializeTheme() {
    const themeToggle = document.querySelector('[data-theme-toggle]');
    const savedTheme = localStorage.getItem('liquid-glass-theme') || 'auto';
    
    setTheme(savedTheme);
    
    themeToggle?.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        setTheme(newTheme);
        localStorage.setItem('liquid-glass-theme', newTheme);
    });
}

function setTheme(theme) {
    if (theme === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        theme = prefersDark ? 'dark' : 'light';
    }
    
    document.documentElement.setAttribute('data-theme', theme);
    
    // Update theme toggle icon
    const lightIcon = document.querySelector('.theme-icon-light');
    const darkIcon = document.querySelector('.theme-icon-dark');
    
    if (theme === 'dark') {
        lightIcon?.classList.add('hidden');
        darkIcon?.classList.remove('hidden');
    } else {
        lightIcon?.classList.remove('hidden');
        darkIcon?.classList.add('hidden');
    }
}

// Navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const target = link.getAttribute('href');
            scrollToSection(target.substring(1));
        });
    });
    
    // Update active nav on scroll
    window.addEventListener('scroll', updateActiveNav);
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offset = 80; // Account for fixed nav
        const elementPosition = section.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}

function updateActiveNav() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    
    sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        if (sectionTop <= 100) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// Component Tabs
function initializeComponentTabs() {
    const tabs = document.querySelectorAll('.component-tab');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            tab.classList.add('active');
            
            // Load component content
            const component = tab.getAttribute('data-component');
            loadComponentContent(component);
        });
    });
}

function loadDefaultComponent() {
    loadComponentContent('buttons');
}

function loadComponentContent(component) {
    const content = document.getElementById('component-content');
    
    switch (component) {
        case 'buttons':
            content.innerHTML = getButtonsContent();
            break;
        case 'cards':
            content.innerHTML = getCardsContent();
            break;
        case 'effects':
            content.innerHTML = getEffectsContent();
            break;
        case 'utilities':
            content.innerHTML = getUtilitiesContent();
            break;
        default:
            content.innerHTML = getButtonsContent();
    }
    
    // Reinitialize components after loading content
    setTimeout(() => {
        if (window.LiquidGlassUI) {
            new window.LiquidGlassUI();
        }
        initializeCopyButtons();
    }, 100);
}

// Copy to Clipboard
function initializeCopyButtons() {
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', () => copyCode(button));
    });
}

function copyCode(button) {
    const codeBlock = button.parentElement.querySelector('code');
    const text = codeBlock.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        button.classList.add('copied');
        button.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        `;
        
        setTimeout(() => {
            button.classList.remove('copied');
            button.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            `;
        }, 2000);
    });
}

// Component Content Functions
function getButtonsContent() {
    return `
        <div class="demo-section fade-in-up">
            <h3 class="demo-title">Glass Buttons</h3>
            <p class="demo-description">Beautiful glass buttons with various styles, sizes, and interactive effects.</p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>Button Variants</h4>
                    <p>Different button styles for various use cases</p>
                    <div class="demo-preview">
                        <button class="glass-btn glass-btn-primary" data-glass-button>Primary</button>
                        <button class="glass-btn glass-btn-secondary" data-glass-button>Secondary</button>
                        <button class="glass-btn glass-btn-success" data-glass-button>Success</button>
                        <button class="glass-btn glass-btn-warning" data-glass-button>Warning</button>
                        <button class="glass-btn glass-btn-danger" data-glass-button>Danger</button>
                        <button class="glass-btn glass-btn-ghost" data-glass-button>Ghost</button>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;button class="glass-btn glass-btn-primary"&gt;Primary&lt;/button&gt;
&lt;button class="glass-btn glass-btn-secondary"&gt;Secondary&lt;/button&gt;
&lt;button class="glass-btn glass-btn-success"&gt;Success&lt;/button&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="demo-item">
                    <h4>Button Sizes</h4>
                    <p>Multiple sizes to fit your design needs</p>
                    <div class="demo-preview">
                        <button class="glass-btn glass-btn-primary glass-btn-xs" data-glass-button>Extra Small</button>
                        <button class="glass-btn glass-btn-primary glass-btn-sm" data-glass-button>Small</button>
                        <button class="glass-btn glass-btn-primary" data-glass-button>Default</button>
                        <button class="glass-btn glass-btn-primary glass-btn-lg" data-glass-button>Large</button>
                        <button class="glass-btn glass-btn-primary glass-btn-xl" data-glass-button>Extra Large</button>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;button class="glass-btn glass-btn-primary glass-btn-xs"&gt;XS&lt;/button&gt;
&lt;button class="glass-btn glass-btn-primary glass-btn-sm"&gt;SM&lt;/button&gt;
&lt;button class="glass-btn glass-btn-primary"&gt;Default&lt;/button&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="demo-item">
                    <h4>Special Shapes</h4>
                    <p>Unique button shapes for special use cases</p>
                    <div class="demo-preview">
                        <button class="glass-btn glass-btn-primary glass-btn-circle" data-glass-button>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                        <button class="glass-btn glass-btn-secondary glass-btn-pill" data-glass-button>Pill Button</button>
                        <button class="glass-btn glass-btn-success glass-btn-square" data-glass-button>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;button class="glass-btn glass-btn-primary glass-btn-circle"&gt;●&lt;/button&gt;
&lt;button class="glass-btn glass-btn-secondary glass-btn-pill"&gt;Pill&lt;/button&gt;
&lt;button class="glass-btn glass-btn-success glass-btn-square"&gt;■&lt;/button&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="demo-item">
                    <h4>Button States</h4>
                    <p>Loading, disabled, and active states</p>
                    <div class="demo-preview">
                        <button class="glass-btn glass-btn-primary" data-glass-button>Normal</button>
                        <button class="glass-btn glass-btn-primary loading" data-glass-button disabled>Loading</button>
                        <button class="glass-btn glass-btn-primary" data-glass-button disabled>Disabled</button>
                        <button class="glass-btn glass-btn-primary active" data-glass-button>Active</button>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;button class="glass-btn glass-btn-primary"&gt;Normal&lt;/button&gt;
&lt;button class="glass-btn glass-btn-primary loading" disabled&gt;Loading&lt;/button&gt;
&lt;button class="glass-btn glass-btn-primary" disabled&gt;Disabled&lt;/button&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getCardsContent() {
    return `
        <div class="demo-section fade-in-up">
            <h3 class="demo-title">Glass Cards</h3>
            <p class="demo-description">Elegant glass cards with various layouts and interactive effects.</p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>Basic Cards</h4>
                    <p>Simple card layouts with glass effects</p>
                    <div class="demo-preview">
                        <div class="glass-card" style="width: 250px;" data-glass-card>
                            <div class="glass-card-header">
                                <h3 class="glass-card-title">Card Title</h3>
                                <p class="glass-card-subtitle">Card subtitle</p>
                            </div>
                            <div class="glass-card-body">
                                <p class="glass-card-text">This is a basic glass card with beautiful translucent effects.</p>
                            </div>
                            <div class="glass-card-footer">
                                <div class="glass-card-actions">
                                    <button class="glass-btn glass-btn-ghost glass-btn-sm">Cancel</button>
                                    <button class="glass-btn glass-btn-primary glass-btn-sm">Action</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;div class="glass-card" data-glass-card&gt;
  &lt;div class="glass-card-header"&gt;
    &lt;h3 class="glass-card-title"&gt;Card Title&lt;/h3&gt;
    &lt;p class="glass-card-subtitle"&gt;Subtitle&lt;/p&gt;
  &lt;/div&gt;
  &lt;div class="glass-card-body"&gt;
    &lt;p class="glass-card-text"&gt;Card content&lt;/p&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="demo-item">
                    <h4>Card Variants</h4>
                    <p>Different card styles and intensities</p>
                    <div class="demo-preview" style="flex-direction: column; gap: 1rem;">
                        <div class="glass-card glass-card-subtle" style="width: 200px; padding: 1rem;" data-glass-card>
                            <h4>Subtle Card</h4>
                            <p>Light glass effect</p>
                        </div>
                        <div class="glass-card glass-card-elevated" style="width: 200px; padding: 1rem;" data-glass-card>
                            <h4>Elevated Card</h4>
                            <p>Enhanced depth</p>
                        </div>
                        <div class="glass-card glass-card-outlined" style="width: 200px; padding: 1rem;" data-glass-card>
                            <h4>Outlined Card</h4>
                            <p>Defined borders</p>
                        </div>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;div class="glass-card glass-card-subtle"&gt;Subtle&lt;/div&gt;
&lt;div class="glass-card glass-card-elevated"&gt;Elevated&lt;/div&gt;
&lt;div class="glass-card glass-card-outlined"&gt;Outlined&lt;/div&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getEffectsContent() {
    return `
        <div class="demo-section fade-in-up">
            <h3 class="demo-title">Glass Effects</h3>
            <p class="demo-description">Various glass effect intensities and styles.</p>
            
            <div class="glass-demo-grid">
                <div class="glass-demo-item glass-subtle">
                    <span>Subtle Glass</span>
                </div>
                <div class="glass-demo-item glass-light">
                    <span>Light Glass</span>
                </div>
                <div class="glass-demo-item glass-medium">
                    <span>Medium Glass</span>
                </div>
                <div class="glass-demo-item glass-strong">
                    <span>Strong Glass</span>
                </div>
                <div class="glass-demo-item glass-intense">
                    <span>Intense Glass</span>
                </div>
                <div class="glass-demo-item glass">
                    <span>Default Glass</span>
                </div>
            </div>
            
            <div class="code-block">
                <pre><code class="language-html">&lt;div class="glass-subtle"&gt;Subtle Glass&lt;/div&gt;
&lt;div class="glass-light"&gt;Light Glass&lt;/div&gt;
&lt;div class="glass-medium"&gt;Medium Glass&lt;/div&gt;
&lt;div class="glass-strong"&gt;Strong Glass&lt;/div&gt;
&lt;div class="glass-intense"&gt;Intense Glass&lt;/div&gt;</code></pre>
                <button class="copy-btn" onclick="copyCode(this)">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
}

function getUtilitiesContent() {
    return `
        <div class="demo-section fade-in-up">
            <h3 class="demo-title">Utility Classes</h3>
            <p class="demo-description">Helpful utility classes for glass effects and layouts.</p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>Backdrop Blur</h4>
                    <p>Control backdrop blur intensity</p>
                    <div class="demo-preview">
                        <div class="backdrop-blur-sm p-4 rounded-lg bg-white/20">blur-sm</div>
                        <div class="backdrop-blur-md p-4 rounded-lg bg-white/20">blur-md</div>
                        <div class="backdrop-blur-lg p-4 rounded-lg bg-white/20">blur-lg</div>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;div class="backdrop-blur-sm"&gt;Small blur&lt;/div&gt;
&lt;div class="backdrop-blur-md"&gt;Medium blur&lt;/div&gt;
&lt;div class="backdrop-blur-lg"&gt;Large blur&lt;/div&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="demo-item">
                    <h4>Glass Backgrounds</h4>
                    <p>Pre-defined glass background utilities</p>
                    <div class="demo-preview">
                        <div class="bg-glass-light p-4 rounded-lg">Light</div>
                        <div class="bg-glass-medium p-4 rounded-lg">Medium</div>
                        <div class="bg-glass-strong p-4 rounded-lg">Strong</div>
                    </div>
                    <div class="code-block">
                        <pre><code class="language-html">&lt;div class="bg-glass-light"&gt;Light background&lt;/div&gt;
&lt;div class="bg-glass-medium"&gt;Medium background&lt;/div&gt;
&lt;div class="bg-glass-strong"&gt;Strong background&lt;/div&gt;</code></pre>
                        <button class="copy-btn" onclick="copyCode(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}
