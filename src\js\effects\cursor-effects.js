/**
 * Cursor Effects
 * Creates dynamic cursor-based shadow and lighting effects for desktop users
 */

export class CursorEffects {
  constructor(options = {}) {
    this.options = {
      enableShadows: true,
      enableLighting: true,
      shadowIntensity: 0.3,
      lightingIntensity: 0.2,
      maxDistance: 200,
      smoothing: 0.1,
      ...options
    }

    this.cursor = { x: 0, y: 0 }
    this.smoothCursor = { x: 0, y: 0 }
    this.elements = new Set()
    this.isActive = false
    this.animationFrame = null
  }

  /**
   * Initialize cursor effects
   */
  init() {
    if (this.isActive) return

    this.isActive = true
    this.bindEvents()
    this.findElements()
    this.startAnimation()
  }

  /**
   * Bind mouse events
   */
  bindEvents() {
    this.handleMouseMove = this.handleMouseMove.bind(this)
    this.handleMouseLeave = this.handleMouseLeave.bind(this)

    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseleave', this.handleMouseLeave)
  }

  /**
   * Find elements with cursor effects
   */
  findElements() {
    const selectors = [
      '[data-cursor-effect]',
      '.glass-cursor-effect',
      '.glass',
      '.glass-button',
      '.glass-card'
    ]

    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        this.addElement(element)
      })
    })
  }

  /**
   * Add element to cursor effects
   */
  addElement(element) {
    if (this.elements.has(element)) return

    this.elements.add(element)
    
    // Add CSS custom properties for cursor effects
    element.style.setProperty('--cursor-x', '0')
    element.style.setProperty('--cursor-y', '0')
    element.style.setProperty('--cursor-distance', '1')
    element.style.setProperty('--cursor-intensity', '0')

    // Add cursor effect class
    element.classList.add('has-cursor-effect')
  }

  /**
   * Remove element from cursor effects
   */
  removeElement(element) {
    if (!this.elements.has(element)) return

    this.elements.delete(element)
    element.classList.remove('has-cursor-effect')
    
    // Reset CSS custom properties
    element.style.removeProperty('--cursor-x')
    element.style.removeProperty('--cursor-y')
    element.style.removeProperty('--cursor-distance')
    element.style.removeProperty('--cursor-intensity')
  }

  /**
   * Handle mouse move
   */
  handleMouseMove(event) {
    this.cursor.x = event.clientX
    this.cursor.y = event.clientY
  }

  /**
   * Handle mouse leave
   */
  handleMouseLeave() {
    this.cursor.x = -1000
    this.cursor.y = -1000
  }

  /**
   * Start animation loop
   */
  startAnimation() {
    const animate = () => {
      if (!this.isActive) return

      this.updateCursor()
      this.updateElements()
      
      this.animationFrame = requestAnimationFrame(animate)
    }

    animate()
  }

  /**
   * Update smooth cursor position
   */
  updateCursor() {
    this.smoothCursor.x += (this.cursor.x - this.smoothCursor.x) * this.options.smoothing
    this.smoothCursor.y += (this.cursor.y - this.smoothCursor.y) * this.options.smoothing
  }

  /**
   * Update all elements with cursor effects
   */
  updateElements() {
    this.elements.forEach(element => {
      this.updateElement(element)
    })
  }

  /**
   * Update individual element
   */
  updateElement(element) {
    const rect = element.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    // Calculate distance from cursor to element center
    const deltaX = this.smoothCursor.x - centerX
    const deltaY = this.smoothCursor.y - centerY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // Calculate normalized values
    const normalizedDistance = Math.min(distance / this.options.maxDistance, 1)
    const intensity = 1 - normalizedDistance

    // Calculate relative position within element
    const relativeX = (this.smoothCursor.x - rect.left) / rect.width
    const relativeY = (this.smoothCursor.y - rect.top) / rect.height

    // Update CSS custom properties
    element.style.setProperty('--cursor-x', relativeX.toFixed(3))
    element.style.setProperty('--cursor-y', relativeY.toFixed(3))
    element.style.setProperty('--cursor-distance', normalizedDistance.toFixed(3))
    element.style.setProperty('--cursor-intensity', intensity.toFixed(3))

    // Apply shadow effects
    if (this.options.enableShadows) {
      this.applyShadowEffect(element, deltaX, deltaY, intensity)
    }

    // Apply lighting effects
    if (this.options.enableLighting) {
      this.applyLightingEffect(element, relativeX, relativeY, intensity)
    }
  }

  /**
   * Apply shadow effect based on cursor position
   */
  applyShadowEffect(element, deltaX, deltaY, intensity) {
    const shadowIntensity = intensity * this.options.shadowIntensity
    const shadowX = -deltaX * 0.1 * shadowIntensity
    const shadowY = -deltaY * 0.1 * shadowIntensity
    const shadowBlur = 20 * shadowIntensity
    const shadowOpacity = 0.3 * shadowIntensity

    const shadow = `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, ${shadowOpacity})`
    element.style.setProperty('--cursor-shadow', shadow)
  }

  /**
   * Apply lighting effect based on cursor position
   */
  applyLightingEffect(element, relativeX, relativeY, intensity) {
    const lightIntensity = intensity * this.options.lightingIntensity
    
    // Create radial gradient for lighting effect
    const gradientX = relativeX * 100
    const gradientY = relativeY * 100
    const gradientSize = 150 * intensity
    
    const gradient = `radial-gradient(${gradientSize}px circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`
    element.style.setProperty('--cursor-light', gradient)
  }

  /**
   * Create ripple effect at cursor position
   */
  createRipple(element, event) {
    const rect = element.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const ripple = document.createElement('div')
    ripple.className = 'cursor-ripple'
    ripple.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: cursor-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `

    element.style.position = 'relative'
    element.appendChild(ripple)

    // Remove ripple after animation
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
    }, 600)
  }

  /**
   * Add ripple effect to element
   */
  addRippleEffect(element) {
    const handleClick = (event) => {
      this.createRipple(element, event)
    }

    element.addEventListener('click', handleClick)
    
    return () => {
      element.removeEventListener('click', handleClick)
    }
  }

  /**
   * Update options
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
  }

  /**
   * Pause cursor effects
   */
  pause() {
    this.isActive = false
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }

  /**
   * Resume cursor effects
   */
  resume() {
    if (!this.isActive) {
      this.isActive = true
      this.startAnimation()
    }
  }

  /**
   * Destroy cursor effects
   */
  destroy() {
    this.pause()
    
    // Remove event listeners
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseleave', this.handleMouseLeave)

    // Clean up elements
    this.elements.forEach(element => {
      this.removeElement(element)
    })
    
    this.elements.clear()
  }

  /**
   * Get cursor position
   */
  getCursorPosition() {
    return { ...this.smoothCursor }
  }

  /**
   * Check if cursor effects are active
   */
  isEnabled() {
    return this.isActive
  }
}
