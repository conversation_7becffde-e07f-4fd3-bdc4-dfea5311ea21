/* Apple Liquid Glass UI - Form Components */

/* Form Container */
.glass-form {
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-large);
  padding: var(--glass-space-large);
  background: var(--glass-material-thin);
  border-radius: var(--glass-radius-extra-large);
  border: var(--glass-border-ultra-thin);
  box-shadow: var(--glass-shadow-regular);
}

/* Form Group */
.glass-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-small);
}

.glass-form-row {
  display: flex;
  gap: var(--glass-space-medium);
  flex-wrap: wrap;
}

.glass-form-row .glass-form-group {
  flex: 1;
  min-width: 200px;
}

/* Labels */
.glass-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.8);
  letter-spacing: -0.01em;
  margin-bottom: var(--glass-space-small);
}

.glass-label.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 4px;
}

/* Input Fields */
.glass-input {
  width: 100%;
  padding: var(--glass-space-medium);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-large);
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: rgba(0, 0, 0, 0.9);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  box-shadow: var(--glass-shadow-thin);
}

.glass-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.glass-input:focus {
  outline: none;
  border: 1px solid #007AFF;
  box-shadow: var(--glass-shadow-regular), 0 0 0 2px rgba(0, 122, 255, 0.2);
  transform: translateY(-1px);
  background: var(--glass-material-thick);
}

.glass-input:hover:not(:focus) {
  border-color: rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

/* Textarea */
.glass-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Select */
.glass-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--glass-space-medium) center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: calc(var(--glass-space-medium) * 2.5);
}

/* Checkbox */
.glass-checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--glass-space-medium);
  cursor: pointer;
}

.glass-checkbox {
  position: relative;
  width: 20px;
  height: 20px;
  appearance: none;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-small);
  cursor: pointer;
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-checkbox:checked {
  background: #007AFF;
  border-color: #007AFF;
}

.glass-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.glass-checkbox:hover {
  transform: scale(1.05);
  box-shadow: var(--glass-shadow-thin);
}

.glass-checkbox-label {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

/* Radio Button */
.glass-radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-medium);
}

.glass-radio-item {
  display: flex;
  align-items: center;
  gap: var(--glass-space-medium);
  cursor: pointer;
}

.glass-radio {
  position: relative;
  width: 20px;
  height: 20px;
  appearance: none;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-radio:checked {
  border-color: #007AFF;
}

.glass-radio:checked::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: 10px;
  height: 10px;
  background: #007AFF;
  border-radius: 50%;
}

.glass-radio:hover {
  transform: scale(1.05);
  box-shadow: var(--glass-shadow-thin);
}

.glass-radio-label {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

/* Toggle Switch */
.glass-toggle-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--glass-space-medium);
}

.glass-toggle {
  position: relative;
  width: 52px;
  height: 32px;
  appearance: none;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: 16px;
  cursor: pointer;
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
  box-shadow: var(--glass-shadow-thin);
}

.glass-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 26px;
  height: 26px;
  background: white;
  border-radius: 50%;
  transition: all var(--glass-duration-normal) var(--glass-ease-spring);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.glass-toggle:checked {
  background: #007AFF;
  border-color: #007AFF;
}

.glass-toggle:checked::before {
  transform: translateX(20px);
}

.glass-toggle:hover {
  transform: scale(1.02);
  box-shadow: var(--glass-shadow-regular);
}

.glass-toggle-label {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
}

/* Range Slider */
.glass-range-group {
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-small);
}

.glass-range {
  width: 100%;
  height: 6px;
  appearance: none;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.glass-range::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #007AFF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--glass-shadow-thin);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-range::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: var(--glass-shadow-regular);
}

.glass-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #007AFF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--glass-shadow-thin);
}

.glass-range-value {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
  font-weight: 500;
}

/* File Input */
.glass-file-input {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.glass-file-input input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.glass-file-label {
  display: flex;
  align-items: center;
  gap: var(--glass-space-medium);
  padding: var(--glass-space-medium) var(--glass-space-large);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-large);
  cursor: pointer;
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.8);
}

.glass-file-label:hover {
  background: var(--glass-material-thick);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow-regular);
}

/* Form Validation */
.glass-input.error {
  border-color: #FF3B30;
  box-shadow: var(--glass-shadow-thin), 0 0 0 2px rgba(255, 59, 48, 0.2);
}

.glass-input.success {
  border-color: #34C759;
  box-shadow: var(--glass-shadow-thin), 0 0 0 2px rgba(52, 199, 89, 0.2);
}

.glass-error-message {
  font-size: 0.75rem;
  color: #FF3B30;
  margin-top: var(--glass-space-small);
}

.glass-success-message {
  font-size: 0.75rem;
  color: #34C759;
  margin-top: var(--glass-space-small);
}

/* Form Actions */
.glass-form-actions {
  display: flex;
  gap: var(--glass-space-medium);
  justify-content: flex-end;
  margin-top: var(--glass-space-large);
  flex-wrap: wrap;
}

@media (max-width: 480px) {
  .glass-form-actions {
    flex-direction: column;
  }
  
  .glass-form-row {
    flex-direction: column;
  }
  
  .glass-form-row .glass-form-group {
    min-width: auto;
  }
}
