/* Liquid Glass UI - Layout Utilities */

/* Glass Container Utilities */
.glass-container {
  position: relative;
  isolation: isolate;
}

.glass-container-fluid {
  width: 100%;
  max-width: none;
  padding-left: var(--glass-space-md);
  padding-right: var(--glass-space-md);
}

.glass-container-sm {
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--glass-space-md);
  padding-right: var(--glass-space-md);
}

.glass-container-md {
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--glass-space-md);
  padding-right: var(--glass-space-md);
}

.glass-container-lg {
  max-width: 1024px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--glass-space-md);
  padding-right: var(--glass-space-md);
}

.glass-container-xl {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--glass-space-md);
  padding-right: var(--glass-space-md);
}

/* Glass Grid Utilities */
.glass-grid {
  display: grid;
  gap: var(--glass-space-md);
}

.glass-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.glass-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.glass-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.glass-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.glass-grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.glass-grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.glass-grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

.glass-col-span-1 { grid-column: span 1; }
.glass-col-span-2 { grid-column: span 2; }
.glass-col-span-3 { grid-column: span 3; }
.glass-col-span-4 { grid-column: span 4; }
.glass-col-span-5 { grid-column: span 5; }
.glass-col-span-6 { grid-column: span 6; }
.glass-col-span-full { grid-column: 1 / -1; }

/* Glass Flexbox Utilities */
.glass-flex {
  display: flex;
  gap: var(--glass-space-md);
}

.glass-flex-col {
  flex-direction: column;
}

.glass-flex-row {
  flex-direction: row;
}

.glass-flex-wrap {
  flex-wrap: wrap;
}

.glass-flex-nowrap {
  flex-wrap: nowrap;
}

.glass-items-start { align-items: flex-start; }
.glass-items-center { align-items: center; }
.glass-items-end { align-items: flex-end; }
.glass-items-stretch { align-items: stretch; }

.glass-justify-start { justify-content: flex-start; }
.glass-justify-center { justify-content: center; }
.glass-justify-end { justify-content: flex-end; }
.glass-justify-between { justify-content: space-between; }
.glass-justify-around { justify-content: space-around; }
.glass-justify-evenly { justify-content: space-evenly; }

.glass-flex-1 { flex: 1 1 0%; }
.glass-flex-auto { flex: 1 1 auto; }
.glass-flex-initial { flex: 0 1 auto; }
.glass-flex-none { flex: none; }

/* Glass Stack Utilities */
.glass-stack {
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-md);
}

.glass-stack-sm {
  gap: var(--glass-space-sm);
}

.glass-stack-lg {
  gap: var(--glass-space-lg);
}

.glass-stack-xl {
  gap: var(--glass-space-xl);
}

/* Glass Cluster Utilities */
.glass-cluster {
  display: flex;
  flex-wrap: wrap;
  gap: var(--glass-space-md);
  align-items: center;
}

.glass-cluster-sm {
  gap: var(--glass-space-sm);
}

.glass-cluster-lg {
  gap: var(--glass-space-lg);
}

/* Glass Center Utilities */
.glass-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.glass-center-x {
  display: flex;
  justify-content: center;
}

.glass-center-y {
  display: flex;
  align-items: center;
}

/* Glass Position Utilities */
.glass-relative { position: relative; }
.glass-absolute { position: absolute; }
.glass-fixed { position: fixed; }
.glass-sticky { position: sticky; }

.glass-inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.glass-top-0 { top: 0; }
.glass-right-0 { right: 0; }
.glass-bottom-0 { bottom: 0; }
.glass-left-0 { left: 0; }

/* Glass Overflow Utilities */
.glass-overflow-hidden { overflow: hidden; }
.glass-overflow-auto { overflow: auto; }
.glass-overflow-scroll { overflow: scroll; }
.glass-overflow-visible { overflow: visible; }

.glass-overflow-x-hidden { overflow-x: hidden; }
.glass-overflow-y-hidden { overflow-y: hidden; }
.glass-overflow-x-auto { overflow-x: auto; }
.glass-overflow-y-auto { overflow-y: auto; }

/* Glass Display Utilities */
.glass-block { display: block; }
.glass-inline { display: inline; }
.glass-inline-block { display: inline-block; }
.glass-hidden { display: none; }

/* Glass Sizing Utilities */
.glass-w-full { width: 100%; }
.glass-w-auto { width: auto; }
.glass-w-fit { width: fit-content; }
.glass-w-min { width: min-content; }
.glass-w-max { width: max-content; }

.glass-h-full { height: 100%; }
.glass-h-auto { height: auto; }
.glass-h-fit { height: fit-content; }
.glass-h-min { height: min-content; }
.glass-h-max { height: max-content; }
.glass-h-screen { height: 100vh; }

.glass-min-h-0 { min-height: 0; }
.glass-min-h-full { min-height: 100%; }
.glass-min-h-screen { min-height: 100vh; }

.glass-max-w-none { max-width: none; }
.glass-max-w-xs { max-width: 20rem; }
.glass-max-w-sm { max-width: 24rem; }
.glass-max-w-md { max-width: 28rem; }
.glass-max-w-lg { max-width: 32rem; }
.glass-max-w-xl { max-width: 36rem; }
.glass-max-w-2xl { max-width: 42rem; }
.glass-max-w-full { max-width: 100%; }

/* Glass Aspect Ratio Utilities */
.glass-aspect-square { aspect-ratio: 1 / 1; }
.glass-aspect-video { aspect-ratio: 16 / 9; }
.glass-aspect-photo { aspect-ratio: 4 / 3; }

/* Glass Layer Utilities */
.glass-layer {
  position: relative;
  z-index: var(--glass-z-base);
}

.glass-layer-raised {
  z-index: var(--glass-z-raised);
}

.glass-layer-overlay {
  z-index: var(--glass-z-overlay);
}

.glass-layer-modal {
  z-index: var(--glass-z-modal);
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .glass-sm\:hidden { display: none; }
  .glass-sm\:block { display: block; }
  .glass-sm\:flex { display: flex; }
  .glass-sm\:grid { display: grid; }
  
  .glass-sm\:flex-col { flex-direction: column; }
  .glass-sm\:flex-row { flex-direction: row; }
  
  .glass-sm\:w-full { width: 100%; }
  .glass-sm\:h-auto { height: auto; }
}

@media (min-width: 641px) and (max-width: 768px) {
  .glass-md\:hidden { display: none; }
  .glass-md\:block { display: block; }
  .glass-md\:flex { display: flex; }
  .glass-md\:grid { display: grid; }
  
  .glass-md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .glass-md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 769px) {
  .glass-lg\:hidden { display: none; }
  .glass-lg\:block { display: block; }
  .glass-lg\:flex { display: flex; }
  .glass-lg\:grid { display: grid; }
  
  .glass-lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .glass-lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .glass-lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}
