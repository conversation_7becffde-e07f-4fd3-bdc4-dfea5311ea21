<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glass Effects Test</title>
    <style>
        :root {
            /* Apple Material Vibrancy Effects */
            --glass-vibrancy-ultra-thin: rgba(255, 255, 255, 0.9);
            --glass-vibrancy-thin: rgba(255, 255, 255, 0.8);
            --glass-vibrancy-regular: rgba(255, 255, 255, 0.7);
            --glass-vibrancy-thick: rgba(255, 255, 255, 0.6);
            --glass-vibrancy-heavy: rgba(255, 255, 255, 0.5);
            --glass-vibrancy-ultra-heavy: rgba(255, 255, 255, 0.4);

            /* Apple Blur Levels */
            --glass-blur-ultra: 1px;
            --glass-blur-thin: 3px;
            --glass-blur-regular: 6px;
            --glass-blur-thick: 10px;
            --glass-blur-heavy: 15px;
            --glass-blur-ultra-heavy: 25px;

            /* Apple Material Backgrounds */
            --glass-material-ultra-thin: rgba(255, 255, 255, 0.9);
            --glass-material-thin: rgba(255, 255, 255, 0.8);
            --glass-material-regular: rgba(255, 255, 255, 0.7);
            --glass-material-thick: rgba(255, 255, 255, 0.6);
            --glass-material-heavy: rgba(255, 255, 255, 0.5);
            --glass-material-ultra-heavy: rgba(255, 255, 255, 0.4);

            /* Apple Borders */
            --glass-border-ultra-thin: 0.5px solid rgba(255, 255, 255, 0.3);
            --glass-border-thin: 0.5px solid rgba(255, 255, 255, 0.4);
            --glass-border-regular: 0.5px solid rgba(255, 255, 255, 0.5);
            --glass-border-thick: 1px solid rgba(255, 255, 255, 0.6);

            /* Apple Shadows */
            --glass-shadow-ultra-thin: 0 0.5px 1px rgba(0, 0, 0, 0.04), 0 0.5px 2px rgba(0, 0, 0, 0.02);
            --glass-shadow-thin: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
            --glass-shadow-regular: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.06);
            --glass-shadow-thick: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.08);
            --glass-shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.1);

            /* Apple Highlights */
            --glass-highlight-top: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);

            /* Apple Border Radius */
            --glass-radius-large: 1rem;
            --glass-radius-extra-large: 1.25rem;

            /* Apple Animation */
            --glass-duration-normal: 300ms;
            --glass-ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

            /* Apple Spacing */
            --glass-space-large: 1rem;
            --glass-space-extra-large: 1.5rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1551384963-cccb0b7ed94b?q=80&w=3247&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') center/cover;
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 2rem;
            align-items: center;
            justify-content: center;
        }

        .glass {
            position: relative;
            background: var(--glass-material-regular);
            backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            border: var(--glass-border-regular);
            border-radius: var(--glass-radius-large);
            box-shadow: var(--glass-shadow-regular);
            padding: var(--glass-space-extra-large);
            overflow: hidden;
            transition: all var(--glass-duration-normal) var(--glass-ease-spring);
        }

        .glass::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: var(--glass-highlight-top);
            pointer-events: none;
            z-index: 1;
        }

        .glass > * {
            position: relative;
            z-index: 2;
        }

        .glass:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--glass-shadow-heavy);
        }

        .glass-container {
            position: relative;
            display: flex;
            border-radius: var(--glass-radius-large);
            overflow: hidden;
            box-shadow: var(--glass-shadow-regular);
            transition: all var(--glass-duration-normal) var(--glass-ease-spring);
            min-height: 120px;
            width: 400px;
        }

        .glass-filter {
            position: absolute;
            inset: 0;
            z-index: 0;
            backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            filter: url(#liquid-glass-distortion);
            isolation: isolate;
        }

        .glass-overlay {
            position: absolute;
            inset: 0;
            z-index: 1;
            background: var(--glass-material-regular);
        }

        .glass-specular {
            position: absolute;
            inset: 0;
            z-index: 2;
            border-radius: inherit;
            overflow: hidden;
            box-shadow: 
                inset 1px 1px 0 var(--glass-highlight-top),
                inset 0 0 5px var(--glass-highlight-top);
        }

        .glass-content {
            position: relative;
            z-index: 3;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--glass-space-large);
            width: 100%;
            text-align: center;
        }

        h1 {
            color: white;
            text-shadow: 0 0 20px rgba(0,0,0,0.5);
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        h2 {
            color: rgba(0, 0, 0, 0.9);
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        h3 {
            color: white;
            text-shadow: 0 0 3px rgba(0,0,0,0.5);
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        p {
            color: rgba(0, 0, 0, 0.7);
            margin: 8px 0 0;
            line-height: 1.6;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            width: 100%;
            max-width: 1200px;
        }
    </style>
</head>
<body>
    <!-- SVG Filters -->
    <svg style="position: absolute; width: 0; height: 0; pointer-events: none;">
        <defs>
            <filter id="liquid-glass-distortion" x="0%" y="0%" width="100%" height="100%">
                <feTurbulence type="fractalNoise" baseFrequency="0.008 0.008" numOctaves="2" seed="92" result="noise" />
                <feGaussianBlur in="noise" stdDeviation="2" result="blurred" />
                <feDisplacementMap in="SourceGraphic" in2="blurred" scale="70" xChannelSelector="R" yChannelSelector="G" />
            </filter>
        </defs>
    </svg>

    <h1>Glass Effects Test</h1>

    <div class="test-grid">
        <!-- Basic Glass -->
        <div class="glass">
            <h2>Basic Glass Effect</h2>
            <p>This should show a basic glass effect with backdrop blur, border, and shadow.</p>
        </div>

        <!-- Advanced Layered Glass -->
        <div class="glass-container">
            <div class="glass-filter"></div>
            <div class="glass-overlay"></div>
            <div class="glass-specular"></div>
            <div class="glass-content">
                <div>
                    <h3>Layered Glass</h3>
                    <p style="color: rgba(255,255,255,0.8);">Advanced multi-layer glass system with SVG distortion.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animate SVG filters
        function animateFilters() {
            const turbulence = document.querySelector('feTurbulence');
            if (turbulence) {
                const time = Date.now() * 0.0001;
                const baseFreq = 0.008 + Math.sin(time) * 0.002;
                turbulence.setAttribute('baseFrequency', `${baseFreq} ${baseFreq}`);
            }
            requestAnimationFrame(animateFilters);
        }
        
        animateFilters();
    </script>
</body>
</html>
