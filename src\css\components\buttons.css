/* Liquid Glass UI - Button Components */

/* Base Button Styles */
.glass-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--glass-space-sm);
  padding: var(--glass-space-sm) var(--glass-space-lg);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--glass-radius-md);
  background: var(--glass-white-800);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  color: inherit;
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  transition: all var(--glass-duration-normal) var(--glass-ease-out);
  box-shadow: var(--glass-shadow-sm);
  overflow: hidden;
}

.glass-btn:hover {
  background: var(--glass-white-700);
  border-color: var(--glass-border-medium);
  box-shadow: var(--glass-shadow-md);
  transform: translateY(-1px);
}

.glass-btn:active {
  transform: translateY(0);
  box-shadow: var(--glass-shadow-sm);
}

.glass-btn:focus {
  outline: none;
  box-shadow: var(--glass-shadow-sm), 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.glass-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Dark Theme */
[data-theme="dark"] .glass-btn {
  background: var(--glass-black-800);
  border-color: var(--glass-border-dark);
}

[data-theme="dark"] .glass-btn:hover {
  background: var(--glass-black-700);
  border-color: var(--glass-border-dark-medium);
}

/* Button Variants */
.glass-btn-primary {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  color: rgb(59, 130, 246);
}

.glass-btn-primary:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.4);
}

.glass-btn-secondary {
  background: var(--glass-white-900);
  border-color: var(--glass-border-light);
}

.glass-btn-secondary:hover {
  background: var(--glass-white-800);
  border-color: var(--glass-border-medium);
}

.glass-btn-success {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
  color: rgb(34, 197, 94);
}

.glass-btn-success:hover {
  background: rgba(34, 197, 94, 0.3);
  border-color: rgba(34, 197, 94, 0.4);
}

.glass-btn-warning {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.3);
  color: rgb(245, 158, 11);
}

.glass-btn-warning:hover {
  background: rgba(245, 158, 11, 0.3);
  border-color: rgba(245, 158, 11, 0.4);
}

.glass-btn-danger {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: rgb(239, 68, 68);
}

.glass-btn-danger:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.4);
}

.glass-btn-ghost {
  background: transparent;
  border-color: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  box-shadow: none;
}

.glass-btn-ghost:hover {
  background: var(--glass-white-900);
  border-color: var(--glass-border-light);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
  box-shadow: var(--glass-shadow-sm);
}

/* Button Sizes */
.glass-btn-xs {
  padding: var(--glass-space-xs) var(--glass-space-sm);
  font-size: 0.75rem;
  border-radius: var(--glass-radius-sm);
}

.glass-btn-sm {
  padding: var(--glass-space-xs) var(--glass-space-md);
  font-size: 0.8125rem;
  border-radius: var(--glass-radius-sm);
}

.glass-btn-lg {
  padding: var(--glass-space-md) var(--glass-space-xl);
  font-size: 1rem;
  border-radius: var(--glass-radius-lg);
}

.glass-btn-xl {
  padding: var(--glass-space-lg) var(--glass-space-2xl);
  font-size: 1.125rem;
  border-radius: var(--glass-radius-xl);
}

/* Button Shapes */
.glass-btn-square {
  aspect-ratio: 1;
  padding: var(--glass-space-sm);
}

.glass-btn-circle {
  aspect-ratio: 1;
  padding: var(--glass-space-sm);
  border-radius: var(--glass-radius-full);
}

.glass-btn-pill {
  border-radius: var(--glass-radius-full);
}

/* Button Groups */
.glass-btn-group {
  display: inline-flex;
  border-radius: var(--glass-radius-md);
  overflow: hidden;
  box-shadow: var(--glass-shadow-sm);
}

.glass-btn-group .glass-btn {
  border-radius: 0;
  border-right-width: 0;
  box-shadow: none;
}

.glass-btn-group .glass-btn:first-child {
  border-top-left-radius: var(--glass-radius-md);
  border-bottom-left-radius: var(--glass-radius-md);
}

.glass-btn-group .glass-btn:last-child {
  border-top-right-radius: var(--glass-radius-md);
  border-bottom-right-radius: var(--glass-radius-md);
  border-right-width: 1px;
}

.glass-btn-group .glass-btn:hover {
  z-index: 1;
  border-right-width: 1px;
}

/* Button with Icon */
.glass-btn-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--glass-space-xs);
}

.glass-btn-icon-only {
  padding: var(--glass-space-sm);
}

.glass-btn .icon {
  width: 1em;
  height: 1em;
  flex-shrink: 0;
}

/* Loading State */
.glass-btn-loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.glass-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1em;
  height: 1em;
  margin: -0.5em 0 0 -0.5em;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: glass-btn-spin 1s linear infinite;
}

@keyframes glass-btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Action Button */
.glass-fab {
  position: fixed;
  bottom: var(--glass-space-xl);
  right: var(--glass-space-xl);
  width: 56px;
  height: 56px;
  border-radius: var(--glass-radius-full);
  background: var(--glass-white-700);
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
  border: 1px solid var(--glass-border-medium);
  box-shadow: var(--glass-shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--glass-duration-normal) var(--glass-ease-out);
  z-index: var(--glass-z-overlay);
}

.glass-fab:hover {
  background: var(--glass-white-600);
  box-shadow: var(--glass-shadow-xl);
  transform: scale(1.1);
}

.glass-fab:active {
  transform: scale(1.05);
}

/* Button Animations */
.glass-btn-animate-pulse {
  animation: glass-btn-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes glass-btn-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.glass-btn-animate-bounce {
  animation: glass-btn-bounce 1s infinite;
}

@keyframes glass-btn-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Responsive Buttons */
@media (max-width: 768px) {
  .glass-btn {
    padding: var(--glass-space-md) var(--glass-space-lg);
    font-size: 1rem;
  }
  
  .glass-fab {
    bottom: var(--glass-space-lg);
    right: var(--glass-space-lg);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .glass-btn,
  .glass-fab {
    transition: none;
    animation: none;
  }
  
  .glass-btn:hover,
  .glass-fab:hover {
    transform: none;
  }
}
