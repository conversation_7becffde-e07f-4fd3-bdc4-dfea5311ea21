/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,js,ts,jsx,tsx}",
    "./docs/**/*.{html,js,ts,jsx,tsx}",
    "./examples/**/*.{html,js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        glass: {
          50: 'rgba(255, 255, 255, 0.9)',
          100: 'rgba(255, 255, 255, 0.8)',
          200: 'rgba(255, 255, 255, 0.7)',
          300: 'rgba(255, 255, 255, 0.6)',
          400: 'rgba(255, 255, 255, 0.5)',
          500: 'rgba(255, 255, 255, 0.4)',
          600: 'rgba(255, 255, 255, 0.3)',
          700: 'rgba(255, 255, 255, 0.2)',
          800: 'rgba(255, 255, 255, 0.1)',
          900: 'rgba(255, 255, 255, 0.05)',
        },
        'glass-dark': {
          50: 'rgba(0, 0, 0, 0.9)',
          100: 'rgba(0, 0, 0, 0.8)',
          200: 'rgba(0, 0, 0, 0.7)',
          300: 'rgba(0, 0, 0, 0.6)',
          400: 'rgba(0, 0, 0, 0.5)',
          500: 'rgba(0, 0, 0, 0.4)',
          600: 'rgba(0, 0, 0, 0.3)',
          700: 'rgba(0, 0, 0, 0.2)',
          800: 'rgba(0, 0, 0, 0.1)',
          900: 'rgba(0, 0, 0, 0.05)',
        }
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '24px',
        '3xl': '32px',
      },
      animation: {
        'glass-shimmer': 'glass-shimmer 2s ease-in-out infinite',
        'glass-pulse': 'glass-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glass-float': 'glass-float 3s ease-in-out infinite',
      },
      keyframes: {
        'glass-shimmer': {
          '0%, 100%': { 
            backgroundPosition: '-200% 0',
            opacity: '0.5'
          },
          '50%': { 
            backgroundPosition: '200% 0',
            opacity: '0.8'
          }
        },
        'glass-pulse': {
          '0%, 100%': { 
            backdropFilter: 'blur(8px)',
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
          },
          '50%': { 
            backdropFilter: 'blur(12px)',
            backgroundColor: 'rgba(255, 255, 255, 0.2)'
          }
        },
        'glass-float': {
          '0%, 100%': { 
            transform: 'translateY(0px) scale(1)',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
          },
          '50%': { 
            transform: 'translateY(-5px) scale(1.02)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
          }
        }
      },
      boxShadow: {
        'glass': '0 8px 32px rgba(0, 0, 0, 0.1)',
        'glass-lg': '0 16px 64px rgba(0, 0, 0, 0.15)',
        'glass-xl': '0 24px 96px rgba(0, 0, 0, 0.2)',
        'glass-inner': 'inset 0 1px 0 rgba(255, 255, 255, 0.2)',
      }
    },
  },
  plugins: [
    function({ addUtilities, theme }) {
      const glassUtilities = {
        '.glass': {
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(8px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.glass-dark': {
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(8px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-strong': {
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
        },
        '.glass-subtle': {
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(4px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        }
      }
      addUtilities(glassUtilities)
    }
  ],
}
