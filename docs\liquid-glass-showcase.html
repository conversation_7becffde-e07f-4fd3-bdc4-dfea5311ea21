<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass Showcase - Apple Liquid Glass UI</title>
    <link rel="stylesheet" href="../src/css/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1551384963-cccb0b7ed94b?q=80&w=3247&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') center/cover;
            min-height: 100vh;
            margin: 0;
            padding: 2rem 0;
            animation: bg-move 10s ease-in-out infinite alternate;
            filter: saturate(140%);
        }
        
        @keyframes bg-move {
            from { background-position: center center; }
            to { background-position: center top; }
        }
        
        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--glass-space-large);
            display: grid;
            gap: var(--glass-space-extra-large);
        }
        
        .showcase-title {
            text-align: center;
            color: white;
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            margin: 0 0 var(--glass-space-large) 0;
            text-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--glass-space-extra-large);
        }
        
        /* iPhone-style Notification */
        .iphone-notification {
            width: 350px;
            height: 700px;
            border-radius: 60px;
            background: url('https://cdn.pixabay.com/photo/2020/05/27/22/18/meadow-5229169_1280.jpg') center/cover;
            position: relative;
            filter: saturate(140%);
            margin: 0 auto;
        }
        
        .notification-glass {
            position: absolute;
            left: 15px;
            bottom: 255px;
            width: 320px;
            height: 70px;
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            backdrop-filter: blur(4px);
            border: 2px solid rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            padding: 0 15px;
            gap: 15px;
        }
        
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .notification-text h4 {
            margin: 0;
            color: white;
            font-weight: 700;
            font-size: 0.9rem;
        }
        
        .notification-text p {
            margin: 4px 0 0;
            color: #eee;
            font-size: 0.8rem;
        }
        
        .player-glass {
            position: absolute;
            left: 15px;
            bottom: 95px;
            width: 320px;
            height: 150px;
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            backdrop-filter: blur(4px);
            border: 2px solid rgba(255,255,255,0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .player-info {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .player-info h4 {
            margin: 0;
            color: white;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .player-info p {
            margin: 4px 0 0;
            color: #eee;
            font-size: 0.9rem;
        }
        
        .player-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            color: white;
            font-size: 24px;
        }
        
        .time-display {
            position: absolute;
            top: 70px;
            width: 100%;
            text-align: center;
            color: white;
        }
        
        .time-display .date {
            font-size: 20px;
            font-weight: 500;
        }
        
        .time-display .time {
            font-size: 80px;
            font-weight: 700;
            margin-top: -10px;
        }
        
        /* Advanced Glass Cards */
        .advanced-glass-card {
            background: var(--glass-material-regular);
            backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
            border: var(--glass-border-regular);
            border-radius: var(--glass-radius-extra-large);
            box-shadow: var(--glass-shadow-heavy);
            padding: var(--glass-space-extra-large);
            position: relative;
            overflow: hidden;
            transition: all var(--glass-duration-normal) var(--glass-ease-spring);
        }
        
        .advanced-glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: var(--glass-highlight-top);
            pointer-events: none;
            z-index: 1;
        }
        
        .advanced-glass-card > * {
            position: relative;
            z-index: 2;
        }
        
        .advanced-glass-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: var(--glass-shadow-ultra-heavy);
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.9);
            margin: 0 0 var(--glass-space-medium) 0;
        }
        
        .card-description {
            font-size: 1rem;
            color: rgba(0, 0, 0, 0.7);
            line-height: 1.6;
            margin: 0 0 var(--glass-space-large) 0;
        }
        
        /* Liquid Glass Button */
        .liquid-glass-btn {
            background: var(--glass-material-thick);
            backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
            border: var(--glass-border-regular);
            border-radius: var(--glass-radius-pill);
            padding: var(--glass-space-medium) var(--glass-space-large);
            color: rgba(0, 0, 0, 0.9);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--glass-duration-fast) var(--glass-ease-spring);
            position: relative;
            overflow: hidden;
        }
        
        .liquid-glass-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
            opacity: 0;
            transition: opacity var(--glass-duration-fast) ease;
        }
        
        .liquid-glass-btn:hover::before {
            opacity: 1;
        }
        
        .liquid-glass-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--glass-shadow-thick);
        }
        
        .liquid-glass-btn:active {
            transform: translateY(0) scale(0.98);
        }
        
        @media (max-width: 768px) {
            .showcase-grid {
                grid-template-columns: 1fr;
            }
            
            .iphone-notification {
                width: 300px;
                height: 600px;
                border-radius: 50px;
            }
            
            .notification-glass,
            .player-glass {
                width: 270px;
                left: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- SVG Filters -->
    <svg style="position: absolute; width: 0; height: 0; pointer-events: none;">
        <defs>
            <filter id="liquid-distortion" x="0%" y="0%" width="100%" height="100%">
                <feTurbulence type="fractalNoise" baseFrequency="0.008 0.008" numOctaves="2" seed="92" result="noise" />
                <feGaussianBlur in="noise" stdDeviation="2" result="blurred" />
                <feDisplacementMap in="SourceGraphic" in2="blurred" scale="70" xChannelSelector="R" yChannelSelector="G" />
            </filter>
        </defs>
    </svg>

    <div class="showcase-container">
        <h1 class="showcase-title">Liquid Glass Showcase</h1>
        
        <div class="showcase-grid">
            <!-- iPhone-style Interface -->
            <div class="advanced-glass-card">
                <h2 class="card-title">iPhone-Style Interface</h2>
                <p class="card-description">Authentic iOS-style notifications and music player with liquid glass effects.</p>
                
                <div class="iphone-notification">
                    <div class="time-display">
                        <div class="date">Monday 9 June</div>
                        <div class="time">11:14</div>
                    </div>
                    
                    <div class="notification-glass">
                        <div class="notification-icon">📧</div>
                        <div class="notification-text">
                            <h4>Liquid Glass UI</h4>
                            <p>New showcase available...</p>
                        </div>
                    </div>
                    
                    <div class="player-glass">
                        <div class="player-info">
                            <h4>Liquid Dreams</h4>
                            <p>Glass UI Collection</p>
                        </div>
                        <div class="player-controls">
                            <span>⏮</span>
                            <span>⏸</span>
                            <span>⏭</span>
                        </div>
                    </div>
                </div>
                
                <button class="liquid-glass-btn">Explore Interface</button>
            </div>
            
            <!-- Advanced Glass Effects -->
            <div class="advanced-glass-card">
                <h2 class="card-title">Advanced Glass Effects</h2>
                <p class="card-description">Multi-layered glass system with SVG distortion filters and dynamic animations.</p>
                
                <div class="glass-container" style="min-height: 200px; margin: var(--glass-space-large) 0;">
                    <div class="glass-filter" style="filter: url(#liquid-distortion);"></div>
                    <div class="glass-overlay"></div>
                    <div class="glass-specular"></div>
                    <div class="glass-content" style="flex-direction: column; text-align: center;">
                        <h3 style="margin: 0; color: white; text-shadow: 0 0 3px rgba(0,0,0,0.5);">Layered Glass</h3>
                        <p style="margin: 8px 0 0; color: rgba(255,255,255,0.8);">With SVG distortion filters</p>
                        <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                            <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; backdrop-filter: blur(4px);"></div>
                            <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; backdrop-filter: blur(4px);"></div>
                            <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; backdrop-filter: blur(4px);"></div>
                        </div>
                    </div>
                </div>
                
                <button class="liquid-glass-btn">View Effects</button>
            </div>
            
            <!-- Interactive Components -->
            <div class="advanced-glass-card">
                <h2 class="card-title">Interactive Components</h2>
                <p class="card-description">Responsive glass components with hover effects and smooth animations.</p>
                
                <div style="display: grid; gap: var(--glass-space-medium); margin: var(--glass-space-large) 0;">
                    <button class="glass-btn glass-btn-primary">Primary Action</button>
                    <button class="glass-btn glass-btn-secondary">Secondary Action</button>
                    
                    <div class="glass-toggle-group">
                        <span class="glass-toggle-label">Enable Liquid Effects</span>
                        <input type="checkbox" class="glass-toggle" checked>
                    </div>
                    
                    <div class="glass-range-group">
                        <label class="glass-label">Distortion Level</label>
                        <input type="range" class="glass-range" min="0" max="100" value="70">
                        <div class="glass-range-value">70%</div>
                    </div>
                </div>
                
                <button class="liquid-glass-btn">Try Components</button>
            </div>
            
            <!-- Documentation Link -->
            <div class="advanced-glass-card">
                <h2 class="card-title">Complete Documentation</h2>
                <p class="card-description">Explore the full component library, design system, and implementation guides.</p>
                
                <div style="display: flex; gap: var(--glass-space-medium); margin: var(--glass-space-large) 0; flex-wrap: wrap;">
                    <span class="glass-badge glass-badge-primary">Components</span>
                    <span class="glass-badge glass-badge-success">Mobile-First</span>
                    <span class="glass-badge glass-badge-warning">Interactive</span>
                    <span class="glass-badge">Apple Design</span>
                </div>
                
                <div style="display: flex; gap: var(--glass-space-medium); flex-wrap: wrap;">
                    <a href="index.html" class="liquid-glass-btn">Documentation</a>
                    <a href="complete-demo.html" class="liquid-glass-btn">Full Demo</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced parallax and interaction effects
        document.addEventListener('mousemove', (e) => {
            const xPercent = (e.clientX / window.innerWidth - 0.5) * 15;
            const yPercent = (e.clientY / window.innerHeight - 0.5) * 15;
            
            // Background parallax
            document.body.style.backgroundPosition = `calc(50% + ${xPercent}px) calc(50% + ${yPercent}px)`;
            
            // iPhone parallax
            const iphone = document.querySelector('.iphone-notification');
            if (iphone) {
                iphone.style.backgroundPosition = `calc(50% + ${xPercent * 0.5}px) calc(50% + ${yPercent * 0.5}px)`;
            }
            
            // Glass container 3D effects
            const glassContainers = document.querySelectorAll('.glass-container, .advanced-glass-card');
            glassContainers.forEach(container => {
                const rect = container.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const deltaX = (e.clientX - centerX) / rect.width;
                const deltaY = (e.clientY - centerY) / rect.height;
                
                const tiltX = deltaY * 3;
                const tiltY = deltaX * -3;
                
                container.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg) translateZ(0)`;
            });
        });
        
        // Reset on mouse leave
        document.addEventListener('mouseleave', () => {
            const elements = document.querySelectorAll('.glass-container, .advanced-glass-card');
            elements.forEach(el => {
                el.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0)';
            });
        });
        
        // Animate SVG filters
        function animateFilters() {
            const turbulence = document.querySelector('feTurbulence');
            if (turbulence) {
                const time = Date.now() * 0.0001;
                const baseFreq = 0.008 + Math.sin(time) * 0.002;
                turbulence.setAttribute('baseFrequency', `${baseFreq} ${baseFreq}`);
            }
            requestAnimationFrame(animateFilters);
        }
        
        animateFilters();
    </script>
</body>
</html>
