/**
 * Glass Button Component
 * Enhanced button with glass effects and interactions
 */

export class GlassButton {
  constructor(element, options = {}) {
    this.element = element
    this.options = {
      enableRipple: true,
      enableHover: true,
      enableFocus: true,
      enableLoading: true,
      rippleColor: 'rgba(255, 255, 255, 0.3)',
      ...options
    }

    this.isLoading = false
    this.ripples = new Set()
    
    this.init()
  }

  /**
   * Initialize button component
   */
  init() {
    this.setupElement()
    this.bindEvents()
    this.setupAccessibility()
  }

  /**
   * Setup button element
   */
  setupElement() {
    // Add base glass button class if not present
    if (!this.element.classList.contains('glass-btn')) {
      this.element.classList.add('glass-btn')
    }

    // Add component identifier
    this.element.setAttribute('data-glass-button', 'initialized')

    // Setup initial state
    this.updateState()
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Click events
    this.element.addEventListener('click', this.handleClick.bind(this))
    
    // Mouse events for hover effects
    if (this.options.enableHover) {
      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))
    }

    // Focus events
    if (this.options.enableFocus) {
      this.element.addEventListener('focus', this.handleFocus.bind(this))
      this.element.addEventListener('blur', this.handleBlur.bind(this))
    }

    // Keyboard events
    this.element.addEventListener('keydown', this.handleKeyDown.bind(this))
  }

  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    // Ensure button has proper role
    if (!this.element.getAttribute('role') && this.element.tagName !== 'BUTTON') {
      this.element.setAttribute('role', 'button')
    }

    // Ensure button is focusable
    if (!this.element.hasAttribute('tabindex') && this.element.tagName !== 'BUTTON') {
      this.element.setAttribute('tabindex', '0')
    }

    // Add ARIA attributes for loading state
    if (this.options.enableLoading) {
      this.element.setAttribute('aria-busy', 'false')
    }
  }

  /**
   * Handle click events
   */
  handleClick(event) {
    if (this.isLoading || this.element.disabled) {
      event.preventDefault()
      return
    }

    // Create ripple effect
    if (this.options.enableRipple) {
      this.createRipple(event)
    }

    // Dispatch custom event
    this.dispatchEvent('glass-button-click', { originalEvent: event })
  }

  /**
   * Handle mouse enter
   */
  handleMouseEnter(event) {
    this.element.classList.add('glass-btn-hover')
    this.dispatchEvent('glass-button-hover', { state: 'enter', originalEvent: event })
  }

  /**
   * Handle mouse leave
   */
  handleMouseLeave(event) {
    this.element.classList.remove('glass-btn-hover')
    this.dispatchEvent('glass-button-hover', { state: 'leave', originalEvent: event })
  }

  /**
   * Handle focus
   */
  handleFocus(event) {
    this.element.classList.add('glass-btn-focus')
    this.dispatchEvent('glass-button-focus', { state: 'focus', originalEvent: event })
  }

  /**
   * Handle blur
   */
  handleBlur(event) {
    this.element.classList.remove('glass-btn-focus')
    this.dispatchEvent('glass-button-focus', { state: 'blur', originalEvent: event })
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event) {
    // Activate button with Enter or Space
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      this.element.click()
    }
  }

  /**
   * Create ripple effect
   */
  createRipple(event) {
    const rect = this.element.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const ripple = document.createElement('div')
    ripple.className = 'glass-btn-ripple'
    ripple.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: ${this.options.rippleColor};
      transform: translate(-50%, -50%);
      animation: glass-btn-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1;
    `

    // Ensure button has relative positioning
    if (getComputedStyle(this.element).position === 'static') {
      this.element.style.position = 'relative'
    }

    this.element.appendChild(ripple)
    this.ripples.add(ripple)

    // Remove ripple after animation
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
      this.ripples.delete(ripple)
    }, 600)
  }

  /**
   * Set loading state
   */
  setLoading(loading = true) {
    this.isLoading = loading
    
    if (loading) {
      this.element.classList.add('glass-btn-loading')
      this.element.setAttribute('aria-busy', 'true')
      this.element.disabled = true
    } else {
      this.element.classList.remove('glass-btn-loading')
      this.element.setAttribute('aria-busy', 'false')
      this.element.disabled = false
    }

    this.updateState()
    this.dispatchEvent('glass-button-loading', { loading })
  }

  /**
   * Set disabled state
   */
  setDisabled(disabled = true) {
    this.element.disabled = disabled
    this.element.classList.toggle('glass-btn-disabled', disabled)
    this.updateState()
    this.dispatchEvent('glass-button-disabled', { disabled })
  }

  /**
   * Set button variant
   */
  setVariant(variant) {
    // Remove existing variant classes
    const variantClasses = [
      'glass-btn-primary',
      'glass-btn-secondary',
      'glass-btn-success',
      'glass-btn-warning',
      'glass-btn-danger',
      'glass-btn-ghost'
    ]
    
    this.element.classList.remove(...variantClasses)
    
    // Add new variant class
    if (variant && variant !== 'default') {
      this.element.classList.add(`glass-btn-${variant}`)
    }

    this.dispatchEvent('glass-button-variant', { variant })
  }

  /**
   * Set button size
   */
  setSize(size) {
    // Remove existing size classes
    const sizeClasses = ['glass-btn-xs', 'glass-btn-sm', 'glass-btn-lg', 'glass-btn-xl']
    this.element.classList.remove(...sizeClasses)
    
    // Add new size class
    if (size && size !== 'default') {
      this.element.classList.add(`glass-btn-${size}`)
    }

    this.dispatchEvent('glass-button-size', { size })
  }

  /**
   * Update button state
   */
  updateState() {
    const state = {
      loading: this.isLoading,
      disabled: this.element.disabled,
      focused: this.element.classList.contains('glass-btn-focus'),
      hovered: this.element.classList.contains('glass-btn-hover')
    }

    this.element.setAttribute('data-state', JSON.stringify(state))
  }

  /**
   * Dispatch custom event
   */
  dispatchEvent(eventName, detail = {}) {
    const event = new CustomEvent(eventName, {
      detail: {
        button: this,
        element: this.element,
        ...detail
      },
      bubbles: true,
      cancelable: true
    })

    this.element.dispatchEvent(event)
  }

  /**
   * Add animation
   */
  addAnimation(animationType, duration = 1000) {
    const animationClass = `glass-btn-animate-${animationType}`
    this.element.classList.add(animationClass)

    setTimeout(() => {
      this.element.classList.remove(animationClass)
    }, duration)
  }

  /**
   * Update options
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
  }

  /**
   * Get button state
   */
  getState() {
    return {
      loading: this.isLoading,
      disabled: this.element.disabled,
      variant: this.getVariant(),
      size: this.getSize()
    }
  }

  /**
   * Get current variant
   */
  getVariant() {
    const variants = ['primary', 'secondary', 'success', 'warning', 'danger', 'ghost']
    return variants.find(variant => this.element.classList.contains(`glass-btn-${variant}`)) || 'default'
  }

  /**
   * Get current size
   */
  getSize() {
    const sizes = ['xs', 'sm', 'lg', 'xl']
    return sizes.find(size => this.element.classList.contains(`glass-btn-${size}`)) || 'default'
  }

  /**
   * Destroy button component
   */
  destroy() {
    // Remove all ripples
    this.ripples.forEach(ripple => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
    })
    this.ripples.clear()

    // Remove event listeners (they'll be garbage collected with the element)
    
    // Remove component identifier
    this.element.removeAttribute('data-glass-button')
    
    // Remove state classes
    this.element.classList.remove('glass-btn-hover', 'glass-btn-focus', 'glass-btn-loading', 'glass-btn-disabled')
  }
}
