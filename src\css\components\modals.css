/* Apple Liquid Glass UI - Modal Components */

/* Modal Overlay */
.glass-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-material-heavy);
  backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  z-index: var(--glass-z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--glass-space-large);
  opacity: 0;
  visibility: hidden;
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
}

.glass-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Modal Container */
.glass-modal {
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-extra-large);
  box-shadow: var(--glass-shadow-heavy);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  transform: scale(0.9) translateY(20px);
  transition: all var(--glass-duration-normal) var(--glass-ease-spring);
}

.glass-modal-overlay.active .glass-modal {
  transform: scale(1) translateY(0);
}

/* Modal Reflection */
.glass-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

/* Modal Header */
.glass-modal-header {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--glass-space-large);
  border-bottom: var(--glass-border-ultra-thin);
}

.glass-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  margin: 0;
  letter-spacing: -0.02em;
}

.glass-modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--glass-material-thin);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  color: rgba(0, 0, 0, 0.6);
}

.glass-modal-close:hover {
  background: var(--glass-material-thick);
  transform: scale(1.05);
  color: rgba(0, 0, 0, 0.8);
}

.glass-modal-close:active {
  transform: scale(0.95);
}

/* Modal Body */
.glass-modal-body {
  position: relative;
  z-index: 2;
  padding: var(--glass-space-large);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.glass-modal-body::-webkit-scrollbar {
  width: 6px;
}

.glass-modal-body::-webkit-scrollbar-track {
  background: transparent;
}

.glass-modal-body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.glass-modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Modal Footer */
.glass-modal-footer {
  position: relative;
  z-index: 2;
  display: flex;
  gap: var(--glass-space-medium);
  justify-content: flex-end;
  padding: var(--glass-space-large);
  border-top: var(--glass-border-ultra-thin);
}

/* Alert Modal */
.glass-alert {
  max-width: 400px;
  text-align: center;
}

.glass-alert-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--glass-space-large);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.glass-alert-icon.success {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.glass-alert-icon.warning {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.glass-alert-icon.error {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.glass-alert-icon.info {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.glass-alert-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  margin: 0 0 var(--glass-space-medium) 0;
}

.glass-alert-message {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.5;
  margin: 0 0 var(--glass-space-large) 0;
}

/* Drawer/Sheet */
.glass-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-material-heavy);
  backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  z-index: var(--glass-z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
}

.glass-drawer-overlay.active {
  opacity: 1;
  visibility: visible;
}

.glass-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-regular);
  border-bottom: none;
  border-radius: var(--glass-radius-extra-large) var(--glass-radius-extra-large) 0 0;
  box-shadow: var(--glass-shadow-heavy);
  max-height: 90vh;
  transform: translateY(100%);
  transition: all var(--glass-duration-normal) var(--glass-ease-spring);
  overflow: hidden;
}

.glass-drawer-overlay.active .glass-drawer {
  transform: translateY(0);
}

.glass-drawer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-drawer-handle {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: var(--glass-space-medium);
  cursor: grab;
}

.glass-drawer-handle::before {
  content: '';
  width: 40px;
  height: 4px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.glass-drawer-content {
  position: relative;
  z-index: 2;
  padding: 0 var(--glass-space-large) calc(var(--glass-space-large) + env(safe-area-inset-bottom));
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

/* Popover */
.glass-popover {
  position: absolute;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-large);
  box-shadow: var(--glass-shadow-heavy);
  padding: var(--glass-space-large);
  z-index: var(--glass-z-popover);
  opacity: 0;
  visibility: hidden;
  transform: scale(0.9) translateY(10px);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  max-width: 300px;
  overflow: hidden;
}

.glass-popover.active {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
}

.glass-popover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-popover-content {
  position: relative;
  z-index: 2;
}

/* Tooltip */
.glass-tooltip {
  position: absolute;
  background: var(--glass-material-heavy);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-ultra-thin);
  border-radius: var(--glass-radius-medium);
  padding: var(--glass-space-small) var(--glass-space-medium);
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.9);
  z-index: var(--glass-z-tooltip);
  opacity: 0;
  visibility: hidden;
  transform: translateY(5px);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  pointer-events: none;
  white-space: nowrap;
}

.glass-tooltip.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .glass-modal {
    margin: var(--glass-space-medium);
    max-width: none;
    width: calc(100% - var(--glass-space-large) * 2);
  }
  
  .glass-modal-footer {
    flex-direction: column;
  }
  
  .glass-popover {
    max-width: calc(100vw - var(--glass-space-large) * 2);
  }
}
