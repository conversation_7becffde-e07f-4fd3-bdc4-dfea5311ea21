# Liquid Glass UI - API Documentation

## Table of Contents

- [Core Framework](#core-framework)
- [Components](#components)
- [Effects](#effects)
- [Utilities](#utilities)
- [CSS Classes](#css-classes)
- [Events](#events)

## Core Framework

### LiquidGlassUI

The main framework class that initializes and manages all glass effects and components.

#### Constructor

```javascript
new LiquidGlassUI(options)
```

**Parameters:**
- `options` (Object): Configuration options
  - `enableCursorEffects` (Boolean): Enable cursor-based effects for desktop (default: `true`)
  - `enableMotionEffects` (Boolean): Enable device motion effects for mobile (default: `true`)
  - `enableAnimations` (Boolean): Enable animations (default: `true`)
  - `theme` (String): Theme mode - `'light'`, `'dark'`, or `'auto'` (default: `'auto'`)

#### Methods

##### `setTheme(theme)`
Changes the current theme.

**Parameters:**
- `theme` (String): `'light'`, `'dark'`, or `'auto'`

##### `destroy()`
Destroys the framework instance and cleans up all effects.

#### Example

```javascript
const ui = new LiquidGlassUI({
    enableCursorEffects: true,
    enableMotionEffects: true,
    enableAnimations: true,
    theme: 'auto'
});

// Change theme
ui.setTheme('dark');

// Clean up
ui.destroy();
```

## Components

### GlassButton

Enhanced button component with glass effects and interactions.

#### Constructor

```javascript
new GlassButton(element, options)
```

**Parameters:**
- `element` (HTMLElement): The button element
- `options` (Object): Configuration options
  - `enableRipple` (Boolean): Enable ripple effect (default: `true`)
  - `enableHover` (Boolean): Enable hover effects (default: `true`)
  - `enableFocus` (Boolean): Enable focus effects (default: `true`)
  - `rippleColor` (String): Ripple effect color (default: `'rgba(255, 255, 255, 0.3)'`)

#### Methods

##### `setLoading(loading)`
Sets the loading state of the button.

**Parameters:**
- `loading` (Boolean): Loading state

##### `setDisabled(disabled)`
Sets the disabled state of the button.

**Parameters:**
- `disabled` (Boolean): Disabled state

##### `setVariant(variant)`
Changes the button variant.

**Parameters:**
- `variant` (String): `'primary'`, `'secondary'`, `'success'`, `'warning'`, `'danger'`, `'ghost'`

##### `setSize(size)`
Changes the button size.

**Parameters:**
- `size` (String): `'xs'`, `'sm'`, `'lg'`, `'xl'`

#### Events

- `glass-button-click`: Fired when button is clicked
- `glass-button-hover`: Fired on hover state change
- `glass-button-focus`: Fired on focus state change
- `glass-button-loading`: Fired when loading state changes

#### Example

```javascript
const button = new GlassButton(document.querySelector('.my-button'), {
    enableRipple: true,
    rippleColor: 'rgba(59, 130, 246, 0.3)'
});

button.setVariant('primary');
button.setLoading(true);

// Listen for events
button.element.addEventListener('glass-button-click', (event) => {
    console.log('Button clicked:', event.detail);
});
```

### GlassCard

Enhanced card component with glass effects and interactions.

#### Constructor

```javascript
new GlassCard(element, options)
```

**Parameters:**
- `element` (HTMLElement): The card element
- `options` (Object): Configuration options
  - `enableHover` (Boolean): Enable hover effects (default: `true`)
  - `enableClick` (Boolean): Enable click effects for interactive cards (default: `true`)
  - `hoverEffect` (String): Hover effect type - `'lift'`, `'glow'`, `'scale'` (default: `'lift'`)

#### Methods

##### `setLoading(loading)`
Sets the loading state of the card.

##### `setVariant(variant)`
Changes the card variant.

**Parameters:**
- `variant` (String): `'elevated'`, `'subtle'`, `'outlined'`, `'filled'`

##### `addBadge(text, options)`
Adds a badge to the card.

**Parameters:**
- `text` (String): Badge text
- `options` (Object): Badge options
  - `position` (String): Badge position (default: `'top-right'`)
  - `variant` (String): Badge variant (default: `'default'`)

##### `removeBadge()`
Removes the badge from the card.

##### `updateContent(content)`
Updates the card content.

**Parameters:**
- `content` (String|HTMLElement): New content

#### Events

- `glass-card-click`: Fired when interactive card is clicked
- `glass-card-hover`: Fired on hover state change
- `glass-card-loading`: Fired when loading state changes

## Effects

### CursorEffects

Manages cursor-based effects for desktop users.

#### Constructor

```javascript
new CursorEffects(options)
```

**Parameters:**
- `options` (Object): Configuration options
  - `enableShadows` (Boolean): Enable shadow effects (default: `true`)
  - `enableLighting` (Boolean): Enable lighting effects (default: `true`)
  - `shadowIntensity` (Number): Shadow intensity (default: `0.3`)
  - `lightingIntensity` (Number): Lighting intensity (default: `0.2`)
  - `maxDistance` (Number): Maximum effect distance (default: `200`)

#### Methods

##### `init()`
Initializes cursor effects.

##### `addElement(element)`
Adds an element to cursor effects.

##### `removeElement(element)`
Removes an element from cursor effects.

##### `destroy()`
Destroys cursor effects and cleans up.

### MotionEffects

Manages device motion effects for mobile devices.

#### Constructor

```javascript
new MotionEffects(options)
```

**Parameters:**
- `options` (Object): Configuration options
  - `enableTilt` (Boolean): Enable tilt effects (default: `true`)
  - `enableParallax` (Boolean): Enable parallax effects (default: `true`)
  - `enableLighting` (Boolean): Enable lighting effects (default: `true`)
  - `sensitivity` (Number): Motion sensitivity (default: `1`)
  - `maxTilt` (Number): Maximum tilt angle (default: `15`)

#### Methods

##### `init()`
Initializes motion effects (requests permission on iOS).

##### `addElement(element)`
Adds an element to motion effects.

##### `requestPermission()`
Requests device orientation permission (iOS 13+).

##### `destroy()`
Destroys motion effects and cleans up.

## Utilities

### ThemeManager

Manages theme switching and system preferences.

#### Constructor

```javascript
new ThemeManager(initialTheme)
```

**Parameters:**
- `initialTheme` (String): Initial theme - `'light'`, `'dark'`, `'auto'` (default: `'auto'`)

#### Methods

##### `setTheme(theme)`
Sets the current theme.

##### `getEffectiveTheme()`
Returns the effective theme (resolves 'auto' to actual theme).

##### `toggleTheme()`
Toggles between light and dark themes.

##### `addListener(callback)`
Adds a theme change listener.

**Returns:** Function to remove the listener

### DeviceDetector

Detects device capabilities and features.

#### Methods

##### `isMobile()`
Returns `true` if device is mobile.

##### `isDesktop()`
Returns `true` if device is desktop.

##### `supportsBackdropFilter()`
Returns `true` if browser supports backdrop-filter.

##### `prefersReducedMotion()`
Returns `true` if user prefers reduced motion.

##### `getOptimalSettings()`
Returns optimal settings based on device capabilities.

## CSS Classes

### Glass Base Classes

- `.glass` - Basic glass effect
- `.glass-subtle` - Subtle glass effect
- `.glass-light` - Light glass effect
- `.glass-medium` - Medium glass effect
- `.glass-strong` - Strong glass effect
- `.glass-intense` - Intense glass effect

### Button Classes

- `.glass-btn` - Base button class
- `.glass-btn-primary` - Primary button variant
- `.glass-btn-secondary` - Secondary button variant
- `.glass-btn-success` - Success button variant
- `.glass-btn-warning` - Warning button variant
- `.glass-btn-danger` - Danger button variant
- `.glass-btn-ghost` - Ghost button variant

### Card Classes

- `.glass-card` - Base card class
- `.glass-card-elevated` - Elevated card variant
- `.glass-card-subtle` - Subtle card variant
- `.glass-card-outlined` - Outlined card variant
- `.glass-card-filled` - Filled card variant
- `.glass-card-interactive` - Interactive card

### Utility Classes

- `.backdrop-blur-*` - Backdrop blur utilities
- `.bg-glass-*` - Glass background utilities
- `.shadow-glass-*` - Glass shadow utilities
- `.transition-glass` - Glass transition utility

## Events

### Framework Events

#### `themechange`
Fired when theme changes.

**Detail:**
- `theme` (String): New theme
- `previousTheme` (String): Previous theme
- `isSystemTheme` (Boolean): Whether using system theme

### Component Events

All component events include:
- `element` (HTMLElement): The component element
- `component` (Object): The component instance
- `originalEvent` (Event): Original DOM event (if applicable)

#### Button Events
- `glass-button-click`
- `glass-button-hover`
- `glass-button-focus`
- `glass-button-loading`

#### Card Events
- `glass-card-click`
- `glass-card-hover`
- `glass-card-focus`
- `glass-card-loading`

### Example Event Handling

```javascript
// Listen for theme changes
document.addEventListener('themechange', (event) => {
    console.log('Theme changed to:', event.detail.theme);
});

// Listen for button clicks
document.addEventListener('glass-button-click', (event) => {
    const { button, element } = event.detail;
    console.log('Button clicked:', button.getState());
});
```
