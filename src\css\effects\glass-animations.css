/* Liquid Glass UI - Glass Animations */

/* Keyframe Definitions */
@keyframes glass-shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0.5;
  }
  50% {
    background-position: 200% 0;
    opacity: 0.8;
  }
  100% {
    background-position: -200% 0;
    opacity: 0.5;
  }
}

@keyframes glass-pulse {
  0%, 100% {
    backdrop-filter: blur(var(--glass-blur-md));
    background-color: var(--glass-white-800);
    transform: scale(1);
  }
  50% {
    backdrop-filter: blur(var(--glass-blur-lg));
    background-color: var(--glass-white-700);
    transform: scale(1.02);
  }
}

@keyframes glass-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    box-shadow: var(--glass-shadow-md);
  }
  50% {
    transform: translateY(-5px) scale(1.01);
    box-shadow: var(--glass-shadow-lg);
  }
}

@keyframes glass-glow {
  0%, 100% {
    box-shadow: var(--glass-shadow-md), 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    box-shadow: var(--glass-shadow-lg), 0 0 20px rgba(255, 255, 255, 0.3);
  }
}

@keyframes glass-ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes glass-slide-in {
  0% {
    transform: translateY(20px);
    opacity: 0;
    backdrop-filter: blur(0);
  }
  100% {
    transform: translateY(0);
    opacity: 1;
    backdrop-filter: blur(var(--glass-blur-md));
  }
}

@keyframes glass-fade-in {
  0% {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(var(--glass-blur-md));
  }
}

@keyframes glass-scale-in {
  0% {
    transform: scale(0.9);
    opacity: 0;
    backdrop-filter: blur(0);
  }
  100% {
    transform: scale(1);
    opacity: 1;
    backdrop-filter: blur(var(--glass-blur-md));
  }
}

/* Animation Classes */
.glass-animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: glass-shimmer 2s ease-in-out infinite;
}

.glass-animate-pulse {
  animation: glass-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.glass-animate-float {
  animation: glass-float 3s ease-in-out infinite;
}

.glass-animate-glow {
  animation: glass-glow 2s ease-in-out infinite;
}

.glass-animate-slide-in {
  animation: glass-slide-in var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-animate-fade-in {
  animation: glass-fade-in var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-animate-scale-in {
  animation: glass-scale-in var(--glass-duration-normal) var(--glass-ease-out);
}

/* Hover Animations */
.glass-hover-lift {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out),
              box-shadow var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-lg);
}

.glass-hover-glow {
  transition: box-shadow var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-hover-glow:hover {
  box-shadow: var(--glass-shadow-md), 0 0 20px rgba(255, 255, 255, 0.2);
}

.glass-hover-blur {
  transition: backdrop-filter var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-hover-blur:hover {
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
}

.glass-hover-scale {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-hover-scale:hover {
  transform: scale(1.02);
}

.glass-hover-brighten {
  transition: background-color var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-hover-brighten:hover {
  background-color: var(--glass-white-700);
}

[data-theme="dark"] .glass-hover-brighten:hover {
  background-color: var(--glass-black-700);
}

/* Focus Animations */
.glass-focus-ring {
  transition: box-shadow var(--glass-duration-fast) var(--glass-ease-out);
}

.glass-focus-ring:focus {
  outline: none;
  box-shadow: var(--glass-shadow-md), 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Active State Animations */
.glass-active-press {
  transition: transform var(--glass-duration-fast) var(--glass-ease-out);
}

.glass-active-press:active {
  transform: scale(0.98);
}

/* Ripple Effect */
.glass-ripple {
  position: relative;
  overflow: hidden;
}

.glass-ripple::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: glass-ripple 0.6s linear;
  pointer-events: none;
}

/* Loading Animations */
.glass-loading {
  position: relative;
  overflow: hidden;
}

.glass-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: glass-shimmer 1.5s ease-in-out infinite;
}

/* Stagger Animations */
.glass-stagger-children > * {
  animation-delay: calc(var(--stagger-delay, 0.1s) * var(--index, 0));
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .glass-animate-shimmer,
  .glass-animate-pulse,
  .glass-animate-float,
  .glass-animate-glow,
  .glass-loading::before {
    animation: none;
  }
  
  .glass-hover-lift,
  .glass-hover-glow,
  .glass-hover-blur,
  .glass-hover-scale,
  .glass-hover-brighten,
  .glass-focus-ring,
  .glass-active-press {
    transition: none;
  }
}
