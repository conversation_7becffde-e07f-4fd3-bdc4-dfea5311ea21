/* Liquid Glass UI - Cursor Effects */

/* Cursor Effect Base Styles */
.has-cursor-effect {
  position: relative;
  transition: box-shadow var(--glass-duration-fast) var(--glass-ease-out);
}

/* Cursor Shadow Effects */
.has-cursor-effect {
  box-shadow: var(--glass-shadow-md), var(--cursor-shadow, 0 0 0 transparent);
}

/* Cursor Lighting Effects */
.has-cursor-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--cursor-light, transparent);
  border-radius: inherit;
  pointer-events: none;
  opacity: var(--cursor-intensity, 0);
  transition: opacity var(--glass-duration-fast) var(--glass-ease-out);
  z-index: 1;
}

.has-cursor-effect > * {
  position: relative;
  z-index: 2;
}

/* Cursor Ripple Animation */
@keyframes cursor-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

.cursor-ripple {
  animation: cursor-ripple 0.6s ease-out;
}

/* Cursor Hover States */
.has-cursor-effect:hover {
  transform: translateY(-1px);
}

/* Cursor Focus States */
.has-cursor-effect:focus {
  outline: none;
  box-shadow: var(--glass-shadow-md), 
              var(--cursor-shadow, 0 0 0 transparent),
              0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Cursor Active States */
.has-cursor-effect:active {
  transform: translateY(0);
}

/* Cursor Effect Variants */
.cursor-effect-subtle .has-cursor-effect {
  --cursor-shadow-intensity: 0.1;
  --cursor-light-intensity: 0.05;
}

.cursor-effect-strong .has-cursor-effect {
  --cursor-shadow-intensity: 0.5;
  --cursor-light-intensity: 0.3;
}

/* Cursor Glow Effect */
.cursor-glow {
  position: relative;
  overflow: hidden;
}

.cursor-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
  pointer-events: none;
  z-index: 1;
}

.cursor-glow:hover::after {
  transform: translate(-50%, -50%) scale(2);
}

/* Cursor Trail Effect */
.cursor-trail {
  position: relative;
}

.cursor-trail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  background-position: -100% -100%;
  border-radius: inherit;
  transition: background-position var(--glass-duration-slow) var(--glass-ease-out);
  pointer-events: none;
  z-index: 1;
}

.cursor-trail:hover::before {
  background-position: 100% 100%;
}

/* Cursor Magnetic Effect */
.cursor-magnetic {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

.cursor-magnetic:hover {
  transform: scale(1.05);
}

/* Cursor Tilt Effect */
.cursor-tilt {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
  transform-style: preserve-3d;
}

.cursor-tilt:hover {
  transform: perspective(1000px) 
             rotateX(calc(var(--cursor-y, 0.5) * 10deg - 5deg))
             rotateY(calc(var(--cursor-x, 0.5) * 10deg - 5deg));
}

/* Cursor Parallax Effect */
.cursor-parallax {
  position: relative;
  overflow: hidden;
}

.cursor-parallax > * {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

.cursor-parallax:hover > * {
  transform: translate(
    calc(var(--cursor-x, 0.5) * 10px - 5px),
    calc(var(--cursor-y, 0.5) * 10px - 5px)
  );
}

/* Cursor Depth Effect */
.cursor-depth {
  position: relative;
}

.cursor-depth::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: -2px;
  bottom: -2px;
  background: inherit;
  border-radius: inherit;
  filter: blur(4px);
  opacity: 0;
  transition: opacity var(--glass-duration-normal) var(--glass-ease-out);
  z-index: -1;
}

.cursor-depth:hover::before {
  opacity: 0.3;
}

/* Cursor Color Shift Effect */
.cursor-color-shift {
  transition: background-color var(--glass-duration-normal) var(--glass-ease-out),
              border-color var(--glass-duration-normal) var(--glass-ease-out);
}

.cursor-color-shift:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .cursor-color-shift:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Cursor Blur Effect */
.cursor-blur {
  transition: backdrop-filter var(--glass-duration-normal) var(--glass-ease-out);
}

.cursor-blur:hover {
  backdrop-filter: blur(calc(var(--glass-blur-md) * 1.5));
  -webkit-backdrop-filter: blur(calc(var(--glass-blur-md) * 1.5));
}

/* Cursor Scale Effect */
.cursor-scale {
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

.cursor-scale:hover {
  transform: scale(calc(1 + var(--cursor-intensity, 0) * 0.1));
}

/* Disable cursor effects on touch devices */
@media (hover: none) and (pointer: coarse) {
  .has-cursor-effect,
  .cursor-glow,
  .cursor-trail,
  .cursor-magnetic,
  .cursor-tilt,
  .cursor-parallax,
  .cursor-depth,
  .cursor-color-shift,
  .cursor-blur,
  .cursor-scale {
    transform: none !important;
    transition: none !important;
  }

  .has-cursor-effect::before,
  .cursor-glow::after,
  .cursor-trail::before,
  .cursor-depth::before {
    display: none !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .has-cursor-effect,
  .cursor-glow,
  .cursor-trail,
  .cursor-magnetic,
  .cursor-tilt,
  .cursor-parallax,
  .cursor-depth,
  .cursor-color-shift,
  .cursor-blur,
  .cursor-scale {
    transition: none !important;
    animation: none !important;
  }
}
