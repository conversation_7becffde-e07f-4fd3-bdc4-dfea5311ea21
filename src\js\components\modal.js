/**
 * Glass Modal Component
 * Enhanced modal with glass effects
 */

export class GlassModal {
  constructor(element, options = {}) {
    this.element = element
    this.options = {
      backdrop: true,
      keyboard: true,
      focus: true,
      ...options
    }

    this.isOpen = false
    this.init()
  }

  init() {
    this.element.setAttribute('data-glass-modal', 'initialized')
  }

  open() {
    this.isOpen = true
    this.element.classList.add('glass-modal-open')
  }

  close() {
    this.isOpen = false
    this.element.classList.remove('glass-modal-open')
  }

  destroy() {
    this.element.removeAttribute('data-glass-modal')
  }
}
