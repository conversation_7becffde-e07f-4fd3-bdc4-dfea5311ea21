/* Liquid Glass UI - Main CSS Entry Point */

/* Base Styles - Load First */
@import './base/variables.css';
@import './base/reset.css';

/* Tailwind CSS Base */
@tailwind base;

/* Core Glass Effects - Load Before Components */
@import './effects/glass-core.css';
@import './effects/glass-animations.css';

/* Utility Classes */
@import './utilities/glass-utilities.css';
@import './utilities/layout-utilities.css';

/* Interactive Effects */
@import './effects/cursor-effects.css';
@import './effects/motion-effects.css';

/* Component Styles */
@import './components/buttons.css';
@import './components/cards.css';
@import './components/navigation.css';
@import './components/forms.css';
@import './components/modals.css';
@import './components/utilities.css';

/* Tailwind Components and Utilities - Load Last */
@tailwind components;
@tailwind utilities;
