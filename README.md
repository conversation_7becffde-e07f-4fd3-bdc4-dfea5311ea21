# Liquid Glass UI

A comprehensive JavaScript/CSS UI framework that replicates Apple's "liquid glass" design aesthetic for web applications. Built with modern web technologies and optimized for performance across all devices.

## ✨ Features

- **🌟 Apple-inspired Design**: Authentic liquid glass aesthetic with translucent effects
- **🎯 Modern Architecture**: Built with ES6+ modules and CSS custom properties
- **📱 Responsive**: Mobile-first design with adaptive effects
- **⚡ Performance Optimized**: Automatic performance detection and optimization
- **🎨 Customizable**: Extensive theming and customization options
- **♿ Accessible**: WCAG compliant with full keyboard navigation
- **🌙 Dark Mode**: Built-in light/dark theme support
- **📦 Framework Agnostic**: Works with any JavaScript framework or vanilla HTML

## 🚀 Quick Start

### Installation

```bash
npm install liquid-glass-ui
```

### Basic Usage

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My App</title>
</head>
<body>
    <!-- Glass Button -->
    <button class="glass-btn glass-btn-primary" data-glass-button>
        Click Me
    </button>

    <!-- Glass Card -->
    <div class="glass-card" data-glass-card>
        <div class="glass-card-header">
            <h3 class="glass-card-title">Glass Card</h3>
        </div>
        <div class="glass-card-body">
            <p>Beautiful glass effect card with blur and transparency.</p>
        </div>
    </div>

    <!-- Initialize Framework -->
    <script type="module">
        import LiquidGlassUI from 'liquid-glass-ui';
        
        const ui = new LiquidGlassUI({
            enableCursorEffects: true,
            enableMotionEffects: true,
            theme: 'auto'
        });
    </script>
</body>
</html>
```

## 🎨 Components

### Buttons

```html
<!-- Basic Button -->
<button class="glass-btn">Default</button>

<!-- Button Variants -->
<button class="glass-btn glass-btn-primary">Primary</button>
<button class="glass-btn glass-btn-secondary">Secondary</button>
<button class="glass-btn glass-btn-success">Success</button>
<button class="glass-btn glass-btn-warning">Warning</button>
<button class="glass-btn glass-btn-danger">Danger</button>
<button class="glass-btn glass-btn-ghost">Ghost</button>

<!-- Button Sizes -->
<button class="glass-btn glass-btn-xs">Extra Small</button>
<button class="glass-btn glass-btn-sm">Small</button>
<button class="glass-btn">Default</button>
<button class="glass-btn glass-btn-lg">Large</button>
<button class="glass-btn glass-btn-xl">Extra Large</button>

<!-- Button Shapes -->
<button class="glass-btn glass-btn-circle">●</button>
<button class="glass-btn glass-btn-pill">Pill Button</button>
```

### Cards

```html
<!-- Basic Card -->
<div class="glass-card">
    <div class="glass-card-header">
        <h3 class="glass-card-title">Card Title</h3>
        <p class="glass-card-subtitle">Card subtitle</p>
    </div>
    <div class="glass-card-body">
        <p class="glass-card-text">Card content goes here.</p>
    </div>
    <div class="glass-card-footer">
        <div class="glass-card-actions">
            <button class="glass-btn glass-btn-ghost">Cancel</button>
            <button class="glass-btn glass-btn-primary">Action</button>
        </div>
    </div>
</div>

<!-- Card Variants -->
<div class="glass-card glass-card-elevated">Elevated Card</div>
<div class="glass-card glass-card-subtle">Subtle Card</div>
<div class="glass-card glass-card-outlined">Outlined Card</div>
<div class="glass-card glass-card-filled">Filled Card</div>

<!-- Interactive Card -->
<div class="glass-card glass-card-interactive" data-glass-card>
    <div class="glass-card-badge">New</div>
    <!-- Card content -->
</div>
```

## 🎛️ Configuration

### Framework Options

```javascript
const ui = new LiquidGlassUI({
    // Enable/disable cursor effects for desktop
    enableCursorEffects: true,
    
    // Enable/disable motion effects for mobile
    enableMotionEffects: true,
    
    // Enable/disable animations
    enableAnimations: true,
    
    // Theme: 'light', 'dark', 'auto'
    theme: 'auto'
});
```

### CSS Custom Properties

```css
:root {
    /* Glass blur levels */
    --glass-blur-xs: 2px;
    --glass-blur-sm: 4px;
    --glass-blur-md: 8px;
    --glass-blur-lg: 12px;
    --glass-blur-xl: 16px;
    
    /* Glass opacity levels */
    --glass-opacity-subtle: 0.05;
    --glass-opacity-light: 0.1;
    --glass-opacity-medium: 0.15;
    --glass-opacity-strong: 0.2;
    
    /* Animation durations */
    --glass-duration-fast: 150ms;
    --glass-duration-normal: 250ms;
    --glass-duration-slow: 350ms;
    
    /* Border radius */
    --glass-radius-sm: 0.375rem;
    --glass-radius-md: 0.5rem;
    --glass-radius-lg: 0.75rem;
    --glass-radius-xl: 1rem;
}
```

## 🌙 Theming

### Automatic Theme Detection

```javascript
// Auto-detect system preference
const ui = new LiquidGlassUI({ theme: 'auto' });

// Manual theme control
ui.setTheme('dark');
ui.setTheme('light');
```

### Theme Toggle Button

```html
<button data-theme-toggle class="glass-btn">
    Toggle Theme
</button>

<select data-theme-selector class="glass-btn">
    <option value="auto">Auto</option>
    <option value="light">Light</option>
    <option value="dark">Dark</option>
</select>
```

## 📱 Responsive Design

The framework automatically adapts to different screen sizes and device capabilities:

- **Desktop**: Full cursor effects and high-performance animations
- **Mobile**: Touch-optimized interactions and motion-based effects
- **Tablet**: Hybrid approach with touch and cursor support

## ♿ Accessibility

- Full keyboard navigation support
- ARIA attributes for screen readers
- High contrast mode support
- Reduced motion preferences respected
- Focus management and indicators

## 🎯 Browser Support

- **Modern Browsers**: Chrome 88+, Firefox 87+, Safari 14+, Edge 88+
- **Backdrop Filter**: Required for glass effects (graceful fallback provided)
- **CSS Custom Properties**: Required for theming
- **ES6 Modules**: Required for JavaScript functionality

## 📦 Build and Development

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint
```

### Project Structure

```
liquid-glass-ui/
├── src/
│   ├── css/
│   │   ├── base/           # Core styles and variables
│   │   ├── components/     # Component styles
│   │   ├── effects/        # Glass effects
│   │   └── utilities/      # Utility classes
│   ├── js/
│   │   ├── components/     # JavaScript components
│   │   ├── effects/        # Effect controllers
│   │   └── utils/          # Helper utilities
│   └── index.js           # Main entry point
├── dist/                  # Built files
├── docs/                  # Documentation
├── examples/              # Example implementations
└── tests/                 # Test files
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by Apple's design language and iOS interface elements
- Built with modern web standards and best practices
- Thanks to the open source community for tools and inspiration
