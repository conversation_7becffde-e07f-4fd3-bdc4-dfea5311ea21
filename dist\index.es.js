class u {
  constructor(e = {}) {
    this.options = {
      enableAnimations: !0,
      enableInteractions: !0,
      performanceMode: "auto",
      // 'low', 'medium', 'high', 'auto'
      ...e
    }, this.elements = /* @__PURE__ */ new Map(), this.observers = /* @__PURE__ */ new Map(), this.isInitialized = !1;
  }
  /**
   * Initialize glass effects system
   */
  init() {
    this.isInitialized || (this.detectPerformanceCapabilities(), this.setupIntersectionObserver(), this.bindGlobalEvents(), this.scanForElements(), this.isInitialized = !0);
  }
  /**
   * Detect device performance capabilities
   */
  detectPerformanceCapabilities() {
    if (this.options.performanceMode !== "auto") return;
    const e = navigator.deviceMemory || 4, t = navigator.hardwareConcurrency || 4, s = navigator.connection;
    let i = 0;
    e >= 8 ? i += 3 : e >= 4 ? i += 2 : e >= 2 && (i += 1), t >= 8 ? i += 3 : t >= 4 ? i += 2 : t >= 2 && (i += 1), s ? s.effectiveType === "4g" ? i += 2 : s.effectiveType === "3g" && (i += 1) : i += 2, i >= 6 ? this.options.performanceMode = "high" : i >= 4 ? this.options.performanceMode = "medium" : this.options.performanceMode = "low", document.documentElement.classList.add(`performance-${this.options.performanceMode}`);
  }
  /**
   * Setup intersection observer for performance optimization
   */
  setupIntersectionObserver() {
    "IntersectionObserver" in window && (this.intersectionObserver = new IntersectionObserver(
      (e) => {
        e.forEach((t) => {
          const s = t.target, i = this.elements.get(s);
          i && (t.isIntersecting ? this.activateElement(s, i) : this.deactivateElement(s, i));
        });
      },
      {
        rootMargin: "50px",
        threshold: 0.1
      }
    ));
  }
  /**
   * Bind global events
   */
  bindGlobalEvents() {
    document.addEventListener("visibilitychange", () => {
      document.hidden ? this.pauseAllEffects() : this.resumeAllEffects();
    });
    let e;
    window.addEventListener("resize", () => {
      clearTimeout(e), e = setTimeout(() => {
        this.updateAllElements();
      }, 100);
    });
  }
  /**
   * Scan for elements with glass effects
   */
  scanForElements() {
    [
      "[data-glass-effect]",
      ".glass",
      ".glass-subtle",
      ".glass-light",
      ".glass-medium",
      ".glass-strong",
      ".glass-intense"
    ].forEach((t) => {
      document.querySelectorAll(t).forEach((s) => {
        this.addElement(s);
      });
    });
  }
  /**
   * Add element to glass effects system
   */
  addElement(e, t = {}) {
    if (this.elements.has(e)) return;
    const s = {
      element: e,
      options: { ...this.getDefaultOptions(), ...t },
      isActive: !1,
      animations: /* @__PURE__ */ new Set(),
      observers: /* @__PURE__ */ new Set()
    };
    this.elements.set(e, s), this.intersectionObserver ? this.intersectionObserver.observe(e) : this.activateElement(e, s), this.initializeElement(e, s);
  }
  /**
   * Get default options based on performance mode
   */
  getDefaultOptions() {
    const e = {
      enableBlur: !0,
      enableShadows: !0,
      enableAnimations: this.options.enableAnimations,
      enableInteractions: this.options.enableInteractions
    };
    switch (this.options.performanceMode) {
      case "low":
        return {
          ...e,
          enableBlur: !1,
          enableAnimations: !1,
          maxBlur: 4
        };
      case "medium":
        return {
          ...e,
          maxBlur: 8
        };
      case "high":
        return {
          ...e,
          maxBlur: 16
        };
      default:
        return e;
    }
  }
  /**
   * Initialize element with glass effects
   */
  initializeElement(e, t) {
    const { options: s } = t;
    e.classList.add("glass-element"), e.classList.add(`glass-performance-${this.options.performanceMode}`), this.updateElementProperties(e, t), s.enableInteractions && this.addInteractionListeners(e, t);
  }
  /**
   * Update element CSS custom properties
   */
  updateElementProperties(e, t) {
    const { options: s } = t, i = e.getBoundingClientRect();
    e.style.setProperty("--element-width", `${i.width}px`), e.style.setProperty("--element-height", `${i.height}px`), e.style.setProperty("--max-blur", `${s.maxBlur || 8}px`), e.style.setProperty("--enable-animations", s.enableAnimations ? "1" : "0");
  }
  /**
   * Add interaction listeners to element
   */
  addInteractionListeners(e, t) {
    Object.entries({
      mouseenter: () => this.handleElementHover(e, t, !0),
      mouseleave: () => this.handleElementHover(e, t, !1),
      focus: () => this.handleElementFocus(e, t, !0),
      blur: () => this.handleElementFocus(e, t, !1),
      click: (i) => this.handleElementClick(e, t, i)
    }).forEach(([i, n]) => {
      e.addEventListener(i, n), t.observers.add(() => {
        e.removeEventListener(i, n);
      });
    });
  }
  /**
   * Handle element hover
   */
  handleElementHover(e, t, s) {
    t.options.enableInteractions && (e.classList.toggle("glass-hover", s), s ? this.addAnimation(e, "hover-in") : this.addAnimation(e, "hover-out"));
  }
  /**
   * Handle element focus
   */
  handleElementFocus(e, t, s) {
    t.options.enableInteractions && e.classList.toggle("glass-focus", s);
  }
  /**
   * Handle element click
   */
  handleElementClick(e, t, s) {
    t.options.enableInteractions && (this.createRippleEffect(e, s), this.addAnimation(e, "click"));
  }
  /**
   * Create ripple effect
   */
  createRippleEffect(e, t) {
    const s = e.getBoundingClientRect(), i = t.clientX - s.left, n = t.clientY - s.top, a = document.createElement("div");
    a.className = "glass-ripple", a.style.cssText = `
      position: absolute;
      left: ${i}px;
      top: ${n}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: glass-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `, e.style.position = "relative", e.appendChild(a), setTimeout(() => {
      a.parentNode && a.parentNode.removeChild(a);
    }, 600);
  }
  /**
   * Add animation to element
   */
  addAnimation(e, t) {
    const s = this.elements.get(e);
    !s || !s.options.enableAnimations || (s.animations.add(t), e.classList.add(`glass-animate-${t}`), setTimeout(() => {
      s.animations.delete(t), e.classList.remove(`glass-animate-${t}`);
    }, 300));
  }
  /**
   * Activate element effects
   */
  activateElement(e, t) {
    t.isActive || (t.isActive = !0, e.classList.add("glass-active"));
  }
  /**
   * Deactivate element effects
   */
  deactivateElement(e, t) {
    t.isActive && (t.isActive = !1, e.classList.remove("glass-active"));
  }
  /**
   * Remove element from glass effects system
   */
  removeElement(e) {
    const t = this.elements.get(e);
    t && (this.intersectionObserver && this.intersectionObserver.unobserve(e), t.observers.forEach((s) => s()), e.classList.remove("glass-element", "glass-active", "glass-hover", "glass-focus"), this.elements.delete(e));
  }
  /**
   * Update all elements
   */
  updateAllElements() {
    this.elements.forEach((e, t) => {
      this.updateElementProperties(t, e);
    });
  }
  /**
   * Pause all effects
   */
  pauseAllEffects() {
    this.elements.forEach((e, t) => {
      t.classList.add("glass-paused");
    });
  }
  /**
   * Resume all effects
   */
  resumeAllEffects() {
    this.elements.forEach((e, t) => {
      t.classList.remove("glass-paused");
    });
  }
  /**
   * Destroy glass effects system
   */
  destroy() {
    this.elements.forEach((e, t) => {
      this.removeElement(t);
    }), this.intersectionObserver && this.intersectionObserver.disconnect(), this.elements.clear(), this.observers.clear(), this.isInitialized = !1;
  }
}
class p {
  constructor(e = {}) {
    this.options = {
      enableShadows: !0,
      enableLighting: !0,
      shadowIntensity: 0.3,
      lightingIntensity: 0.2,
      maxDistance: 200,
      smoothing: 0.1,
      ...e
    }, this.cursor = { x: 0, y: 0 }, this.smoothCursor = { x: 0, y: 0 }, this.elements = /* @__PURE__ */ new Set(), this.isActive = !1, this.animationFrame = null;
  }
  /**
   * Initialize cursor effects
   */
  init() {
    this.isActive || (this.isActive = !0, this.bindEvents(), this.findElements(), this.startAnimation());
  }
  /**
   * Bind mouse events
   */
  bindEvents() {
    this.handleMouseMove = this.handleMouseMove.bind(this), this.handleMouseLeave = this.handleMouseLeave.bind(this), document.addEventListener("mousemove", this.handleMouseMove), document.addEventListener("mouseleave", this.handleMouseLeave);
  }
  /**
   * Find elements with cursor effects
   */
  findElements() {
    [
      "[data-cursor-effect]",
      ".glass-cursor-effect",
      ".glass",
      ".glass-button",
      ".glass-card"
    ].forEach((t) => {
      document.querySelectorAll(t).forEach((s) => {
        this.addElement(s);
      });
    });
  }
  /**
   * Add element to cursor effects
   */
  addElement(e) {
    this.elements.has(e) || (this.elements.add(e), e.style.setProperty("--cursor-x", "0"), e.style.setProperty("--cursor-y", "0"), e.style.setProperty("--cursor-distance", "1"), e.style.setProperty("--cursor-intensity", "0"), e.classList.add("has-cursor-effect"));
  }
  /**
   * Remove element from cursor effects
   */
  removeElement(e) {
    this.elements.has(e) && (this.elements.delete(e), e.classList.remove("has-cursor-effect"), e.style.removeProperty("--cursor-x"), e.style.removeProperty("--cursor-y"), e.style.removeProperty("--cursor-distance"), e.style.removeProperty("--cursor-intensity"));
  }
  /**
   * Handle mouse move
   */
  handleMouseMove(e) {
    this.cursor.x = e.clientX, this.cursor.y = e.clientY;
  }
  /**
   * Handle mouse leave
   */
  handleMouseLeave() {
    this.cursor.x = -1e3, this.cursor.y = -1e3;
  }
  /**
   * Start animation loop
   */
  startAnimation() {
    const e = () => {
      this.isActive && (this.updateCursor(), this.updateElements(), this.animationFrame = requestAnimationFrame(e));
    };
    e();
  }
  /**
   * Update smooth cursor position
   */
  updateCursor() {
    this.smoothCursor.x += (this.cursor.x - this.smoothCursor.x) * this.options.smoothing, this.smoothCursor.y += (this.cursor.y - this.smoothCursor.y) * this.options.smoothing;
  }
  /**
   * Update all elements with cursor effects
   */
  updateElements() {
    this.elements.forEach((e) => {
      this.updateElement(e);
    });
  }
  /**
   * Update individual element
   */
  updateElement(e) {
    const t = e.getBoundingClientRect(), s = t.left + t.width / 2, i = t.top + t.height / 2, n = this.smoothCursor.x - s, a = this.smoothCursor.y - i, r = Math.sqrt(n * n + a * a), l = Math.min(r / this.options.maxDistance, 1), h = 1 - l, c = (this.smoothCursor.x - t.left) / t.width, m = (this.smoothCursor.y - t.top) / t.height;
    e.style.setProperty("--cursor-x", c.toFixed(3)), e.style.setProperty("--cursor-y", m.toFixed(3)), e.style.setProperty("--cursor-distance", l.toFixed(3)), e.style.setProperty("--cursor-intensity", h.toFixed(3)), this.options.enableShadows && this.applyShadowEffect(e, n, a, h), this.options.enableLighting && this.applyLightingEffect(e, c, m, h);
  }
  /**
   * Apply shadow effect based on cursor position
   */
  applyShadowEffect(e, t, s, i) {
    const n = i * this.options.shadowIntensity, a = -t * 0.1 * n, r = -s * 0.1 * n, l = 20 * n, h = 0.3 * n, c = `${a}px ${r}px ${l}px rgba(0, 0, 0, ${h})`;
    e.style.setProperty("--cursor-shadow", c);
  }
  /**
   * Apply lighting effect based on cursor position
   */
  applyLightingEffect(e, t, s, i) {
    const n = i * this.options.lightingIntensity, a = t * 100, r = s * 100, h = `radial-gradient(${150 * i}px circle at ${a}% ${r}%, rgba(255, 255, 255, ${n}) 0%, transparent 70%)`;
    e.style.setProperty("--cursor-light", h);
  }
  /**
   * Create ripple effect at cursor position
   */
  createRipple(e, t) {
    const s = e.getBoundingClientRect(), i = t.clientX - s.left, n = t.clientY - s.top, a = document.createElement("div");
    a.className = "cursor-ripple", a.style.cssText = `
      position: absolute;
      left: ${i}px;
      top: ${n}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: cursor-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `, e.style.position = "relative", e.appendChild(a), setTimeout(() => {
      a.parentNode && a.parentNode.removeChild(a);
    }, 600);
  }
  /**
   * Add ripple effect to element
   */
  addRippleEffect(e) {
    const t = (s) => {
      this.createRipple(e, s);
    };
    return e.addEventListener("click", t), () => {
      e.removeEventListener("click", t);
    };
  }
  /**
   * Update options
   */
  updateOptions(e) {
    this.options = { ...this.options, ...e };
  }
  /**
   * Pause cursor effects
   */
  pause() {
    this.isActive = !1, this.animationFrame && (cancelAnimationFrame(this.animationFrame), this.animationFrame = null);
  }
  /**
   * Resume cursor effects
   */
  resume() {
    this.isActive || (this.isActive = !0, this.startAnimation());
  }
  /**
   * Destroy cursor effects
   */
  destroy() {
    this.pause(), document.removeEventListener("mousemove", this.handleMouseMove), document.removeEventListener("mouseleave", this.handleMouseLeave), this.elements.forEach((e) => {
      this.removeElement(e);
    }), this.elements.clear();
  }
  /**
   * Get cursor position
   */
  getCursorPosition() {
    return { ...this.smoothCursor };
  }
  /**
   * Check if cursor effects are active
   */
  isEnabled() {
    return this.isActive;
  }
}
class f {
  constructor(e = {}) {
    this.options = {
      enableTilt: !0,
      enableParallax: !0,
      enableLighting: !0,
      sensitivity: 1,
      maxTilt: 15,
      smoothing: 0.1,
      ...e
    }, this.orientation = { alpha: 0, beta: 0, gamma: 0 }, this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }, this.elements = /* @__PURE__ */ new Set(), this.isActive = !1, this.animationFrame = null, this.permissionGranted = !1;
  }
  /**
   * Initialize motion effects
   */
  async init() {
    if (!this.isActive) {
      if (!this.isSupported())
        return console.warn("Device orientation not supported"), !1;
      if (typeof DeviceOrientationEvent.requestPermission == "function")
        try {
          const e = await DeviceOrientationEvent.requestPermission();
          this.permissionGranted = e === "granted";
        } catch (e) {
          return console.warn("Device orientation permission denied:", e), !1;
        }
      else
        this.permissionGranted = !0;
      return this.permissionGranted ? (this.isActive = !0, this.bindEvents(), this.findElements(), this.startAnimation(), !0) : !1;
    }
  }
  /**
   * Check if device orientation is supported
   */
  isSupported() {
    return "DeviceOrientationEvent" in window;
  }
  /**
   * Bind device orientation events
   */
  bindEvents() {
    this.handleDeviceOrientation = this.handleDeviceOrientation.bind(this), window.addEventListener("deviceorientation", this.handleDeviceOrientation);
  }
  /**
   * Find elements with motion effects
   */
  findElements() {
    [
      "[data-motion-effect]",
      ".glass-motion-effect",
      ".motion-tilt",
      ".motion-parallax"
    ].forEach((t) => {
      document.querySelectorAll(t).forEach((s) => {
        this.addElement(s);
      });
    });
  }
  /**
   * Add element to motion effects
   */
  addElement(e) {
    this.elements.has(e) || (this.elements.add(e), e.style.setProperty("--motion-x", "0"), e.style.setProperty("--motion-y", "0"), e.style.setProperty("--motion-z", "0"), e.style.setProperty("--motion-intensity", "0"), e.classList.add("has-motion-effect"));
  }
  /**
   * Remove element from motion effects
   */
  removeElement(e) {
    this.elements.has(e) && (this.elements.delete(e), e.classList.remove("has-motion-effect"), e.style.removeProperty("--motion-x"), e.style.removeProperty("--motion-y"), e.style.removeProperty("--motion-z"), e.style.removeProperty("--motion-intensity"));
  }
  /**
   * Handle device orientation change
   */
  handleDeviceOrientation(e) {
    this.orientation.alpha = e.alpha || 0, this.orientation.beta = e.beta || 0, this.orientation.gamma = e.gamma || 0;
  }
  /**
   * Start animation loop
   */
  startAnimation() {
    const e = () => {
      this.isActive && (this.updateOrientation(), this.updateElements(), this.animationFrame = requestAnimationFrame(e));
    };
    e();
  }
  /**
   * Update smooth orientation values
   */
  updateOrientation() {
    this.smoothOrientation.alpha += (this.orientation.alpha - this.smoothOrientation.alpha) * this.options.smoothing, this.smoothOrientation.beta += (this.orientation.beta - this.smoothOrientation.beta) * this.options.smoothing, this.smoothOrientation.gamma += (this.orientation.gamma - this.smoothOrientation.gamma) * this.options.smoothing;
  }
  /**
   * Update all elements with motion effects
   */
  updateElements() {
    this.elements.forEach((e) => {
      this.updateElement(e);
    });
  }
  /**
   * Update individual element
   */
  updateElement(e) {
    const { beta: t, gamma: s } = this.smoothOrientation, i = this.clamp(s / 90, -1, 1) * this.options.sensitivity, n = this.clamp(t / 180, -1, 1) * this.options.sensitivity, a = Math.sqrt(i * i + n * n);
    e.style.setProperty("--motion-x", i.toFixed(3)), e.style.setProperty("--motion-y", n.toFixed(3)), e.style.setProperty("--motion-intensity", Math.min(a, 1).toFixed(3)), this.options.enableTilt && this.applyTiltEffect(e, i, n), this.options.enableParallax && this.applyParallaxEffect(e, i, n), this.options.enableLighting && this.applyLightingEffect(e, i, n, a);
  }
  /**
   * Apply tilt effect based on device orientation
   */
  applyTiltEffect(e, t, s) {
    const i = s * this.options.maxTilt, n = -t * this.options.maxTilt;
    e.style.setProperty("--motion-tilt-x", `${i}deg`), e.style.setProperty("--motion-tilt-y", `${n}deg`);
  }
  /**
   * Apply parallax effect based on device orientation
   */
  applyParallaxEffect(e, t, s) {
    const i = t * 20, n = s * 20;
    e.style.setProperty("--motion-parallax-x", `${i}px`), e.style.setProperty("--motion-parallax-y", `${n}px`);
  }
  /**
   * Apply lighting effect based on device orientation
   */
  applyLightingEffect(e, t, s, i) {
    const n = (t + 1) * 50, a = (s + 1) * 50, r = i * 0.3, l = `radial-gradient(circle at ${n}% ${a}%, rgba(255, 255, 255, ${r}) 0%, transparent 70%)`;
    e.style.setProperty("--motion-light", l);
  }
  /**
   * Create shake effect
   */
  createShake(e, t = 1) {
    const s = `motion-shake-${Math.floor(t * 3) + 1}`;
    e.classList.add(s), setTimeout(() => {
      e.classList.remove(s);
    }, 500);
  }
  /**
   * Clamp value between min and max
   */
  clamp(e, t, s) {
    return Math.min(Math.max(e, t), s);
  }
  /**
   * Request permission for device orientation (iOS 13+)
   */
  async requestPermission() {
    if (typeof DeviceOrientationEvent.requestPermission == "function")
      try {
        const e = await DeviceOrientationEvent.requestPermission();
        return this.permissionGranted = e === "granted", this.permissionGranted;
      } catch (e) {
        return console.error("Error requesting device orientation permission:", e), !1;
      }
    return this.permissionGranted = !0, !0;
  }
  /**
   * Calibrate device orientation
   */
  calibrate() {
    this.orientation = { alpha: 0, beta: 0, gamma: 0 }, this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 };
  }
  /**
   * Update options
   */
  updateOptions(e) {
    this.options = { ...this.options, ...e };
  }
  /**
   * Pause motion effects
   */
  pause() {
    this.isActive = !1, this.animationFrame && (cancelAnimationFrame(this.animationFrame), this.animationFrame = null);
  }
  /**
   * Resume motion effects
   */
  resume() {
    !this.isActive && this.permissionGranted && (this.isActive = !0, this.startAnimation());
  }
  /**
   * Destroy motion effects
   */
  destroy() {
    this.pause(), window.removeEventListener("deviceorientation", this.handleDeviceOrientation), this.elements.forEach((e) => {
      this.removeElement(e);
    }), this.elements.clear();
  }
  /**
   * Get current orientation
   */
  getOrientation() {
    return { ...this.smoothOrientation };
  }
  /**
   * Check if motion effects are active
   */
  isEnabled() {
    return this.isActive && this.permissionGranted;
  }
}
class g {
  constructor(e, t = {}) {
    this.element = e, this.options = {
      enableRipple: !0,
      enableHover: !0,
      enableFocus: !0,
      enableLoading: !0,
      rippleColor: "rgba(255, 255, 255, 0.3)",
      ...t
    }, this.isLoading = !1, this.ripples = /* @__PURE__ */ new Set(), this.init();
  }
  /**
   * Initialize button component
   */
  init() {
    this.setupElement(), this.bindEvents(), this.setupAccessibility();
  }
  /**
   * Setup button element
   */
  setupElement() {
    this.element.classList.contains("glass-btn") || this.element.classList.add("glass-btn"), this.element.setAttribute("data-glass-button", "initialized"), this.updateState();
  }
  /**
   * Bind event listeners
   */
  bindEvents() {
    this.element.addEventListener("click", this.handleClick.bind(this)), this.options.enableHover && (this.element.addEventListener("mouseenter", this.handleMouseEnter.bind(this)), this.element.addEventListener("mouseleave", this.handleMouseLeave.bind(this))), this.options.enableFocus && (this.element.addEventListener("focus", this.handleFocus.bind(this)), this.element.addEventListener("blur", this.handleBlur.bind(this))), this.element.addEventListener("keydown", this.handleKeyDown.bind(this));
  }
  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    !this.element.getAttribute("role") && this.element.tagName !== "BUTTON" && this.element.setAttribute("role", "button"), !this.element.hasAttribute("tabindex") && this.element.tagName !== "BUTTON" && this.element.setAttribute("tabindex", "0"), this.options.enableLoading && this.element.setAttribute("aria-busy", "false");
  }
  /**
   * Handle click events
   */
  handleClick(e) {
    if (this.isLoading || this.element.disabled) {
      e.preventDefault();
      return;
    }
    this.options.enableRipple && this.createRipple(e), this.dispatchEvent("glass-button-click", { originalEvent: e });
  }
  /**
   * Handle mouse enter
   */
  handleMouseEnter(e) {
    this.element.classList.add("glass-btn-hover"), this.dispatchEvent("glass-button-hover", { state: "enter", originalEvent: e });
  }
  /**
   * Handle mouse leave
   */
  handleMouseLeave(e) {
    this.element.classList.remove("glass-btn-hover"), this.dispatchEvent("glass-button-hover", { state: "leave", originalEvent: e });
  }
  /**
   * Handle focus
   */
  handleFocus(e) {
    this.element.classList.add("glass-btn-focus"), this.dispatchEvent("glass-button-focus", { state: "focus", originalEvent: e });
  }
  /**
   * Handle blur
   */
  handleBlur(e) {
    this.element.classList.remove("glass-btn-focus"), this.dispatchEvent("glass-button-focus", { state: "blur", originalEvent: e });
  }
  /**
   * Handle keyboard events
   */
  handleKeyDown(e) {
    (e.key === "Enter" || e.key === " ") && (e.preventDefault(), this.element.click());
  }
  /**
   * Create ripple effect
   */
  createRipple(e) {
    const t = this.element.getBoundingClientRect(), s = e.clientX - t.left, i = e.clientY - t.top, n = document.createElement("div");
    n.className = "glass-btn-ripple", n.style.cssText = `
      position: absolute;
      left: ${s}px;
      top: ${i}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: ${this.options.rippleColor};
      transform: translate(-50%, -50%);
      animation: glass-btn-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1;
    `, getComputedStyle(this.element).position === "static" && (this.element.style.position = "relative"), this.element.appendChild(n), this.ripples.add(n), setTimeout(() => {
      n.parentNode && n.parentNode.removeChild(n), this.ripples.delete(n);
    }, 600);
  }
  /**
   * Set loading state
   */
  setLoading(e = !0) {
    this.isLoading = e, e ? (this.element.classList.add("glass-btn-loading"), this.element.setAttribute("aria-busy", "true"), this.element.disabled = !0) : (this.element.classList.remove("glass-btn-loading"), this.element.setAttribute("aria-busy", "false"), this.element.disabled = !1), this.updateState(), this.dispatchEvent("glass-button-loading", { loading: e });
  }
  /**
   * Set disabled state
   */
  setDisabled(e = !0) {
    this.element.disabled = e, this.element.classList.toggle("glass-btn-disabled", e), this.updateState(), this.dispatchEvent("glass-button-disabled", { disabled: e });
  }
  /**
   * Set button variant
   */
  setVariant(e) {
    const t = [
      "glass-btn-primary",
      "glass-btn-secondary",
      "glass-btn-success",
      "glass-btn-warning",
      "glass-btn-danger",
      "glass-btn-ghost"
    ];
    this.element.classList.remove(...t), e && e !== "default" && this.element.classList.add(`glass-btn-${e}`), this.dispatchEvent("glass-button-variant", { variant: e });
  }
  /**
   * Set button size
   */
  setSize(e) {
    const t = ["glass-btn-xs", "glass-btn-sm", "glass-btn-lg", "glass-btn-xl"];
    this.element.classList.remove(...t), e && e !== "default" && this.element.classList.add(`glass-btn-${e}`), this.dispatchEvent("glass-button-size", { size: e });
  }
  /**
   * Update button state
   */
  updateState() {
    const e = {
      loading: this.isLoading,
      disabled: this.element.disabled,
      focused: this.element.classList.contains("glass-btn-focus"),
      hovered: this.element.classList.contains("glass-btn-hover")
    };
    this.element.setAttribute("data-state", JSON.stringify(e));
  }
  /**
   * Dispatch custom event
   */
  dispatchEvent(e, t = {}) {
    const s = new CustomEvent(e, {
      detail: {
        button: this,
        element: this.element,
        ...t
      },
      bubbles: !0,
      cancelable: !0
    });
    this.element.dispatchEvent(s);
  }
  /**
   * Add animation
   */
  addAnimation(e, t = 1e3) {
    const s = `glass-btn-animate-${e}`;
    this.element.classList.add(s), setTimeout(() => {
      this.element.classList.remove(s);
    }, t);
  }
  /**
   * Update options
   */
  updateOptions(e) {
    this.options = { ...this.options, ...e };
  }
  /**
   * Get button state
   */
  getState() {
    return {
      loading: this.isLoading,
      disabled: this.element.disabled,
      variant: this.getVariant(),
      size: this.getSize()
    };
  }
  /**
   * Get current variant
   */
  getVariant() {
    return ["primary", "secondary", "success", "warning", "danger", "ghost"].find((t) => this.element.classList.contains(`glass-btn-${t}`)) || "default";
  }
  /**
   * Get current size
   */
  getSize() {
    return ["xs", "sm", "lg", "xl"].find((t) => this.element.classList.contains(`glass-btn-${t}`)) || "default";
  }
  /**
   * Destroy button component
   */
  destroy() {
    this.ripples.forEach((e) => {
      e.parentNode && e.parentNode.removeChild(e);
    }), this.ripples.clear(), this.element.removeAttribute("data-glass-button"), this.element.classList.remove("glass-btn-hover", "glass-btn-focus", "glass-btn-loading", "glass-btn-disabled");
  }
}
class v {
  constructor(e, t = {}) {
    this.element = e, this.options = {
      enableHover: !0,
      enableClick: !0,
      enableLoading: !0,
      hoverEffect: "lift",
      // 'lift', 'glow', 'scale'
      ...t
    }, this.isLoading = !1, this.init();
  }
  /**
   * Initialize card component
   */
  init() {
    this.setupElement(), this.bindEvents(), this.setupAccessibility();
  }
  /**
   * Setup card element
   */
  setupElement() {
    this.element.classList.contains("glass-card") || this.element.classList.add("glass-card"), this.element.setAttribute("data-glass-card", "initialized"), this.updateState();
  }
  /**
   * Bind event listeners
   */
  bindEvents() {
    this.options.enableClick && this.element.classList.contains("glass-card-interactive") && this.element.addEventListener("click", this.handleClick.bind(this)), this.options.enableHover && (this.element.addEventListener("mouseenter", this.handleMouseEnter.bind(this)), this.element.addEventListener("mouseleave", this.handleMouseLeave.bind(this))), this.element.addEventListener("focus", this.handleFocus.bind(this)), this.element.addEventListener("blur", this.handleBlur.bind(this)), this.element.classList.contains("glass-card-interactive") && this.element.addEventListener("keydown", this.handleKeyDown.bind(this));
  }
  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    this.element.classList.contains("glass-card-interactive") && (this.element.hasAttribute("tabindex") || this.element.setAttribute("tabindex", "0"), this.element.getAttribute("role") || this.element.setAttribute("role", "button")), this.options.enableLoading && this.element.setAttribute("aria-busy", "false");
  }
  /**
   * Handle click events
   */
  handleClick(e) {
    if (this.isLoading) {
      e.preventDefault();
      return;
    }
    this.dispatchEvent("glass-card-click", { originalEvent: e });
  }
  /**
   * Handle mouse enter
   */
  handleMouseEnter(e) {
    switch (this.element.classList.add("glass-card-hover"), this.options.hoverEffect) {
      case "glow":
        this.element.classList.add("glass-card-glow");
        break;
      case "scale":
        this.element.classList.add("glass-card-scale");
        break;
    }
    this.dispatchEvent("glass-card-hover", { state: "enter", originalEvent: e });
  }
  /**
   * Handle mouse leave
   */
  handleMouseLeave(e) {
    this.element.classList.remove("glass-card-hover", "glass-card-glow", "glass-card-scale"), this.dispatchEvent("glass-card-hover", { state: "leave", originalEvent: e });
  }
  /**
   * Handle focus
   */
  handleFocus(e) {
    this.element.classList.add("glass-card-focus"), this.dispatchEvent("glass-card-focus", { state: "focus", originalEvent: e });
  }
  /**
   * Handle blur
   */
  handleBlur(e) {
    this.element.classList.remove("glass-card-focus"), this.dispatchEvent("glass-card-focus", { state: "blur", originalEvent: e });
  }
  /**
   * Handle keyboard events
   */
  handleKeyDown(e) {
    (e.key === "Enter" || e.key === " ") && (e.preventDefault(), this.element.click());
  }
  /**
   * Set loading state
   */
  setLoading(e = !0) {
    this.isLoading = e, e ? (this.element.classList.add("glass-card-loading"), this.element.setAttribute("aria-busy", "true")) : (this.element.classList.remove("glass-card-loading"), this.element.setAttribute("aria-busy", "false")), this.updateState(), this.dispatchEvent("glass-card-loading", { loading: e });
  }
  /**
   * Set card variant
   */
  setVariant(e) {
    const t = [
      "glass-card-elevated",
      "glass-card-subtle",
      "glass-card-outlined",
      "glass-card-filled"
    ];
    this.element.classList.remove(...t), e && e !== "default" && this.element.classList.add(`glass-card-${e}`), this.dispatchEvent("glass-card-variant", { variant: e });
  }
  /**
   * Set card size
   */
  setSize(e) {
    const t = ["glass-card-sm", "glass-card-lg"];
    this.element.classList.remove(...t), e && e !== "default" && this.element.classList.add(`glass-card-${e}`), this.dispatchEvent("glass-card-size", { size: e });
  }
  /**
   * Add badge to card
   */
  addBadge(e, t = {}) {
    const s = {
      position: "top-right",
      variant: "default",
      ...t
    };
    this.removeBadge();
    const i = document.createElement("div");
    i.className = `glass-card-badge glass-card-badge-${s.position}`, i.textContent = e, s.variant !== "default" && i.classList.add(`glass-card-badge-${s.variant}`), this.element.appendChild(i), this.dispatchEvent("glass-card-badge-added", { text: e, options: s });
  }
  /**
   * Remove badge from card
   */
  removeBadge() {
    const e = this.element.querySelector(".glass-card-badge");
    e && (e.remove(), this.dispatchEvent("glass-card-badge-removed"));
  }
  /**
   * Update card content
   */
  updateContent(e) {
    const t = this.element.querySelector(".glass-card-body");
    t && (typeof e == "string" ? t.innerHTML = e : e instanceof HTMLElement && (t.innerHTML = "", t.appendChild(e)), this.dispatchEvent("glass-card-content-updated", { content: e }));
  }
  /**
   * Update card state
   */
  updateState() {
    const e = {
      loading: this.isLoading,
      interactive: this.element.classList.contains("glass-card-interactive"),
      focused: this.element.classList.contains("glass-card-focus"),
      hovered: this.element.classList.contains("glass-card-hover")
    };
    this.element.setAttribute("data-state", JSON.stringify(e));
  }
  /**
   * Dispatch custom event
   */
  dispatchEvent(e, t = {}) {
    const s = new CustomEvent(e, {
      detail: {
        card: this,
        element: this.element,
        ...t
      },
      bubbles: !0,
      cancelable: !0
    });
    this.element.dispatchEvent(s);
  }
  /**
   * Add animation
   */
  addAnimation(e, t = 1e3) {
    const s = `glass-card-animate-${e}`;
    this.element.classList.add(s), setTimeout(() => {
      this.element.classList.remove(s);
    }, t);
  }
  /**
   * Update options
   */
  updateOptions(e) {
    this.options = { ...this.options, ...e };
  }
  /**
   * Get card state
   */
  getState() {
    return {
      loading: this.isLoading,
      interactive: this.element.classList.contains("glass-card-interactive"),
      variant: this.getVariant(),
      size: this.getSize()
    };
  }
  /**
   * Get current variant
   */
  getVariant() {
    return ["elevated", "subtle", "outlined", "filled"].find((t) => this.element.classList.contains(`glass-card-${t}`)) || "default";
  }
  /**
   * Get current size
   */
  getSize() {
    return ["sm", "lg"].find((t) => this.element.classList.contains(`glass-card-${t}`)) || "default";
  }
  /**
   * Destroy card component
   */
  destroy() {
    this.element.removeAttribute("data-glass-card"), this.element.classList.remove("glass-card-hover", "glass-card-focus", "glass-card-loading", "glass-card-glow", "glass-card-scale"), this.removeBadge();
  }
}
class b {
  constructor(e, t = {}) {
    this.element = e, this.options = {
      backdrop: !0,
      keyboard: !0,
      focus: !0,
      ...t
    }, this.isOpen = !1, this.init();
  }
  init() {
    this.element.setAttribute("data-glass-modal", "initialized");
  }
  open() {
    this.isOpen = !0, this.element.classList.add("glass-modal-open");
  }
  close() {
    this.isOpen = !1, this.element.classList.remove("glass-modal-open");
  }
  destroy() {
    this.element.removeAttribute("data-glass-modal");
  }
}
class E {
  constructor(e, t = {}) {
    this.element = e, this.options = {
      sticky: !1,
      collapsible: !0,
      ...t
    }, this.init();
  }
  init() {
    this.element.setAttribute("data-glass-nav", "initialized");
  }
  destroy() {
    this.element.removeAttribute("data-glass-nav");
  }
}
class y {
  constructor(e = "auto") {
    this.currentTheme = e, this.systemTheme = this.getSystemTheme(), this.mediaQuery = window.matchMedia("(prefers-color-scheme: dark)"), this.listeners = /* @__PURE__ */ new Set(), this.storageKey = "liquid-glass-ui-theme";
  }
  /**
   * Initialize theme manager
   */
  init() {
    const e = this.loadTheme();
    e && (this.currentTheme = e), this.applyTheme(), this.mediaQuery.addEventListener("change", this.handleSystemThemeChange.bind(this)), this.initThemeToggles();
  }
  /**
   * Get system theme preference
   */
  getSystemTheme() {
    return this.mediaQuery.matches ? "dark" : "light";
  }
  /**
   * Get effective theme (resolves 'auto' to actual theme)
   */
  getEffectiveTheme() {
    return this.currentTheme === "auto" ? this.systemTheme : this.currentTheme;
  }
  /**
   * Set theme
   */
  setTheme(e) {
    ["light", "dark", "auto"].includes(e) || (console.warn(`Invalid theme: ${e}. Using 'auto' instead.`), e = "auto"), this.currentTheme = e, this.applyTheme(), this.saveTheme(), this.notifyListeners();
  }
  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const t = this.getEffectiveTheme() === "light" ? "dark" : "light";
    this.setTheme(t);
  }
  /**
   * Apply theme to document
   */
  applyTheme() {
    const e = this.getEffectiveTheme();
    document.documentElement.classList.remove("theme-light", "theme-dark"), document.documentElement.removeAttribute("data-theme"), document.documentElement.classList.add(`theme-${e}`), document.documentElement.setAttribute("data-theme", e), this.updateMetaThemeColor(e), this.dispatchThemeEvent(e);
  }
  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(e) {
    let t = document.querySelector('meta[name="theme-color"]');
    t || (t = document.createElement("meta"), t.name = "theme-color", document.head.appendChild(t));
    const s = {
      light: "#ffffff",
      dark: "#000000"
    };
    t.content = s[e] || s.light;
  }
  /**
   * Dispatch custom theme change event
   */
  dispatchThemeEvent(e) {
    const t = new CustomEvent("themechange", {
      detail: {
        theme: e,
        previousTheme: this.previousTheme,
        isSystemTheme: this.currentTheme === "auto"
      }
    });
    this.previousTheme = e, document.dispatchEvent(t);
  }
  /**
   * Handle system theme changes
   */
  handleSystemThemeChange(e) {
    this.systemTheme = e.matches ? "dark" : "light", this.currentTheme === "auto" && (this.applyTheme(), this.notifyListeners());
  }
  /**
   * Initialize theme toggle buttons
   */
  initThemeToggles() {
    document.querySelectorAll("[data-theme-toggle]").forEach((s) => {
      s.addEventListener("click", () => {
        this.toggleTheme();
      });
    }), document.querySelectorAll("[data-theme-selector]").forEach((s) => {
      s.addEventListener("change", (i) => {
        this.setTheme(i.target.value);
      }), s.value = this.currentTheme;
    });
  }
  /**
   * Add theme change listener
   */
  addListener(e) {
    return this.listeners.add(e), () => {
      this.listeners.delete(e);
    };
  }
  /**
   * Notify all listeners of theme change
   */
  notifyListeners() {
    const e = this.getEffectiveTheme();
    this.listeners.forEach((t) => {
      try {
        t(e, this.currentTheme);
      } catch (s) {
        console.error("Error in theme change listener:", s);
      }
    });
  }
  /**
   * Save theme preference to localStorage
   */
  saveTheme() {
    try {
      localStorage.setItem(this.storageKey, this.currentTheme);
    } catch (e) {
      console.warn("Could not save theme preference:", e);
    }
  }
  /**
   * Load theme preference from localStorage
   */
  loadTheme() {
    try {
      return localStorage.getItem(this.storageKey);
    } catch (e) {
      return console.warn("Could not load theme preference:", e), null;
    }
  }
  /**
   * Get theme-specific CSS custom property value
   */
  getThemeValue(e) {
    return getComputedStyle(document.documentElement).getPropertyValue(e);
  }
  /**
   * Set theme-specific CSS custom property
   */
  setThemeValue(e, t) {
    document.documentElement.style.setProperty(e, t);
  }
  /**
   * Create theme-aware color scheme
   */
  createColorScheme(e, t) {
    return this.getEffectiveTheme() === "dark" ? t : e;
  }
  /**
   * Destroy theme manager
   */
  destroy() {
    this.mediaQuery.removeEventListener("change", this.handleSystemThemeChange.bind(this)), this.listeners.clear();
  }
  /**
   * Get current theme info
   */
  getThemeInfo() {
    return {
      current: this.currentTheme,
      effective: this.getEffectiveTheme(),
      system: this.systemTheme,
      isAuto: this.currentTheme === "auto"
    };
  }
}
class L {
  constructor() {
    this.userAgent = navigator.userAgent.toLowerCase(), this.capabilities = this.detectCapabilities();
  }
  /**
   * Check if device is mobile
   */
  isMobile() {
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent);
  }
  /**
   * Check if device is tablet
   */
  isTablet() {
    return /ipad|android(?!.*mobile)/i.test(this.userAgent);
  }
  /**
   * Check if device is desktop
   */
  isDesktop() {
    return !this.isMobile() && !this.isTablet();
  }
  /**
   * Check if device is iOS
   */
  isIOS() {
    return /iphone|ipad|ipod/i.test(this.userAgent);
  }
  /**
   * Check if device is Android
   */
  isAndroid() {
    return /android/i.test(this.userAgent);
  }
  /**
   * Check if device supports touch
   */
  supportsTouch() {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
  }
  /**
   * Check if device supports device orientation
   */
  supportsDeviceOrientation() {
    return "DeviceOrientationEvent" in window;
  }
  /**
   * Check if device supports device motion
   */
  supportsDeviceMotion() {
    return "DeviceMotionEvent" in window;
  }
  /**
   * Check if browser supports backdrop-filter
   */
  supportsBackdropFilter() {
    return CSS.supports("backdrop-filter", "blur(1px)") || CSS.supports("-webkit-backdrop-filter", "blur(1px)");
  }
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion() {
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  }
  /**
   * Get device pixel ratio
   */
  getPixelRatio() {
    return window.devicePixelRatio || 1;
  }
  /**
   * Get viewport dimensions
   */
  getViewport() {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }
  /**
   * Check if device has high refresh rate
   */
  hasHighRefreshRate() {
    return new Promise((e) => {
      let t = 0;
      const s = performance.now(), i = () => {
        if (t++, t < 60)
          requestAnimationFrame(i);
        else {
          const n = performance.now() - s, a = Math.round(t / (n / 1e3));
          e(a > 60);
        }
      };
      requestAnimationFrame(i);
    });
  }
  /**
   * Detect all device capabilities
   */
  detectCapabilities() {
    return {
      isMobile: this.isMobile(),
      isTablet: this.isTablet(),
      isDesktop: this.isDesktop(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      supportsTouch: this.supportsTouch(),
      supportsDeviceOrientation: this.supportsDeviceOrientation(),
      supportsDeviceMotion: this.supportsDeviceMotion(),
      supportsBackdropFilter: this.supportsBackdropFilter(),
      prefersReducedMotion: this.prefersReducedMotion(),
      pixelRatio: this.getPixelRatio(),
      viewport: this.getViewport()
    };
  }
  /**
   * Get optimal glass effect settings based on device
   */
  getOptimalSettings() {
    const e = {
      blurIntensity: "medium",
      animationDuration: "normal",
      enableCursorEffects: !1,
      enableMotionEffects: !1,
      enableParallax: !1,
      enableHighFrameRate: !1
    };
    return this.isDesktop() && (e.enableCursorEffects = !0, e.blurIntensity = "high", e.enableHighFrameRate = !0), this.isMobile() && (e.enableMotionEffects = this.supportsDeviceOrientation(), e.blurIntensity = "low", e.animationDuration = "fast"), this.getPixelRatio() > 2 && (e.enableParallax = !0), this.prefersReducedMotion() && (e.animationDuration = "none", e.enableMotionEffects = !1, e.enableParallax = !1), this.supportsBackdropFilter() || (e.blurIntensity = "none"), e;
  }
  /**
   * Add device classes to document
   */
  addDeviceClasses() {
    const e = [];
    this.isMobile() && e.push("device-mobile"), this.isTablet() && e.push("device-tablet"), this.isDesktop() && e.push("device-desktop"), this.isIOS() && e.push("device-ios"), this.isAndroid() && e.push("device-android"), this.supportsTouch() && e.push("supports-touch"), this.supportsBackdropFilter() && e.push("supports-backdrop-filter"), this.prefersReducedMotion() && e.push("prefers-reduced-motion"), document.documentElement.classList.add(...e);
  }
  /**
   * Listen for viewport changes
   */
  onViewportChange(e) {
    let t;
    const s = () => {
      clearTimeout(t), t = setTimeout(() => {
        this.capabilities.viewport = this.getViewport(), e(this.capabilities.viewport);
      }, 100);
    };
    return window.addEventListener("resize", s), window.addEventListener("orientationchange", s), () => {
      window.removeEventListener("resize", s), window.removeEventListener("orientationchange", s);
    };
  }
}
class d {
  constructor(e = {}) {
    this.options = {
      enableCursorEffects: !0,
      enableMotionEffects: !0,
      enableAnimations: !0,
      theme: "auto",
      // 'light', 'dark', 'auto'
      ...e
    }, this.effects = new u(this.options), this.themeManager = new y(this.options.theme), this.deviceDetector = new L(), this.init();
  }
  /**
   * Initialize the framework
   */
  init() {
    this.themeManager.init(), this.deviceDetector.isDesktop() && this.options.enableCursorEffects && (this.cursorEffects = new p(), this.cursorEffects.init()), this.deviceDetector.isMobile() && this.options.enableMotionEffects && (this.motionEffects = new f(), this.motionEffects.init()), this.initComponents(), document.documentElement.setAttribute("data-liquid-glass-ui", "initialized");
  }
  /**
   * Initialize all components
   */
  initComponents() {
    this.initButtons(), this.initCards(), this.initModals(), this.initNavigation();
  }
  /**
   * Initialize button components
   */
  initButtons() {
    document.querySelectorAll("[data-glass-button]").forEach((t) => new g(t));
  }
  /**
   * Initialize card components
   */
  initCards() {
    document.querySelectorAll("[data-glass-card]").forEach((t) => new v(t));
  }
  /**
   * Initialize modal components
   */
  initModals() {
    document.querySelectorAll("[data-glass-modal]").forEach((t) => new b(t));
  }
  /**
   * Initialize navigation components
   */
  initNavigation() {
    document.querySelectorAll("[data-glass-nav]").forEach((t) => new E(t));
  }
  /**
   * Update theme
   */
  setTheme(e) {
    this.themeManager.setTheme(e);
  }
  /**
   * Destroy the framework instance
   */
  destroy() {
    this.cursorEffects && this.cursorEffects.destroy(), this.motionEffects && this.motionEffects.destroy(), this.themeManager.destroy(), document.documentElement.removeAttribute("data-liquid-glass-ui");
  }
}
typeof window < "u" && !window.LiquidGlassUI && (window.LiquidGlassUI = d, document.readyState === "loading" ? document.addEventListener("DOMContentLoaded", () => {
  document.querySelector("[data-no-auto-init]") || new d();
}) : document.querySelector("[data-no-auto-init]") || new d());
export {
  p as CursorEffects,
  L as DeviceDetector,
  g as GlassButton,
  v as GlassCard,
  u as GlassEffects,
  b as GlassModal,
  E as GlassNavigation,
  f as MotionEffects,
  y as ThemeManager,
  d as default
};
//# sourceMappingURL=index.es.js.map
