<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass UI - Demo</title>
    <style>
        body {
            margin: 0;
            padding: 2rem;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .demo-title {
            color: white;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .demo-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.125rem;
            margin-bottom: 2rem;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <header class="demo-section">
            <h1 class="demo-title">Liquid Glass UI Framework</h1>
            <p class="demo-subtitle">A comprehensive JavaScript/CSS UI framework that replicates Apple's liquid glass design aesthetic</p>
        </header>

        <section class="demo-section">
            <h2 class="demo-title">Glass Buttons</h2>
            <div class="button-grid">
                <button class="glass-btn" data-glass-button>Default</button>
                <button class="glass-btn glass-btn-primary" data-glass-button>Primary</button>
                <button class="glass-btn glass-btn-secondary" data-glass-button>Secondary</button>
                <button class="glass-btn glass-btn-success" data-glass-button>Success</button>
                <button class="glass-btn glass-btn-warning" data-glass-button>Warning</button>
                <button class="glass-btn glass-btn-danger" data-glass-button>Danger</button>
                <button class="glass-btn glass-btn-ghost" data-glass-button>Ghost</button>
                <button class="glass-btn glass-btn-sm" data-glass-button>Small</button>
                <button class="glass-btn glass-btn-lg" data-glass-button>Large</button>
                <button class="glass-btn glass-btn-circle" data-glass-button>●</button>
                <button class="glass-btn glass-btn-pill" data-glass-button>Pill Button</button>
                <button class="glass-btn glass-btn-loading" data-glass-button disabled>Loading</button>
            </div>
        </section>

        <section class="demo-section">
            <h2 class="demo-title">Glass Cards</h2>
            <div class="card-grid">
                <div class="glass-card" data-glass-card>
                    <div class="glass-card-header">
                        <h3 class="glass-card-title">Basic Card</h3>
                        <p class="glass-card-subtitle">Simple glass card with header</p>
                    </div>
                    <div class="glass-card-body">
                        <p class="glass-card-text">This is a basic glass card with a translucent background and blur effect. It demonstrates the core glass aesthetic of the framework.</p>
                    </div>
                    <div class="glass-card-footer">
                        <div class="glass-card-actions">
                            <button class="glass-btn glass-btn-ghost glass-btn-sm">Cancel</button>
                            <button class="glass-btn glass-btn-primary glass-btn-sm">Action</button>
                        </div>
                    </div>
                </div>

                <div class="glass-card glass-card-elevated" data-glass-card>
                    <div class="glass-card-header">
                        <h3 class="glass-card-title">Elevated Card</h3>
                        <p class="glass-card-subtitle">Enhanced with stronger shadows</p>
                    </div>
                    <div class="glass-card-body">
                        <p class="glass-card-text">This elevated card has enhanced shadows and blur effects for a more prominent appearance in the interface.</p>
                    </div>
                </div>

                <div class="glass-card glass-card-subtle" data-glass-card>
                    <div class="glass-card-header">
                        <h3 class="glass-card-title">Subtle Card</h3>
                        <p class="glass-card-subtitle">Minimal glass effect</p>
                    </div>
                    <div class="glass-card-body">
                        <p class="glass-card-text">A more subtle approach to the glass effect, perfect for content that needs to be less prominent.</p>
                    </div>
                </div>

                <div class="glass-card glass-card-interactive" data-glass-card>
                    <div class="glass-card-badge">New</div>
                    <div class="glass-card-header">
                        <h3 class="glass-card-title">Interactive Card</h3>
                        <p class="glass-card-subtitle">Click me!</p>
                    </div>
                    <div class="glass-card-body">
                        <p class="glass-card-text">This card responds to user interactions with hover and click effects. It includes a badge for additional visual interest.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="demo-section">
            <h2 class="demo-title">Theme Controls</h2>
            <div class="glass-card" style="max-width: 400px;">
                <div class="glass-card-body">
                    <label for="theme-selector" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Theme:</label>
                    <select id="theme-selector" data-theme-selector class="glass-btn" style="width: 100%;">
                        <option value="auto">Auto (System)</option>
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                    </select>
                    <button class="glass-btn glass-btn-secondary" data-theme-toggle style="margin-top: 1rem; width: 100%;">
                        Toggle Theme
                    </button>
                </div>
            </div>
        </section>
    </div>

    <!-- Load the framework -->
    <script type="module">
        import LiquidGlassUI from './src/index.js';
        
        // Initialize the framework
        const ui = new LiquidGlassUI({
            enableCursorEffects: true,
            enableMotionEffects: true,
            enableAnimations: true,
            theme: 'auto'
        });

        console.log('Liquid Glass UI initialized:', ui);
        
        // Add some demo interactions
        document.addEventListener('glass-button-click', (event) => {
            console.log('Button clicked:', event.detail);
        });
        
        document.addEventListener('themechange', (event) => {
            console.log('Theme changed:', event.detail);
        });
    </script>
</body>
</html>
