# NPM Publishing Guide for Liquid Glass UI

This guide provides step-by-step instructions for publishing the Liquid Glass UI framework to npm.

## 📋 Pre-Publishing Checklist

### 1. Code Quality and Testing

- [ ] All components are implemented and tested
- [ ] Cross-browser compatibility verified
- [ ] Performance optimizations applied
- [ ] Accessibility features tested
- [ ] Documentation is complete and accurate

### 2. Build and Distribution

- [ ] Production build is working correctly
- [ ] All files are properly included in the distribution
- [ ] Source maps are generated
- [ ] CSS is properly minified and optimized
- [ ] JavaScript is bundled correctly

### 3. Package Configuration

- [ ] `package.json` is properly configured
- [ ] Version number is appropriate (semantic versioning)
- [ ] Dependencies are correctly specified
- [ ] Entry points are correct
- [ ] Files array includes all necessary files

## 🚀 Publishing Steps

### Step 1: Prepare the Package

1. **Update Version Number**
   ```bash
   # For patch release (bug fixes)
   npm version patch
   
   # For minor release (new features)
   npm version minor
   
   # For major release (breaking changes)
   npm version major
   ```

2. **Build for Production**
   ```bash
   npm run build
   ```

3. **Verify Build Output**
   ```bash
   # Check that dist/ directory contains all necessary files
   ls -la dist/
   
   # Verify package contents
   npm pack --dry-run
   ```

### Step 2: Test the Package Locally

1. **Create a Test Project**
   ```bash
   mkdir test-liquid-glass-ui
   cd test-liquid-glass-ui
   npm init -y
   ```

2. **Install Local Package**
   ```bash
   # From the test directory
   npm install ../liquid-glass-ui
   ```

3. **Test Integration**
   Create a simple HTML file to test the package:
   ```html
   <!DOCTYPE html>
   <html>
   <head>
       <title>Test Liquid Glass UI</title>
   </head>
   <body>
       <button class="glass-btn glass-btn-primary">Test Button</button>
       <script type="module">
           import LiquidGlassUI from 'liquid-glass-ui';
           new LiquidGlassUI();
       </script>
   </body>
   </html>
   ```

### Step 3: Prepare npm Account

1. **Create npm Account** (if you don't have one)
   - Go to [npmjs.com](https://www.npmjs.com)
   - Sign up for an account
   - Verify your email address

2. **Login to npm**
   ```bash
   npm login
   ```

3. **Verify Login**
   ```bash
   npm whoami
   ```

### Step 4: Publish to npm

1. **Dry Run** (recommended first)
   ```bash
   npm publish --dry-run
   ```

2. **Publish the Package**
   ```bash
   npm publish
   ```

3. **Verify Publication**
   - Check the package page: `https://www.npmjs.com/package/liquid-glass-ui`
   - Test installation: `npm install liquid-glass-ui`

## 📦 Package.json Configuration

Ensure your `package.json` is properly configured:

```json
{
  "name": "liquid-glass-ui",
  "version": "1.0.0",
  "description": "A comprehensive JavaScript/CSS UI framework that replicates Apple's liquid glass design aesthetic",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist",
    "src",
    "README.md",
    "LICENSE"
  ],
  "scripts": {
    "build": "vite build",
    "prepublishOnly": "npm run build"
  },
  "keywords": [
    "ui",
    "framework",
    "css",
    "javascript",
    "glass",
    "liquid",
    "apple",
    "design",
    "components",
    "tailwind",
    "frosted-glass",
    "backdrop-filter",
    "animations"
  ],
  "author": "Your Name <<EMAIL>>",
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/yourusername/liquid-glass-ui.git"
  },
  "bugs": {
    "url": "https://github.com/yourusername/liquid-glass-ui/issues"
  },
  "homepage": "https://yourusername.github.io/liquid-glass-ui",
  "peerDependencies": {
    "tailwindcss": "^3.0.0"
  }
}
```

## 🔄 Updating the Package

### For Bug Fixes (Patch Release)

1. Fix the bug
2. Update tests
3. Update version: `npm version patch`
4. Publish: `npm publish`

### For New Features (Minor Release)

1. Implement new features
2. Update documentation
3. Add tests
4. Update version: `npm version minor`
5. Publish: `npm publish`

### For Breaking Changes (Major Release)

1. Implement breaking changes
2. Update documentation with migration guide
3. Update tests
4. Update version: `npm version major`
5. Publish: `npm publish`

## 📚 Documentation Updates

After publishing, update:

1. **GitHub Repository**
   - Update README.md
   - Create release notes
   - Tag the release

2. **Documentation Website**
   - Update installation instructions
   - Add new feature documentation
   - Update examples

3. **Community**
   - Announce on social media
   - Update relevant forums/communities
   - Notify users of breaking changes

## 🛡️ Security Considerations

1. **Audit Dependencies**
   ```bash
   npm audit
   npm audit fix
   ```

2. **Check for Vulnerabilities**
   ```bash
   npm audit --audit-level moderate
   ```

3. **Use npm 2FA**
   ```bash
   npm profile enable-2fa auth-and-writes
   ```

## 📊 Monitoring and Analytics

After publishing:

1. **Monitor Downloads**
   - Check npm download statistics
   - Monitor package usage

2. **Track Issues**
   - Monitor GitHub issues
   - Respond to user feedback

3. **Performance Monitoring**
   - Track bundle size
   - Monitor performance metrics

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Make sure you're logged in
   npm whoami
   
   # Check package name availability
   npm view liquid-glass-ui
   ```

2. **Build Failures**
   ```bash
   # Clean and rebuild
   rm -rf dist/
   npm run build
   ```

3. **Version Conflicts**
   ```bash
   # Check current version
   npm version
   
   # Reset if needed
   git tag -d v1.0.0
   npm version 1.0.0
   ```

## ✅ Post-Publishing Checklist

- [ ] Package is available on npm
- [ ] Installation works correctly
- [ ] Documentation is updated
- [ ] GitHub release is created
- [ ] Community is notified
- [ ] Analytics are set up
- [ ] Monitoring is in place

## 📞 Support

If you encounter issues during publishing:

1. Check npm documentation: [docs.npmjs.com](https://docs.npmjs.com)
2. Contact npm support: [npmjs.com/support](https://npmjs.com/support)
3. Check GitHub issues for similar problems

---

**Note**: Always test thoroughly before publishing to avoid publishing broken packages. Use semantic versioning and maintain backward compatibility when possible.
