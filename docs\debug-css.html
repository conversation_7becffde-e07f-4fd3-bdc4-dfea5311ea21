<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Debug - Apple Liquid Glass UI</title>
    <link rel="stylesheet" href="../src/css/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1551384963-cccb0b7ed94b?q=80&w=3247&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') center/cover;
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
            color: white;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            display: grid;
            gap: 2rem;
        }
        
        .debug-section {
            background: rgba(0, 0, 0, 0.8);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .debug-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            color: #00ff88;
        }
        
        .test-element {
            margin: 1rem 0;
            padding: 1rem;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: 500;
        }
        
        .computed-styles {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            margin-top: 1rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; font-size: 2.5rem; margin-bottom: 2rem; text-shadow: 0 0 20px rgba(0,0,0,0.5);">CSS Debug Tool</h1>
        
        <div class="debug-section">
            <h2 class="debug-title">CSS Variables Test</h2>
            <div id="variables-test" style="background: var(--glass-material-regular, red); padding: 1rem; border-radius: 0.5rem;">
                CSS Variables Status: <span id="variables-status">Loading...</span>
            </div>
            <div class="computed-styles" id="variables-computed"></div>
        </div>
        
        <div class="debug-section">
            <h2 class="debug-title">Basic Glass Element</h2>
            <div class="glass test-element" id="basic-glass">
                Basic Glass Element
            </div>
            <div class="computed-styles" id="basic-glass-computed"></div>
        </div>
        
        <div class="debug-section">
            <h2 class="debug-title">Glass Button</h2>
            <button class="glass-btn glass-btn-primary test-element" id="glass-button">
                Glass Button
            </button>
            <div class="computed-styles" id="glass-button-computed"></div>
        </div>
        
        <div class="debug-section">
            <h2 class="debug-title">Glass Container System</h2>
            <div class="glass-container test-element" id="glass-container" style="min-height: 100px;">
                <div class="glass-filter"></div>
                <div class="glass-overlay"></div>
                <div class="glass-specular"></div>
                <div class="glass-content">
                    <div>Glass Container System</div>
                </div>
            </div>
            <div class="computed-styles" id="glass-container-computed"></div>
        </div>
        
        <div class="debug-section">
            <h2 class="debug-title">CSS Import Status</h2>
            <div id="css-status">Checking CSS imports...</div>
        </div>
    </div>

    <script>
        function getComputedStylesText(element, properties) {
            const computed = window.getComputedStyle(element);
            return properties.map(prop => {
                const value = computed.getPropertyValue(prop);
                return `${prop}: ${value}`;
            }).join('\n');
        }
        
        function checkCSSVariables() {
            const testElement = document.getElementById('variables-test');
            const computed = window.getComputedStyle(testElement);
            const bgColor = computed.backgroundColor;
            
            const statusElement = document.getElementById('variables-status');
            const computedElement = document.getElementById('variables-computed');
            
            if (bgColor === 'rgba(255, 255, 255, 0.7)' || bgColor === 'rgb(255, 255, 255)') {
                statusElement.textContent = '✅ CSS Variables Working';
                statusElement.style.color = '#00ff88';
            } else if (bgColor.includes('255, 0, 0') || bgColor === 'red') {
                statusElement.textContent = '❌ CSS Variables Not Loading (Fallback Red)';
                statusElement.style.color = '#ff4444';
            } else {
                statusElement.textContent = `⚠️ Unexpected Value: ${bgColor}`;
                statusElement.style.color = '#ffaa00';
            }
            
            computedElement.textContent = getComputedStylesText(testElement, [
                'background-color',
                '--glass-material-regular',
                '--glass-blur-regular',
                '--glass-border-regular'
            ]);
        }
        
        function debugElement(elementId, computedId, properties) {
            const element = document.getElementById(elementId);
            const computedElement = document.getElementById(computedId);
            
            if (element && computedElement) {
                computedElement.textContent = getComputedStylesText(element, properties);
            }
        }
        
        function checkCSSImports() {
            const statusElement = document.getElementById('css-status');
            const stylesheets = Array.from(document.styleSheets);
            
            let status = 'CSS Imports Status:\n\n';
            
            stylesheets.forEach((sheet, index) => {
                try {
                    status += `Stylesheet ${index + 1}: ${sheet.href || 'Inline'}\n`;
                    status += `Rules: ${sheet.cssRules ? sheet.cssRules.length : 'Cannot access'}\n\n`;
                } catch (e) {
                    status += `Stylesheet ${index + 1}: Error accessing rules\n\n`;
                }
            });
            
            // Check if glass classes exist
            const glassElement = document.querySelector('.glass');
            const glassBtn = document.querySelector('.glass-btn');
            const glassContainer = document.querySelector('.glass-container');
            
            status += 'Element Detection:\n';
            status += `Glass element: ${glassElement ? '✅ Found' : '❌ Not found'}\n`;
            status += `Glass button: ${glassBtn ? '✅ Found' : '❌ Not found'}\n`;
            status += `Glass container: ${glassContainer ? '✅ Found' : '❌ Not found'}\n`;
            
            statusElement.textContent = status;
        }
        
        // Run diagnostics when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkCSSVariables();
                
                debugElement('basic-glass', 'basic-glass-computed', [
                    'background',
                    'backdrop-filter',
                    'border',
                    'border-radius',
                    'box-shadow',
                    'position'
                ]);
                
                debugElement('glass-button', 'glass-button-computed', [
                    'background',
                    'backdrop-filter',
                    'border',
                    'border-radius',
                    'box-shadow',
                    'display'
                ]);
                
                debugElement('glass-container', 'glass-container-computed', [
                    'position',
                    'display',
                    'border-radius',
                    'overflow',
                    'box-shadow'
                ]);
                
                checkCSSImports();
            }, 500);
        });
    </script>
</body>
</html>
