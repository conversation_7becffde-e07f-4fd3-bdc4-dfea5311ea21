/* Liquid Glass UI - Glass Utilities */

/* Backdrop Filter Utilities */
.backdrop-blur-none {
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

.backdrop-blur-xs {
  backdrop-filter: blur(var(--glass-blur-xs));
  -webkit-backdrop-filter: blur(var(--glass-blur-xs));
}

.backdrop-blur-sm {
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
}

.backdrop-blur-md {
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
}

.backdrop-blur-lg {
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
}

.backdrop-blur-xl {
  backdrop-filter: blur(var(--glass-blur-xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-xl));
}

.backdrop-blur-2xl {
  backdrop-filter: blur(var(--glass-blur-2xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-2xl));
}

.backdrop-blur-3xl {
  backdrop-filter: blur(var(--glass-blur-3xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-3xl));
}

/* Glass Background Utilities */
.bg-glass-white-50 { background-color: var(--glass-white-50); }
.bg-glass-white-100 { background-color: var(--glass-white-100); }
.bg-glass-white-200 { background-color: var(--glass-white-200); }
.bg-glass-white-300 { background-color: var(--glass-white-300); }
.bg-glass-white-400 { background-color: var(--glass-white-400); }
.bg-glass-white-500 { background-color: var(--glass-white-500); }
.bg-glass-white-600 { background-color: var(--glass-white-600); }
.bg-glass-white-700 { background-color: var(--glass-white-700); }
.bg-glass-white-800 { background-color: var(--glass-white-800); }
.bg-glass-white-900 { background-color: var(--glass-white-900); }

.bg-glass-black-50 { background-color: var(--glass-black-50); }
.bg-glass-black-100 { background-color: var(--glass-black-100); }
.bg-glass-black-200 { background-color: var(--glass-black-200); }
.bg-glass-black-300 { background-color: var(--glass-black-300); }
.bg-glass-black-400 { background-color: var(--glass-black-400); }
.bg-glass-black-500 { background-color: var(--glass-black-500); }
.bg-glass-black-600 { background-color: var(--glass-black-600); }
.bg-glass-black-700 { background-color: var(--glass-black-700); }
.bg-glass-black-800 { background-color: var(--glass-black-800); }
.bg-glass-black-900 { background-color: var(--glass-black-900); }

/* Glass Border Utilities */
.border-glass-light { border-color: var(--glass-border-light); }
.border-glass-medium { border-color: var(--glass-border-medium); }
.border-glass-strong { border-color: var(--glass-border-strong); }
.border-glass-dark { border-color: var(--glass-border-dark); }
.border-glass-dark-medium { border-color: var(--glass-border-dark-medium); }
.border-glass-dark-strong { border-color: var(--glass-border-dark-strong); }

/* Glass Shadow Utilities */
.shadow-glass { box-shadow: var(--glass-shadow-md); }
.shadow-glass-sm { box-shadow: var(--glass-shadow-sm); }
.shadow-glass-lg { box-shadow: var(--glass-shadow-lg); }
.shadow-glass-xl { box-shadow: var(--glass-shadow-xl); }
.shadow-glass-2xl { box-shadow: var(--glass-shadow-2xl); }
.shadow-glass-inner { box-shadow: var(--glass-shadow-inner); }

/* Glass Z-Index Utilities */
.z-glass-base { z-index: var(--glass-z-base); }
.z-glass-raised { z-index: var(--glass-z-raised); }
.z-glass-overlay { z-index: var(--glass-z-overlay); }
.z-glass-modal { z-index: var(--glass-z-modal); }
.z-glass-popover { z-index: var(--glass-z-popover); }
.z-glass-tooltip { z-index: var(--glass-z-tooltip); }
.z-glass-toast { z-index: var(--glass-z-toast); }

/* Glass Spacing Utilities */
.p-glass-xs { padding: var(--glass-space-xs); }
.p-glass-sm { padding: var(--glass-space-sm); }
.p-glass-md { padding: var(--glass-space-md); }
.p-glass-lg { padding: var(--glass-space-lg); }
.p-glass-xl { padding: var(--glass-space-xl); }
.p-glass-2xl { padding: var(--glass-space-2xl); }
.p-glass-3xl { padding: var(--glass-space-3xl); }

.m-glass-xs { margin: var(--glass-space-xs); }
.m-glass-sm { margin: var(--glass-space-sm); }
.m-glass-md { margin: var(--glass-space-md); }
.m-glass-lg { margin: var(--glass-space-lg); }
.m-glass-xl { margin: var(--glass-space-xl); }
.m-glass-2xl { margin: var(--glass-space-2xl); }
.m-glass-3xl { margin: var(--glass-space-3xl); }

/* Glass Border Radius Utilities */
.rounded-glass-sm { border-radius: var(--glass-radius-sm); }
.rounded-glass-md { border-radius: var(--glass-radius-md); }
.rounded-glass-lg { border-radius: var(--glass-radius-lg); }
.rounded-glass-xl { border-radius: var(--glass-radius-xl); }
.rounded-glass-2xl { border-radius: var(--glass-radius-2xl); }
.rounded-glass-full { border-radius: var(--glass-radius-full); }

/* Glass Transition Utilities */
.transition-glass {
  transition: backdrop-filter var(--glass-duration-normal) var(--glass-ease-out),
              background-color var(--glass-duration-normal) var(--glass-ease-out),
              border-color var(--glass-duration-normal) var(--glass-ease-out),
              box-shadow var(--glass-duration-normal) var(--glass-ease-out);
}

.transition-glass-fast {
  transition: backdrop-filter var(--glass-duration-fast) var(--glass-ease-out),
              background-color var(--glass-duration-fast) var(--glass-ease-out),
              border-color var(--glass-duration-fast) var(--glass-ease-out),
              box-shadow var(--glass-duration-fast) var(--glass-ease-out);
}

.transition-glass-slow {
  transition: backdrop-filter var(--glass-duration-slow) var(--glass-ease-out),
              background-color var(--glass-duration-slow) var(--glass-ease-out),
              border-color var(--glass-duration-slow) var(--glass-ease-out),
              box-shadow var(--glass-duration-slow) var(--glass-ease-out);
}

/* Glass Performance Utilities */
.will-change-glass {
  will-change: backdrop-filter, transform, opacity;
}

.transform-gpu {
  transform: translateZ(0);
}

/* Glass Visibility Utilities */
.glass-visible {
  opacity: 1;
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
}

.glass-hidden {
  opacity: 0;
  backdrop-filter: blur(0);
  -webkit-backdrop-filter: blur(0);
}

/* Glass Interactive States */
.glass-interactive {
  cursor: pointer;
  transition: all var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-interactive:hover {
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow-lg);
}

.glass-interactive:active {
  transform: translateY(0);
  box-shadow: var(--glass-shadow-md);
}

/* Glass Disabled State */
.glass-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Glass Loading State */
.glass-loading {
  position: relative;
  pointer-events: none;
}

.glass-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Glass Utilities */
@media (max-width: 768px) {
  .glass-mobile-subtle {
    backdrop-filter: blur(var(--glass-blur-sm));
    -webkit-backdrop-filter: blur(var(--glass-blur-sm));
    background-color: var(--glass-white-900);
  }
  
  [data-theme="dark"] .glass-mobile-subtle {
    background-color: var(--glass-black-900);
  }
}

@media (min-width: 769px) {
  .glass-desktop-enhanced {
    backdrop-filter: blur(var(--glass-blur-lg)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--glass-blur-lg)) saturate(180%);
  }
}
