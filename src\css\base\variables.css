:root {
  /* Glass Effect Variables */
  --glass-blur-xs: 2px;
  --glass-blur-sm: 4px;
  --glass-blur-md: 8px;
  --glass-blur-lg: 12px;
  --glass-blur-xl: 16px;
  --glass-blur-2xl: 24px;
  --glass-blur-3xl: 32px;

  /* Glass Opacity Levels */
  --glass-opacity-subtle: 0.05;
  --glass-opacity-light: 0.1;
  --glass-opacity-medium: 0.15;
  --glass-opacity-strong: 0.2;
  --glass-opacity-intense: 0.3;

  /* Glass Colors - Light Theme */
  --glass-white-50: rgba(255, 255, 255, 0.9);
  --glass-white-100: rgba(255, 255, 255, 0.8);
  --glass-white-200: rgba(255, 255, 255, 0.7);
  --glass-white-300: rgba(255, 255, 255, 0.6);
  --glass-white-400: rgba(255, 255, 255, 0.5);
  --glass-white-500: rgba(255, 255, 255, 0.4);
  --glass-white-600: rgba(255, 255, 255, 0.3);
  --glass-white-700: rgba(255, 255, 255, 0.2);
  --glass-white-800: rgba(255, 255, 255, 0.1);
  --glass-white-900: rgba(255, 255, 255, 0.05);

  /* Glass Colors - Dark Theme */
  --glass-black-50: rgba(0, 0, 0, 0.9);
  --glass-black-100: rgba(0, 0, 0, 0.8);
  --glass-black-200: rgba(0, 0, 0, 0.7);
  --glass-black-300: rgba(0, 0, 0, 0.6);
  --glass-black-400: rgba(0, 0, 0, 0.5);
  --glass-black-500: rgba(0, 0, 0, 0.4);
  --glass-black-600: rgba(0, 0, 0, 0.3);
  --glass-black-700: rgba(0, 0, 0, 0.2);
  --glass-black-800: rgba(0, 0, 0, 0.1);
  --glass-black-900: rgba(0, 0, 0, 0.05);

  /* Border Colors */
  --glass-border-light: rgba(255, 255, 255, 0.2);
  --glass-border-medium: rgba(255, 255, 255, 0.3);
  --glass-border-strong: rgba(255, 255, 255, 0.4);
  --glass-border-dark: rgba(0, 0, 0, 0.1);
  --glass-border-dark-medium: rgba(0, 0, 0, 0.2);
  --glass-border-dark-strong: rgba(0, 0, 0, 0.3);

  /* Shadow Variables */
  --glass-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --glass-shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
  --glass-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  --glass-shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.2);
  --glass-shadow-2xl: 0 24px 96px rgba(0, 0, 0, 0.25);
  --glass-shadow-inner: inset 0 1px 0 rgba(255, 255, 255, 0.2);

  /* Animation Durations */
  --glass-duration-fast: 150ms;
  --glass-duration-normal: 250ms;
  --glass-duration-slow: 350ms;
  --glass-duration-slower: 500ms;

  /* Animation Easings */
  --glass-ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --glass-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --glass-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --glass-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Spacing Scale */
  --glass-space-xs: 0.25rem;
  --glass-space-sm: 0.5rem;
  --glass-space-md: 1rem;
  --glass-space-lg: 1.5rem;
  --glass-space-xl: 2rem;
  --glass-space-2xl: 3rem;
  --glass-space-3xl: 4rem;

  /* Border Radius */
  --glass-radius-sm: 0.375rem;
  --glass-radius-md: 0.5rem;
  --glass-radius-lg: 0.75rem;
  --glass-radius-xl: 1rem;
  --glass-radius-2xl: 1.5rem;
  --glass-radius-full: 9999px;

  /* Z-Index Scale */
  --glass-z-base: 0;
  --glass-z-raised: 10;
  --glass-z-overlay: 20;
  --glass-z-modal: 30;
  --glass-z-popover: 40;
  --glass-z-tooltip: 50;
  --glass-z-toast: 60;
}

/* Dark Theme Overrides */
[data-theme="dark"] {
  --glass-border-light: rgba(255, 255, 255, 0.1);
  --glass-border-medium: rgba(255, 255, 255, 0.15);
  --glass-border-strong: rgba(255, 255, 255, 0.2);
  --glass-shadow-inner: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  :root {
    --glass-duration-fast: 0ms;
    --glass-duration-normal: 0ms;
    --glass-duration-slow: 0ms;
    --glass-duration-slower: 0ms;
  }
}
