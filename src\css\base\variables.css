:root {
  /* Apple Material Vibrancy Effects */
  --glass-vibrancy-ultra-thin: rgba(255, 255, 255, 0.9);
  --glass-vibrancy-thin: rgba(255, 255, 255, 0.8);
  --glass-vibrancy-regular: rgba(255, 255, 255, 0.7);
  --glass-vibrancy-thick: rgba(255, 255, 255, 0.6);
  --glass-vibrancy-heavy: rgba(255, 255, 255, 0.5);
  --glass-vibrancy-ultra-heavy: rgba(255, 255, 255, 0.4);

  /* Apple Blur Levels (More Subtle & Realistic) */
  --glass-blur-ultra: 1px;
  --glass-blur-thin: 3px;
  --glass-blur-regular: 6px;
  --glass-blur-thick: 10px;
  --glass-blur-heavy: 15px;
  --glass-blur-ultra-heavy: 25px;

  /* Apple Material Backgrounds (Light) */
  --glass-material-ultra-thin: rgba(255, 255, 255, 0.9);
  --glass-material-thin: rgba(255, 255, 255, 0.8);
  --glass-material-regular: rgba(255, 255, 255, 0.7);
  --glass-material-thick: rgba(255, 255, 255, 0.6);
  --glass-material-heavy: rgba(255, 255, 255, 0.5);
  --glass-material-ultra-heavy: rgba(255, 255, 255, 0.4);

  /* Apple Material Backgrounds (Dark) */
  --glass-material-dark-ultra-thin: rgba(28, 28, 30, 0.9);
  --glass-material-dark-thin: rgba(28, 28, 30, 0.8);
  --glass-material-dark-regular: rgba(28, 28, 30, 0.7);
  --glass-material-dark-thick: rgba(28, 28, 30, 0.6);
  --glass-material-dark-heavy: rgba(28, 28, 30, 0.5);
  --glass-material-dark-ultra-heavy: rgba(28, 28, 30, 0.4);

  /* Apple Ultra-Thin Borders (0.5px) */
  --glass-border-ultra-thin: 0.5px solid rgba(255, 255, 255, 0.3);
  --glass-border-thin: 0.5px solid rgba(255, 255, 255, 0.4);
  --glass-border-regular: 0.5px solid rgba(255, 255, 255, 0.5);
  --glass-border-thick: 1px solid rgba(255, 255, 255, 0.6);

  /* Apple Dark Borders */
  --glass-border-dark-ultra-thin: 0.5px solid rgba(255, 255, 255, 0.1);
  --glass-border-dark-thin: 0.5px solid rgba(255, 255, 255, 0.15);
  --glass-border-dark-regular: 0.5px solid rgba(255, 255, 255, 0.2);
  --glass-border-dark-thick: 1px solid rgba(255, 255, 255, 0.25);

  /* Apple Layered Shadows (Multiple Layers for Depth) */
  --glass-shadow-ultra-thin:
    0 0.5px 1px rgba(0, 0, 0, 0.04),
    0 0.5px 2px rgba(0, 0, 0, 0.02);
  --glass-shadow-thin:
    0 1px 3px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.1);
  --glass-shadow-regular:
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.06),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.15);
  --glass-shadow-thick:
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.2);
  --glass-shadow-heavy:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.25);

  /* Apple Reflection Highlights */
  --glass-highlight-top: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  --glass-highlight-edge: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 30%);
  --glass-highlight-inner: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);

  /* Apple Noise Texture (Subtle Grain) */
  --glass-noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.03'/%3E%3C/svg%3E");

  /* Apple Border Radius (More Rounded) */
  --glass-radius-ultra-small: 0.25rem;
  --glass-radius-small: 0.5rem;
  --glass-radius-medium: 0.75rem;
  --glass-radius-large: 1rem;
  --glass-radius-extra-large: 1.25rem;
  --glass-radius-ultra-large: 1.5rem;
  --glass-radius-pill: 50rem;

  /* Apple Animation Curves (More Natural) */
  --glass-ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --glass-ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --glass-ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --glass-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Apple Animation Durations */
  --glass-duration-instant: 100ms;
  --glass-duration-fast: 200ms;
  --glass-duration-normal: 300ms;
  --glass-duration-slow: 500ms;
  --glass-duration-slower: 700ms;

  /* Apple Spacing Scale */
  --glass-space-ultra-small: 0.125rem;
  --glass-space-small: 0.25rem;
  --glass-space-medium: 0.5rem;
  --glass-space-large: 1rem;
  --glass-space-extra-large: 1.5rem;
  --glass-space-ultra-large: 2rem;

  /* Apple Z-Index Scale */
  --glass-z-base: 0;
  --glass-z-raised: 10;
  --glass-z-overlay: 20;
  --glass-z-modal: 30;
  --glass-z-popover: 40;
  --glass-z-tooltip: 50;
}

/* Apple Dark Theme Material Overrides */
[data-theme="dark"] {
  /* Use dark materials */
  --glass-material-ultra-thin: var(--glass-material-dark-ultra-thin);
  --glass-material-thin: var(--glass-material-dark-thin);
  --glass-material-regular: var(--glass-material-dark-regular);
  --glass-material-thick: var(--glass-material-dark-thick);
  --glass-material-heavy: var(--glass-material-dark-heavy);
  --glass-material-ultra-heavy: var(--glass-material-dark-ultra-heavy);

  /* Dark borders */
  --glass-border-ultra-thin: var(--glass-border-dark-ultra-thin);
  --glass-border-thin: var(--glass-border-dark-thin);
  --glass-border-regular: var(--glass-border-dark-regular);
  --glass-border-thick: var(--glass-border-dark-thick);

  /* Dark highlights */
  --glass-highlight-top: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  --glass-highlight-edge: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, transparent 30%);
  --glass-highlight-inner: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
}

/* Apple Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  :root {
    --glass-duration-instant: 0ms;
    --glass-duration-fast: 0ms;
    --glass-duration-normal: 0ms;
    --glass-duration-slow: 0ms;
    --glass-duration-slower: 0ms;
  }
}

/* Apple High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --glass-border-ultra-thin: 1px solid rgba(255, 255, 255, 0.8);
    --glass-border-thin: 1px solid rgba(255, 255, 255, 0.9);
    --glass-border-regular: 2px solid rgba(255, 255, 255, 1);
  }
}
