{"version": 3, "file": "index.umd.js", "sources": ["../src/js/effects/glass-effects.js", "../src/js/effects/cursor-effects.js", "../src/js/effects/motion-effects.js", "../src/js/components/button.js", "../src/js/components/card.js", "../src/js/components/modal.js", "../src/js/components/navigation.js", "../src/js/utils/theme-manager.js", "../src/js/utils/device-detector.js", "../src/index.js"], "sourcesContent": ["/**\n * Glass Effects\n * Core glass effect management and utilities\n */\n\nexport class GlassEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableAnimations: true,\n      enableInteractions: true,\n      performanceMode: 'auto', // 'low', 'medium', 'high', 'auto'\n      ...options\n    }\n\n    this.elements = new Map()\n    this.observers = new Map()\n    this.isInitialized = false\n  }\n\n  /**\n   * Initialize glass effects system\n   */\n  init() {\n    if (this.isInitialized) return\n\n    this.detectPerformanceCapabilities()\n    this.setupIntersectionObserver()\n    this.bindGlobalEvents()\n    this.scanForElements()\n    \n    this.isInitialized = true\n  }\n\n  /**\n   * Detect device performance capabilities\n   */\n  detectPerformanceCapabilities() {\n    if (this.options.performanceMode !== 'auto') return\n\n    const deviceMemory = navigator.deviceMemory || 4\n    const hardwareConcurrency = navigator.hardwareConcurrency || 4\n    const connection = navigator.connection\n\n    let performanceScore = 0\n\n    // Memory score (0-3)\n    if (deviceMemory >= 8) performanceScore += 3\n    else if (deviceMemory >= 4) performanceScore += 2\n    else if (deviceMemory >= 2) performanceScore += 1\n\n    // CPU score (0-3)\n    if (hardwareConcurrency >= 8) performanceScore += 3\n    else if (hardwareConcurrency >= 4) performanceScore += 2\n    else if (hardwareConcurrency >= 2) performanceScore += 1\n\n    // Connection score (0-2)\n    if (connection) {\n      if (connection.effectiveType === '4g') performanceScore += 2\n      else if (connection.effectiveType === '3g') performanceScore += 1\n    } else {\n      performanceScore += 2 // Assume good connection if unknown\n    }\n\n    // Set performance mode based on score\n    if (performanceScore >= 6) this.options.performanceMode = 'high'\n    else if (performanceScore >= 4) this.options.performanceMode = 'medium'\n    else this.options.performanceMode = 'low'\n\n    // Add performance class to document\n    document.documentElement.classList.add(`performance-${this.options.performanceMode}`)\n  }\n\n  /**\n   * Setup intersection observer for performance optimization\n   */\n  setupIntersectionObserver() {\n    if (!('IntersectionObserver' in window)) return\n\n    this.intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          const element = entry.target\n          const effectData = this.elements.get(element)\n          \n          if (!effectData) return\n\n          if (entry.isIntersecting) {\n            this.activateElement(element, effectData)\n          } else {\n            this.deactivateElement(element, effectData)\n          }\n        })\n      },\n      {\n        rootMargin: '50px',\n        threshold: 0.1\n      }\n    )\n  }\n\n  /**\n   * Bind global events\n   */\n  bindGlobalEvents() {\n    // Handle visibility change for performance\n    document.addEventListener('visibilitychange', () => {\n      if (document.hidden) {\n        this.pauseAllEffects()\n      } else {\n        this.resumeAllEffects()\n      }\n    })\n\n    // Handle resize events\n    let resizeTimeout\n    window.addEventListener('resize', () => {\n      clearTimeout(resizeTimeout)\n      resizeTimeout = setTimeout(() => {\n        this.updateAllElements()\n      }, 100)\n    })\n  }\n\n  /**\n   * Scan for elements with glass effects\n   */\n  scanForElements() {\n    const selectors = [\n      '[data-glass-effect]',\n      '.glass',\n      '.glass-subtle',\n      '.glass-light',\n      '.glass-medium',\n      '.glass-strong',\n      '.glass-intense'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to glass effects system\n   */\n  addElement(element, options = {}) {\n    if (this.elements.has(element)) return\n\n    const effectData = {\n      element,\n      options: { ...this.getDefaultOptions(), ...options },\n      isActive: false,\n      animations: new Set(),\n      observers: new Set()\n    }\n\n    this.elements.set(element, effectData)\n    \n    // Add to intersection observer\n    if (this.intersectionObserver) {\n      this.intersectionObserver.observe(element)\n    } else {\n      this.activateElement(element, effectData)\n    }\n\n    // Initialize element\n    this.initializeElement(element, effectData)\n  }\n\n  /**\n   * Get default options based on performance mode\n   */\n  getDefaultOptions() {\n    const baseOptions = {\n      enableBlur: true,\n      enableShadows: true,\n      enableAnimations: this.options.enableAnimations,\n      enableInteractions: this.options.enableInteractions\n    }\n\n    switch (this.options.performanceMode) {\n      case 'low':\n        return {\n          ...baseOptions,\n          enableBlur: false,\n          enableAnimations: false,\n          maxBlur: 4\n        }\n      case 'medium':\n        return {\n          ...baseOptions,\n          maxBlur: 8\n        }\n      case 'high':\n        return {\n          ...baseOptions,\n          maxBlur: 16\n        }\n      default:\n        return baseOptions\n    }\n  }\n\n  /**\n   * Initialize element with glass effects\n   */\n  initializeElement(element, effectData) {\n    const { options } = effectData\n\n    // Add base glass classes\n    element.classList.add('glass-element')\n    \n    // Add performance-specific classes\n    element.classList.add(`glass-performance-${this.options.performanceMode}`)\n\n    // Set up CSS custom properties\n    this.updateElementProperties(element, effectData)\n\n    // Add interaction listeners if enabled\n    if (options.enableInteractions) {\n      this.addInteractionListeners(element, effectData)\n    }\n  }\n\n  /**\n   * Update element CSS custom properties\n   */\n  updateElementProperties(element, effectData) {\n    const { options } = effectData\n    const rect = element.getBoundingClientRect()\n\n    // Set size properties\n    element.style.setProperty('--element-width', `${rect.width}px`)\n    element.style.setProperty('--element-height', `${rect.height}px`)\n\n    // Set performance properties\n    element.style.setProperty('--max-blur', `${options.maxBlur || 8}px`)\n    element.style.setProperty('--enable-animations', options.enableAnimations ? '1' : '0')\n  }\n\n  /**\n   * Add interaction listeners to element\n   */\n  addInteractionListeners(element, effectData) {\n    const listeners = {\n      mouseenter: () => this.handleElementHover(element, effectData, true),\n      mouseleave: () => this.handleElementHover(element, effectData, false),\n      focus: () => this.handleElementFocus(element, effectData, true),\n      blur: () => this.handleElementFocus(element, effectData, false),\n      click: (event) => this.handleElementClick(element, effectData, event)\n    }\n\n    Object.entries(listeners).forEach(([event, handler]) => {\n      element.addEventListener(event, handler)\n      effectData.observers.add(() => {\n        element.removeEventListener(event, handler)\n      })\n    })\n  }\n\n  /**\n   * Handle element hover\n   */\n  handleElementHover(element, effectData, isHovering) {\n    if (!effectData.options.enableInteractions) return\n\n    element.classList.toggle('glass-hover', isHovering)\n    \n    if (isHovering) {\n      this.addAnimation(element, 'hover-in')\n    } else {\n      this.addAnimation(element, 'hover-out')\n    }\n  }\n\n  /**\n   * Handle element focus\n   */\n  handleElementFocus(element, effectData, isFocused) {\n    if (!effectData.options.enableInteractions) return\n\n    element.classList.toggle('glass-focus', isFocused)\n  }\n\n  /**\n   * Handle element click\n   */\n  handleElementClick(element, effectData, event) {\n    if (!effectData.options.enableInteractions) return\n\n    this.createRippleEffect(element, event)\n    this.addAnimation(element, 'click')\n  }\n\n  /**\n   * Create ripple effect\n   */\n  createRippleEffect(element, event) {\n    const rect = element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'glass-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.3);\n      transform: translate(-50%, -50%);\n      animation: glass-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1000;\n    `\n\n    element.style.position = 'relative'\n    element.appendChild(ripple)\n\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    }, 600)\n  }\n\n  /**\n   * Add animation to element\n   */\n  addAnimation(element, animationType) {\n    const effectData = this.elements.get(element)\n    if (!effectData || !effectData.options.enableAnimations) return\n\n    effectData.animations.add(animationType)\n    element.classList.add(`glass-animate-${animationType}`)\n\n    setTimeout(() => {\n      effectData.animations.delete(animationType)\n      element.classList.remove(`glass-animate-${animationType}`)\n    }, 300)\n  }\n\n  /**\n   * Activate element effects\n   */\n  activateElement(element, effectData) {\n    if (effectData.isActive) return\n\n    effectData.isActive = true\n    element.classList.add('glass-active')\n  }\n\n  /**\n   * Deactivate element effects\n   */\n  deactivateElement(element, effectData) {\n    if (!effectData.isActive) return\n\n    effectData.isActive = false\n    element.classList.remove('glass-active')\n  }\n\n  /**\n   * Remove element from glass effects system\n   */\n  removeElement(element) {\n    const effectData = this.elements.get(element)\n    if (!effectData) return\n\n    // Remove from intersection observer\n    if (this.intersectionObserver) {\n      this.intersectionObserver.unobserve(element)\n    }\n\n    // Clean up observers\n    effectData.observers.forEach(cleanup => cleanup())\n\n    // Remove classes\n    element.classList.remove('glass-element', 'glass-active', 'glass-hover', 'glass-focus')\n\n    // Remove from elements map\n    this.elements.delete(element)\n  }\n\n  /**\n   * Update all elements\n   */\n  updateAllElements() {\n    this.elements.forEach((effectData, element) => {\n      this.updateElementProperties(element, effectData)\n    })\n  }\n\n  /**\n   * Pause all effects\n   */\n  pauseAllEffects() {\n    this.elements.forEach((effectData, element) => {\n      element.classList.add('glass-paused')\n    })\n  }\n\n  /**\n   * Resume all effects\n   */\n  resumeAllEffects() {\n    this.elements.forEach((effectData, element) => {\n      element.classList.remove('glass-paused')\n    })\n  }\n\n  /**\n   * Destroy glass effects system\n   */\n  destroy() {\n    // Clean up all elements\n    this.elements.forEach((effectData, element) => {\n      this.removeElement(element)\n    })\n\n    // Clean up observers\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect()\n    }\n\n    this.elements.clear()\n    this.observers.clear()\n    this.isInitialized = false\n  }\n}\n", "/**\n * Cursor Effects\n * Creates dynamic cursor-based shadow and lighting effects for desktop users\n */\n\nexport class CursorEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableShadows: true,\n      enableLighting: true,\n      shadowIntensity: 0.3,\n      lightingIntensity: 0.2,\n      maxDistance: 200,\n      smoothing: 0.1,\n      ...options\n    }\n\n    this.cursor = { x: 0, y: 0 }\n    this.smoothCursor = { x: 0, y: 0 }\n    this.elements = new Set()\n    this.isActive = false\n    this.animationFrame = null\n  }\n\n  /**\n   * Initialize cursor effects\n   */\n  init() {\n    if (this.isActive) return\n\n    this.isActive = true\n    this.bindEvents()\n    this.findElements()\n    this.startAnimation()\n  }\n\n  /**\n   * Bind mouse events\n   */\n  bindEvents() {\n    this.handleMouseMove = this.handleMouseMove.bind(this)\n    this.handleMouseLeave = this.handleMouseLeave.bind(this)\n\n    document.addEventListener('mousemove', this.handleMouseMove)\n    document.addEventListener('mouseleave', this.handleMouseLeave)\n  }\n\n  /**\n   * Find elements with cursor effects\n   */\n  findElements() {\n    const selectors = [\n      '[data-cursor-effect]',\n      '.glass-cursor-effect',\n      '.glass',\n      '.glass-button',\n      '.glass-card'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to cursor effects\n   */\n  addElement(element) {\n    if (this.elements.has(element)) return\n\n    this.elements.add(element)\n    \n    // Add CSS custom properties for cursor effects\n    element.style.setProperty('--cursor-x', '0')\n    element.style.setProperty('--cursor-y', '0')\n    element.style.setProperty('--cursor-distance', '1')\n    element.style.setProperty('--cursor-intensity', '0')\n\n    // Add cursor effect class\n    element.classList.add('has-cursor-effect')\n  }\n\n  /**\n   * Remove element from cursor effects\n   */\n  removeElement(element) {\n    if (!this.elements.has(element)) return\n\n    this.elements.delete(element)\n    element.classList.remove('has-cursor-effect')\n    \n    // Reset CSS custom properties\n    element.style.removeProperty('--cursor-x')\n    element.style.removeProperty('--cursor-y')\n    element.style.removeProperty('--cursor-distance')\n    element.style.removeProperty('--cursor-intensity')\n  }\n\n  /**\n   * Handle mouse move\n   */\n  handleMouseMove(event) {\n    this.cursor.x = event.clientX\n    this.cursor.y = event.clientY\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave() {\n    this.cursor.x = -1000\n    this.cursor.y = -1000\n  }\n\n  /**\n   * Start animation loop\n   */\n  startAnimation() {\n    const animate = () => {\n      if (!this.isActive) return\n\n      this.updateCursor()\n      this.updateElements()\n      \n      this.animationFrame = requestAnimationFrame(animate)\n    }\n\n    animate()\n  }\n\n  /**\n   * Update smooth cursor position\n   */\n  updateCursor() {\n    this.smoothCursor.x += (this.cursor.x - this.smoothCursor.x) * this.options.smoothing\n    this.smoothCursor.y += (this.cursor.y - this.smoothCursor.y) * this.options.smoothing\n  }\n\n  /**\n   * Update all elements with cursor effects\n   */\n  updateElements() {\n    this.elements.forEach(element => {\n      this.updateElement(element)\n    })\n  }\n\n  /**\n   * Update individual element\n   */\n  updateElement(element) {\n    const rect = element.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n\n    // Calculate distance from cursor to element center\n    const deltaX = this.smoothCursor.x - centerX\n    const deltaY = this.smoothCursor.y - centerY\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)\n\n    // Calculate normalized values\n    const normalizedDistance = Math.min(distance / this.options.maxDistance, 1)\n    const intensity = 1 - normalizedDistance\n\n    // Calculate relative position within element\n    const relativeX = (this.smoothCursor.x - rect.left) / rect.width\n    const relativeY = (this.smoothCursor.y - rect.top) / rect.height\n\n    // Update CSS custom properties\n    element.style.setProperty('--cursor-x', relativeX.toFixed(3))\n    element.style.setProperty('--cursor-y', relativeY.toFixed(3))\n    element.style.setProperty('--cursor-distance', normalizedDistance.toFixed(3))\n    element.style.setProperty('--cursor-intensity', intensity.toFixed(3))\n\n    // Apply shadow effects\n    if (this.options.enableShadows) {\n      this.applyShadowEffect(element, deltaX, deltaY, intensity)\n    }\n\n    // Apply lighting effects\n    if (this.options.enableLighting) {\n      this.applyLightingEffect(element, relativeX, relativeY, intensity)\n    }\n  }\n\n  /**\n   * Apply shadow effect based on cursor position\n   */\n  applyShadowEffect(element, deltaX, deltaY, intensity) {\n    const shadowIntensity = intensity * this.options.shadowIntensity\n    const shadowX = -deltaX * 0.1 * shadowIntensity\n    const shadowY = -deltaY * 0.1 * shadowIntensity\n    const shadowBlur = 20 * shadowIntensity\n    const shadowOpacity = 0.3 * shadowIntensity\n\n    const shadow = `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, ${shadowOpacity})`\n    element.style.setProperty('--cursor-shadow', shadow)\n  }\n\n  /**\n   * Apply lighting effect based on cursor position\n   */\n  applyLightingEffect(element, relativeX, relativeY, intensity) {\n    const lightIntensity = intensity * this.options.lightingIntensity\n    \n    // Create radial gradient for lighting effect\n    const gradientX = relativeX * 100\n    const gradientY = relativeY * 100\n    const gradientSize = 150 * intensity\n    \n    const gradient = `radial-gradient(${gradientSize}px circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`\n    element.style.setProperty('--cursor-light', gradient)\n  }\n\n  /**\n   * Create ripple effect at cursor position\n   */\n  createRipple(element, event) {\n    const rect = element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'cursor-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.3);\n      transform: translate(-50%, -50%);\n      animation: cursor-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1000;\n    `\n\n    element.style.position = 'relative'\n    element.appendChild(ripple)\n\n    // Remove ripple after animation\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    }, 600)\n  }\n\n  /**\n   * Add ripple effect to element\n   */\n  addRippleEffect(element) {\n    const handleClick = (event) => {\n      this.createRipple(element, event)\n    }\n\n    element.addEventListener('click', handleClick)\n    \n    return () => {\n      element.removeEventListener('click', handleClick)\n    }\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Pause cursor effects\n   */\n  pause() {\n    this.isActive = false\n    if (this.animationFrame) {\n      cancelAnimationFrame(this.animationFrame)\n      this.animationFrame = null\n    }\n  }\n\n  /**\n   * Resume cursor effects\n   */\n  resume() {\n    if (!this.isActive) {\n      this.isActive = true\n      this.startAnimation()\n    }\n  }\n\n  /**\n   * Destroy cursor effects\n   */\n  destroy() {\n    this.pause()\n    \n    // Remove event listeners\n    document.removeEventListener('mousemove', this.handleMouseMove)\n    document.removeEventListener('mouseleave', this.handleMouseLeave)\n\n    // Clean up elements\n    this.elements.forEach(element => {\n      this.removeElement(element)\n    })\n    \n    this.elements.clear()\n  }\n\n  /**\n   * Get cursor position\n   */\n  getCursorPosition() {\n    return { ...this.smoothCursor }\n  }\n\n  /**\n   * Check if cursor effects are active\n   */\n  isEnabled() {\n    return this.isActive\n  }\n}\n", "/**\n * Motion Effects\n * Creates device orientation-based effects for mobile devices\n */\n\nexport class MotionEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableTilt: true,\n      enableParallax: true,\n      enableLighting: true,\n      sensitivity: 1,\n      maxTilt: 15,\n      smoothing: 0.1,\n      ...options\n    }\n\n    this.orientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.elements = new Set()\n    this.isActive = false\n    this.animationFrame = null\n    this.permissionGranted = false\n  }\n\n  /**\n   * Initialize motion effects\n   */\n  async init() {\n    if (this.isActive) return\n\n    // Check if device orientation is supported\n    if (!this.isSupported()) {\n      console.warn('Device orientation not supported')\n      return false\n    }\n\n    // Request permission for iOS 13+\n    if (typeof DeviceOrientationEvent.requestPermission === 'function') {\n      try {\n        const permission = await DeviceOrientationEvent.requestPermission()\n        this.permissionGranted = permission === 'granted'\n      } catch (error) {\n        console.warn('Device orientation permission denied:', error)\n        return false\n      }\n    } else {\n      this.permissionGranted = true\n    }\n\n    if (!this.permissionGranted) {\n      return false\n    }\n\n    this.isActive = true\n    this.bindEvents()\n    this.findElements()\n    this.startAnimation()\n    \n    return true\n  }\n\n  /**\n   * Check if device orientation is supported\n   */\n  isSupported() {\n    return 'DeviceOrientationEvent' in window\n  }\n\n  /**\n   * Bind device orientation events\n   */\n  bindEvents() {\n    this.handleDeviceOrientation = this.handleDeviceOrientation.bind(this)\n    window.addEventListener('deviceorientation', this.handleDeviceOrientation)\n  }\n\n  /**\n   * Find elements with motion effects\n   */\n  findElements() {\n    const selectors = [\n      '[data-motion-effect]',\n      '.glass-motion-effect',\n      '.motion-tilt',\n      '.motion-parallax'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to motion effects\n   */\n  addElement(element) {\n    if (this.elements.has(element)) return\n\n    this.elements.add(element)\n    \n    // Add CSS custom properties for motion effects\n    element.style.setProperty('--motion-x', '0')\n    element.style.setProperty('--motion-y', '0')\n    element.style.setProperty('--motion-z', '0')\n    element.style.setProperty('--motion-intensity', '0')\n\n    // Add motion effect class\n    element.classList.add('has-motion-effect')\n  }\n\n  /**\n   * Remove element from motion effects\n   */\n  removeElement(element) {\n    if (!this.elements.has(element)) return\n\n    this.elements.delete(element)\n    element.classList.remove('has-motion-effect')\n    \n    // Reset CSS custom properties\n    element.style.removeProperty('--motion-x')\n    element.style.removeProperty('--motion-y')\n    element.style.removeProperty('--motion-z')\n    element.style.removeProperty('--motion-intensity')\n  }\n\n  /**\n   * Handle device orientation change\n   */\n  handleDeviceOrientation(event) {\n    this.orientation.alpha = event.alpha || 0  // Z axis (0-360)\n    this.orientation.beta = event.beta || 0    // X axis (-180 to 180)\n    this.orientation.gamma = event.gamma || 0  // Y axis (-90 to 90)\n  }\n\n  /**\n   * Start animation loop\n   */\n  startAnimation() {\n    const animate = () => {\n      if (!this.isActive) return\n\n      this.updateOrientation()\n      this.updateElements()\n      \n      this.animationFrame = requestAnimationFrame(animate)\n    }\n\n    animate()\n  }\n\n  /**\n   * Update smooth orientation values\n   */\n  updateOrientation() {\n    this.smoothOrientation.alpha += (this.orientation.alpha - this.smoothOrientation.alpha) * this.options.smoothing\n    this.smoothOrientation.beta += (this.orientation.beta - this.smoothOrientation.beta) * this.options.smoothing\n    this.smoothOrientation.gamma += (this.orientation.gamma - this.smoothOrientation.gamma) * this.options.smoothing\n  }\n\n  /**\n   * Update all elements with motion effects\n   */\n  updateElements() {\n    this.elements.forEach(element => {\n      this.updateElement(element)\n    })\n  }\n\n  /**\n   * Update individual element\n   */\n  updateElement(element) {\n    const { beta, gamma } = this.smoothOrientation\n    \n    // Normalize orientation values\n    const normalizedX = this.clamp(gamma / 90, -1, 1) * this.options.sensitivity\n    const normalizedY = this.clamp(beta / 180, -1, 1) * this.options.sensitivity\n    const intensity = Math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY)\n\n    // Update CSS custom properties\n    element.style.setProperty('--motion-x', normalizedX.toFixed(3))\n    element.style.setProperty('--motion-y', normalizedY.toFixed(3))\n    element.style.setProperty('--motion-intensity', Math.min(intensity, 1).toFixed(3))\n\n    // Apply specific effects\n    if (this.options.enableTilt) {\n      this.applyTiltEffect(element, normalizedX, normalizedY)\n    }\n\n    if (this.options.enableParallax) {\n      this.applyParallaxEffect(element, normalizedX, normalizedY)\n    }\n\n    if (this.options.enableLighting) {\n      this.applyLightingEffect(element, normalizedX, normalizedY, intensity)\n    }\n  }\n\n  /**\n   * Apply tilt effect based on device orientation\n   */\n  applyTiltEffect(element, x, y) {\n    const tiltX = y * this.options.maxTilt\n    const tiltY = -x * this.options.maxTilt\n    \n    element.style.setProperty('--motion-tilt-x', `${tiltX}deg`)\n    element.style.setProperty('--motion-tilt-y', `${tiltY}deg`)\n  }\n\n  /**\n   * Apply parallax effect based on device orientation\n   */\n  applyParallaxEffect(element, x, y) {\n    const parallaxX = x * 20\n    const parallaxY = y * 20\n    \n    element.style.setProperty('--motion-parallax-x', `${parallaxX}px`)\n    element.style.setProperty('--motion-parallax-y', `${parallaxY}px`)\n  }\n\n  /**\n   * Apply lighting effect based on device orientation\n   */\n  applyLightingEffect(element, x, y, intensity) {\n    // Create gradient based on tilt direction\n    const gradientX = (x + 1) * 50  // Convert -1,1 to 0,100\n    const gradientY = (y + 1) * 50  // Convert -1,1 to 0,100\n    const lightIntensity = intensity * 0.3\n    \n    const gradient = `radial-gradient(circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`\n    element.style.setProperty('--motion-light', gradient)\n  }\n\n  /**\n   * Create shake effect\n   */\n  createShake(element, intensity = 1) {\n    const shakeClass = `motion-shake-${Math.floor(intensity * 3) + 1}`\n    element.classList.add(shakeClass)\n    \n    setTimeout(() => {\n      element.classList.remove(shakeClass)\n    }, 500)\n  }\n\n  /**\n   * Clamp value between min and max\n   */\n  clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max)\n  }\n\n  /**\n   * Request permission for device orientation (iOS 13+)\n   */\n  async requestPermission() {\n    if (typeof DeviceOrientationEvent.requestPermission === 'function') {\n      try {\n        const permission = await DeviceOrientationEvent.requestPermission()\n        this.permissionGranted = permission === 'granted'\n        return this.permissionGranted\n      } catch (error) {\n        console.error('Error requesting device orientation permission:', error)\n        return false\n      }\n    }\n    \n    this.permissionGranted = true\n    return true\n  }\n\n  /**\n   * Calibrate device orientation\n   */\n  calibrate() {\n    // Reset orientation baseline\n    this.orientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Pause motion effects\n   */\n  pause() {\n    this.isActive = false\n    if (this.animationFrame) {\n      cancelAnimationFrame(this.animationFrame)\n      this.animationFrame = null\n    }\n  }\n\n  /**\n   * Resume motion effects\n   */\n  resume() {\n    if (!this.isActive && this.permissionGranted) {\n      this.isActive = true\n      this.startAnimation()\n    }\n  }\n\n  /**\n   * Destroy motion effects\n   */\n  destroy() {\n    this.pause()\n    \n    // Remove event listeners\n    window.removeEventListener('deviceorientation', this.handleDeviceOrientation)\n\n    // Clean up elements\n    this.elements.forEach(element => {\n      this.removeElement(element)\n    })\n    \n    this.elements.clear()\n  }\n\n  /**\n   * Get current orientation\n   */\n  getOrientation() {\n    return { ...this.smoothOrientation }\n  }\n\n  /**\n   * Check if motion effects are active\n   */\n  isEnabled() {\n    return this.isActive && this.permissionGranted\n  }\n}\n", "/**\n * Glass Button Component\n * Enhanced button with glass effects and interactions\n */\n\nexport class GlassButton {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      enableRipple: true,\n      enableHover: true,\n      enableFocus: true,\n      enableLoading: true,\n      rippleColor: 'rgba(255, 255, 255, 0.3)',\n      ...options\n    }\n\n    this.isLoading = false\n    this.ripples = new Set()\n    \n    this.init()\n  }\n\n  /**\n   * Initialize button component\n   */\n  init() {\n    this.setupElement()\n    this.bindEvents()\n    this.setupAccessibility()\n  }\n\n  /**\n   * Setup button element\n   */\n  setupElement() {\n    // Add base glass button class if not present\n    if (!this.element.classList.contains('glass-btn')) {\n      this.element.classList.add('glass-btn')\n    }\n\n    // Add component identifier\n    this.element.setAttribute('data-glass-button', 'initialized')\n\n    // Setup initial state\n    this.updateState()\n  }\n\n  /**\n   * Bind event listeners\n   */\n  bindEvents() {\n    // Click events\n    this.element.addEventListener('click', this.handleClick.bind(this))\n    \n    // Mouse events for hover effects\n    if (this.options.enableHover) {\n      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))\n      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))\n    }\n\n    // Focus events\n    if (this.options.enableFocus) {\n      this.element.addEventListener('focus', this.handleFocus.bind(this))\n      this.element.addEventListener('blur', this.handleBlur.bind(this))\n    }\n\n    // Keyboard events\n    this.element.addEventListener('keydown', this.handleKeyDown.bind(this))\n  }\n\n  /**\n   * Setup accessibility features\n   */\n  setupAccessibility() {\n    // Ensure button has proper role\n    if (!this.element.getAttribute('role') && this.element.tagName !== 'BUTTON') {\n      this.element.setAttribute('role', 'button')\n    }\n\n    // Ensure button is focusable\n    if (!this.element.hasAttribute('tabindex') && this.element.tagName !== 'BUTTON') {\n      this.element.setAttribute('tabindex', '0')\n    }\n\n    // Add ARIA attributes for loading state\n    if (this.options.enableLoading) {\n      this.element.setAttribute('aria-busy', 'false')\n    }\n  }\n\n  /**\n   * Handle click events\n   */\n  handleClick(event) {\n    if (this.isLoading || this.element.disabled) {\n      event.preventDefault()\n      return\n    }\n\n    // Create ripple effect\n    if (this.options.enableRipple) {\n      this.createRipple(event)\n    }\n\n    // Dispatch custom event\n    this.dispatchEvent('glass-button-click', { originalEvent: event })\n  }\n\n  /**\n   * Handle mouse enter\n   */\n  handleMouseEnter(event) {\n    this.element.classList.add('glass-btn-hover')\n    this.dispatchEvent('glass-button-hover', { state: 'enter', originalEvent: event })\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave(event) {\n    this.element.classList.remove('glass-btn-hover')\n    this.dispatchEvent('glass-button-hover', { state: 'leave', originalEvent: event })\n  }\n\n  /**\n   * Handle focus\n   */\n  handleFocus(event) {\n    this.element.classList.add('glass-btn-focus')\n    this.dispatchEvent('glass-button-focus', { state: 'focus', originalEvent: event })\n  }\n\n  /**\n   * Handle blur\n   */\n  handleBlur(event) {\n    this.element.classList.remove('glass-btn-focus')\n    this.dispatchEvent('glass-button-focus', { state: 'blur', originalEvent: event })\n  }\n\n  /**\n   * Handle keyboard events\n   */\n  handleKeyDown(event) {\n    // Activate button with Enter or Space\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault()\n      this.element.click()\n    }\n  }\n\n  /**\n   * Create ripple effect\n   */\n  createRipple(event) {\n    const rect = this.element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'glass-btn-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: ${this.options.rippleColor};\n      transform: translate(-50%, -50%);\n      animation: glass-btn-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1;\n    `\n\n    // Ensure button has relative positioning\n    if (getComputedStyle(this.element).position === 'static') {\n      this.element.style.position = 'relative'\n    }\n\n    this.element.appendChild(ripple)\n    this.ripples.add(ripple)\n\n    // Remove ripple after animation\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n      this.ripples.delete(ripple)\n    }, 600)\n  }\n\n  /**\n   * Set loading state\n   */\n  setLoading(loading = true) {\n    this.isLoading = loading\n    \n    if (loading) {\n      this.element.classList.add('glass-btn-loading')\n      this.element.setAttribute('aria-busy', 'true')\n      this.element.disabled = true\n    } else {\n      this.element.classList.remove('glass-btn-loading')\n      this.element.setAttribute('aria-busy', 'false')\n      this.element.disabled = false\n    }\n\n    this.updateState()\n    this.dispatchEvent('glass-button-loading', { loading })\n  }\n\n  /**\n   * Set disabled state\n   */\n  setDisabled(disabled = true) {\n    this.element.disabled = disabled\n    this.element.classList.toggle('glass-btn-disabled', disabled)\n    this.updateState()\n    this.dispatchEvent('glass-button-disabled', { disabled })\n  }\n\n  /**\n   * Set button variant\n   */\n  setVariant(variant) {\n    // Remove existing variant classes\n    const variantClasses = [\n      'glass-btn-primary',\n      'glass-btn-secondary',\n      'glass-btn-success',\n      'glass-btn-warning',\n      'glass-btn-danger',\n      'glass-btn-ghost'\n    ]\n    \n    this.element.classList.remove(...variantClasses)\n    \n    // Add new variant class\n    if (variant && variant !== 'default') {\n      this.element.classList.add(`glass-btn-${variant}`)\n    }\n\n    this.dispatchEvent('glass-button-variant', { variant })\n  }\n\n  /**\n   * Set button size\n   */\n  setSize(size) {\n    // Remove existing size classes\n    const sizeClasses = ['glass-btn-xs', 'glass-btn-sm', 'glass-btn-lg', 'glass-btn-xl']\n    this.element.classList.remove(...sizeClasses)\n    \n    // Add new size class\n    if (size && size !== 'default') {\n      this.element.classList.add(`glass-btn-${size}`)\n    }\n\n    this.dispatchEvent('glass-button-size', { size })\n  }\n\n  /**\n   * Update button state\n   */\n  updateState() {\n    const state = {\n      loading: this.isLoading,\n      disabled: this.element.disabled,\n      focused: this.element.classList.contains('glass-btn-focus'),\n      hovered: this.element.classList.contains('glass-btn-hover')\n    }\n\n    this.element.setAttribute('data-state', JSON.stringify(state))\n  }\n\n  /**\n   * Dispatch custom event\n   */\n  dispatchEvent(eventName, detail = {}) {\n    const event = new CustomEvent(eventName, {\n      detail: {\n        button: this,\n        element: this.element,\n        ...detail\n      },\n      bubbles: true,\n      cancelable: true\n    })\n\n    this.element.dispatchEvent(event)\n  }\n\n  /**\n   * Add animation\n   */\n  addAnimation(animationType, duration = 1000) {\n    const animationClass = `glass-btn-animate-${animationType}`\n    this.element.classList.add(animationClass)\n\n    setTimeout(() => {\n      this.element.classList.remove(animationClass)\n    }, duration)\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Get button state\n   */\n  getState() {\n    return {\n      loading: this.isLoading,\n      disabled: this.element.disabled,\n      variant: this.getVariant(),\n      size: this.getSize()\n    }\n  }\n\n  /**\n   * Get current variant\n   */\n  getVariant() {\n    const variants = ['primary', 'secondary', 'success', 'warning', 'danger', 'ghost']\n    return variants.find(variant => this.element.classList.contains(`glass-btn-${variant}`)) || 'default'\n  }\n\n  /**\n   * Get current size\n   */\n  getSize() {\n    const sizes = ['xs', 'sm', 'lg', 'xl']\n    return sizes.find(size => this.element.classList.contains(`glass-btn-${size}`)) || 'default'\n  }\n\n  /**\n   * Destroy button component\n   */\n  destroy() {\n    // Remove all ripples\n    this.ripples.forEach(ripple => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    })\n    this.ripples.clear()\n\n    // Remove event listeners (they'll be garbage collected with the element)\n    \n    // Remove component identifier\n    this.element.removeAttribute('data-glass-button')\n    \n    // Remove state classes\n    this.element.classList.remove('glass-btn-hover', 'glass-btn-focus', 'glass-btn-loading', 'glass-btn-disabled')\n  }\n}\n", "/**\n * Glass Card Component\n * Enhanced card with glass effects and interactions\n */\n\nexport class GlassCard {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      enableHover: true,\n      enableClick: true,\n      enableLoading: true,\n      hoverEffect: 'lift', // 'lift', 'glow', 'scale'\n      ...options\n    }\n\n    this.isLoading = false\n    \n    this.init()\n  }\n\n  /**\n   * Initialize card component\n   */\n  init() {\n    this.setupElement()\n    this.bindEvents()\n    this.setupAccessibility()\n  }\n\n  /**\n   * Setup card element\n   */\n  setupElement() {\n    // Add base glass card class if not present\n    if (!this.element.classList.contains('glass-card')) {\n      this.element.classList.add('glass-card')\n    }\n\n    // Add component identifier\n    this.element.setAttribute('data-glass-card', 'initialized')\n\n    // Setup initial state\n    this.updateState()\n  }\n\n  /**\n   * Bind event listeners\n   */\n  bindEvents() {\n    // Click events for interactive cards\n    if (this.options.enableClick && this.element.classList.contains('glass-card-interactive')) {\n      this.element.addEventListener('click', this.handleClick.bind(this))\n    }\n\n    // Mouse events for hover effects\n    if (this.options.enableHover) {\n      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))\n      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))\n    }\n\n    // Focus events for accessibility\n    this.element.addEventListener('focus', this.handleFocus.bind(this))\n    this.element.addEventListener('blur', this.handleBlur.bind(this))\n\n    // Keyboard events for interactive cards\n    if (this.element.classList.contains('glass-card-interactive')) {\n      this.element.addEventListener('keydown', this.handleKeyDown.bind(this))\n    }\n  }\n\n  /**\n   * Setup accessibility features\n   */\n  setupAccessibility() {\n    // Make interactive cards focusable and add role\n    if (this.element.classList.contains('glass-card-interactive')) {\n      if (!this.element.hasAttribute('tabindex')) {\n        this.element.setAttribute('tabindex', '0')\n      }\n      if (!this.element.getAttribute('role')) {\n        this.element.setAttribute('role', 'button')\n      }\n    }\n\n    // Add ARIA attributes for loading state\n    if (this.options.enableLoading) {\n      this.element.setAttribute('aria-busy', 'false')\n    }\n  }\n\n  /**\n   * Handle click events\n   */\n  handleClick(event) {\n    if (this.isLoading) {\n      event.preventDefault()\n      return\n    }\n\n    // Dispatch custom event\n    this.dispatchEvent('glass-card-click', { originalEvent: event })\n  }\n\n  /**\n   * Handle mouse enter\n   */\n  handleMouseEnter(event) {\n    this.element.classList.add('glass-card-hover')\n    \n    // Apply hover effect based on options\n    switch (this.options.hoverEffect) {\n      case 'glow':\n        this.element.classList.add('glass-card-glow')\n        break\n      case 'scale':\n        this.element.classList.add('glass-card-scale')\n        break\n      default:\n        // 'lift' is handled by CSS\n        break\n    }\n\n    this.dispatchEvent('glass-card-hover', { state: 'enter', originalEvent: event })\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave(event) {\n    this.element.classList.remove('glass-card-hover', 'glass-card-glow', 'glass-card-scale')\n    this.dispatchEvent('glass-card-hover', { state: 'leave', originalEvent: event })\n  }\n\n  /**\n   * Handle focus\n   */\n  handleFocus(event) {\n    this.element.classList.add('glass-card-focus')\n    this.dispatchEvent('glass-card-focus', { state: 'focus', originalEvent: event })\n  }\n\n  /**\n   * Handle blur\n   */\n  handleBlur(event) {\n    this.element.classList.remove('glass-card-focus')\n    this.dispatchEvent('glass-card-focus', { state: 'blur', originalEvent: event })\n  }\n\n  /**\n   * Handle keyboard events\n   */\n  handleKeyDown(event) {\n    // Activate card with Enter or Space\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault()\n      this.element.click()\n    }\n  }\n\n  /**\n   * Set loading state\n   */\n  setLoading(loading = true) {\n    this.isLoading = loading\n    \n    if (loading) {\n      this.element.classList.add('glass-card-loading')\n      this.element.setAttribute('aria-busy', 'true')\n    } else {\n      this.element.classList.remove('glass-card-loading')\n      this.element.setAttribute('aria-busy', 'false')\n    }\n\n    this.updateState()\n    this.dispatchEvent('glass-card-loading', { loading })\n  }\n\n  /**\n   * Set card variant\n   */\n  setVariant(variant) {\n    // Remove existing variant classes\n    const variantClasses = [\n      'glass-card-elevated',\n      'glass-card-subtle',\n      'glass-card-outlined',\n      'glass-card-filled'\n    ]\n    \n    this.element.classList.remove(...variantClasses)\n    \n    // Add new variant class\n    if (variant && variant !== 'default') {\n      this.element.classList.add(`glass-card-${variant}`)\n    }\n\n    this.dispatchEvent('glass-card-variant', { variant })\n  }\n\n  /**\n   * Set card size\n   */\n  setSize(size) {\n    // Remove existing size classes\n    const sizeClasses = ['glass-card-sm', 'glass-card-lg']\n    this.element.classList.remove(...sizeClasses)\n    \n    // Add new size class\n    if (size && size !== 'default') {\n      this.element.classList.add(`glass-card-${size}`)\n    }\n\n    this.dispatchEvent('glass-card-size', { size })\n  }\n\n  /**\n   * Add badge to card\n   */\n  addBadge(text, options = {}) {\n    const badgeOptions = {\n      position: 'top-right',\n      variant: 'default',\n      ...options\n    }\n\n    // Remove existing badge\n    this.removeBadge()\n\n    const badge = document.createElement('div')\n    badge.className = `glass-card-badge glass-card-badge-${badgeOptions.position}`\n    badge.textContent = text\n    \n    if (badgeOptions.variant !== 'default') {\n      badge.classList.add(`glass-card-badge-${badgeOptions.variant}`)\n    }\n\n    this.element.appendChild(badge)\n    this.dispatchEvent('glass-card-badge-added', { text, options: badgeOptions })\n  }\n\n  /**\n   * Remove badge from card\n   */\n  removeBadge() {\n    const existingBadge = this.element.querySelector('.glass-card-badge')\n    if (existingBadge) {\n      existingBadge.remove()\n      this.dispatchEvent('glass-card-badge-removed')\n    }\n  }\n\n  /**\n   * Update card content\n   */\n  updateContent(content) {\n    const body = this.element.querySelector('.glass-card-body')\n    if (body) {\n      if (typeof content === 'string') {\n        body.innerHTML = content\n      } else if (content instanceof HTMLElement) {\n        body.innerHTML = ''\n        body.appendChild(content)\n      }\n      this.dispatchEvent('glass-card-content-updated', { content })\n    }\n  }\n\n  /**\n   * Update card state\n   */\n  updateState() {\n    const state = {\n      loading: this.isLoading,\n      interactive: this.element.classList.contains('glass-card-interactive'),\n      focused: this.element.classList.contains('glass-card-focus'),\n      hovered: this.element.classList.contains('glass-card-hover')\n    }\n\n    this.element.setAttribute('data-state', JSON.stringify(state))\n  }\n\n  /**\n   * Dispatch custom event\n   */\n  dispatchEvent(eventName, detail = {}) {\n    const event = new CustomEvent(eventName, {\n      detail: {\n        card: this,\n        element: this.element,\n        ...detail\n      },\n      bubbles: true,\n      cancelable: true\n    })\n\n    this.element.dispatchEvent(event)\n  }\n\n  /**\n   * Add animation\n   */\n  addAnimation(animationType, duration = 1000) {\n    const animationClass = `glass-card-animate-${animationType}`\n    this.element.classList.add(animationClass)\n\n    setTimeout(() => {\n      this.element.classList.remove(animationClass)\n    }, duration)\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Get card state\n   */\n  getState() {\n    return {\n      loading: this.isLoading,\n      interactive: this.element.classList.contains('glass-card-interactive'),\n      variant: this.getVariant(),\n      size: this.getSize()\n    }\n  }\n\n  /**\n   * Get current variant\n   */\n  getVariant() {\n    const variants = ['elevated', 'subtle', 'outlined', 'filled']\n    return variants.find(variant => this.element.classList.contains(`glass-card-${variant}`)) || 'default'\n  }\n\n  /**\n   * Get current size\n   */\n  getSize() {\n    const sizes = ['sm', 'lg']\n    return sizes.find(size => this.element.classList.contains(`glass-card-${size}`)) || 'default'\n  }\n\n  /**\n   * Destroy card component\n   */\n  destroy() {\n    // Remove component identifier\n    this.element.removeAttribute('data-glass-card')\n    \n    // Remove state classes\n    this.element.classList.remove('glass-card-hover', 'glass-card-focus', 'glass-card-loading', 'glass-card-glow', 'glass-card-scale')\n    \n    // Remove badge if present\n    this.removeBadge()\n  }\n}\n", "/**\n * Glass Modal Component\n * Enhanced modal with glass effects\n */\n\nexport class GlassModal {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      backdrop: true,\n      keyboard: true,\n      focus: true,\n      ...options\n    }\n\n    this.isOpen = false\n    this.init()\n  }\n\n  init() {\n    this.element.setAttribute('data-glass-modal', 'initialized')\n  }\n\n  open() {\n    this.isOpen = true\n    this.element.classList.add('glass-modal-open')\n  }\n\n  close() {\n    this.isOpen = false\n    this.element.classList.remove('glass-modal-open')\n  }\n\n  destroy() {\n    this.element.removeAttribute('data-glass-modal')\n  }\n}\n", "/**\n * Glass Navigation Component\n * Enhanced navigation with glass effects\n */\n\nexport class GlassNavigation {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      sticky: false,\n      collapsible: true,\n      ...options\n    }\n\n    this.init()\n  }\n\n  init() {\n    this.element.setAttribute('data-glass-nav', 'initialized')\n  }\n\n  destroy() {\n    this.element.removeAttribute('data-glass-nav')\n  }\n}\n", "/**\n * Theme Manager Utility\n * Manages light/dark theme switching and system preferences\n */\n\nexport class ThemeManager {\n  constructor(initialTheme = 'auto') {\n    this.currentTheme = initialTheme\n    this.systemTheme = this.getSystemTheme()\n    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    this.listeners = new Set()\n    \n    this.storageKey = 'liquid-glass-ui-theme'\n  }\n\n  /**\n   * Initialize theme manager\n   */\n  init() {\n    // Load saved theme preference\n    const savedTheme = this.loadTheme()\n    if (savedTheme) {\n      this.currentTheme = savedTheme\n    }\n\n    // Apply initial theme\n    this.applyTheme()\n\n    // Listen for system theme changes\n    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange.bind(this))\n\n    // Add theme toggle functionality\n    this.initThemeToggles()\n  }\n\n  /**\n   * Get system theme preference\n   */\n  getSystemTheme() {\n    return this.mediaQuery.matches ? 'dark' : 'light'\n  }\n\n  /**\n   * Get effective theme (resolves 'auto' to actual theme)\n   */\n  getEffectiveTheme() {\n    if (this.currentTheme === 'auto') {\n      return this.systemTheme\n    }\n    return this.currentTheme\n  }\n\n  /**\n   * Set theme\n   */\n  setTheme(theme) {\n    if (!['light', 'dark', 'auto'].includes(theme)) {\n      console.warn(`Invalid theme: ${theme}. Using 'auto' instead.`)\n      theme = 'auto'\n    }\n\n    this.currentTheme = theme\n    this.applyTheme()\n    this.saveTheme()\n    this.notifyListeners()\n  }\n\n  /**\n   * Toggle between light and dark themes\n   */\n  toggleTheme() {\n    const effectiveTheme = this.getEffectiveTheme()\n    const newTheme = effectiveTheme === 'light' ? 'dark' : 'light'\n    this.setTheme(newTheme)\n  }\n\n  /**\n   * Apply theme to document\n   */\n  applyTheme() {\n    const effectiveTheme = this.getEffectiveTheme()\n    \n    // Remove existing theme classes\n    document.documentElement.classList.remove('theme-light', 'theme-dark')\n    document.documentElement.removeAttribute('data-theme')\n    \n    // Add new theme class and attribute\n    document.documentElement.classList.add(`theme-${effectiveTheme}`)\n    document.documentElement.setAttribute('data-theme', effectiveTheme)\n\n    // Update meta theme-color for mobile browsers\n    this.updateMetaThemeColor(effectiveTheme)\n\n    // Dispatch theme change event\n    this.dispatchThemeEvent(effectiveTheme)\n  }\n\n  /**\n   * Update meta theme-color for mobile browsers\n   */\n  updateMetaThemeColor(theme) {\n    let metaThemeColor = document.querySelector('meta[name=\"theme-color\"]')\n    \n    if (!metaThemeColor) {\n      metaThemeColor = document.createElement('meta')\n      metaThemeColor.name = 'theme-color'\n      document.head.appendChild(metaThemeColor)\n    }\n\n    const colors = {\n      light: '#ffffff',\n      dark: '#000000'\n    }\n\n    metaThemeColor.content = colors[theme] || colors.light\n  }\n\n  /**\n   * Dispatch custom theme change event\n   */\n  dispatchThemeEvent(theme) {\n    const event = new CustomEvent('themechange', {\n      detail: {\n        theme,\n        previousTheme: this.previousTheme,\n        isSystemTheme: this.currentTheme === 'auto'\n      }\n    })\n    \n    this.previousTheme = theme\n    document.dispatchEvent(event)\n  }\n\n  /**\n   * Handle system theme changes\n   */\n  handleSystemThemeChange(event) {\n    this.systemTheme = event.matches ? 'dark' : 'light'\n    \n    if (this.currentTheme === 'auto') {\n      this.applyTheme()\n      this.notifyListeners()\n    }\n  }\n\n  /**\n   * Initialize theme toggle buttons\n   */\n  initThemeToggles() {\n    const toggles = document.querySelectorAll('[data-theme-toggle]')\n    \n    toggles.forEach(toggle => {\n      toggle.addEventListener('click', () => {\n        this.toggleTheme()\n      })\n    })\n\n    // Initialize theme selectors\n    const selectors = document.querySelectorAll('[data-theme-selector]')\n    \n    selectors.forEach(selector => {\n      selector.addEventListener('change', (event) => {\n        this.setTheme(event.target.value)\n      })\n      \n      // Set initial value\n      selector.value = this.currentTheme\n    })\n  }\n\n  /**\n   * Add theme change listener\n   */\n  addListener(callback) {\n    this.listeners.add(callback)\n    \n    return () => {\n      this.listeners.delete(callback)\n    }\n  }\n\n  /**\n   * Notify all listeners of theme change\n   */\n  notifyListeners() {\n    const effectiveTheme = this.getEffectiveTheme()\n    \n    this.listeners.forEach(callback => {\n      try {\n        callback(effectiveTheme, this.currentTheme)\n      } catch (error) {\n        console.error('Error in theme change listener:', error)\n      }\n    })\n  }\n\n  /**\n   * Save theme preference to localStorage\n   */\n  saveTheme() {\n    try {\n      localStorage.setItem(this.storageKey, this.currentTheme)\n    } catch (error) {\n      console.warn('Could not save theme preference:', error)\n    }\n  }\n\n  /**\n   * Load theme preference from localStorage\n   */\n  loadTheme() {\n    try {\n      return localStorage.getItem(this.storageKey)\n    } catch (error) {\n      console.warn('Could not load theme preference:', error)\n      return null\n    }\n  }\n\n  /**\n   * Get theme-specific CSS custom property value\n   */\n  getThemeValue(property) {\n    return getComputedStyle(document.documentElement).getPropertyValue(property)\n  }\n\n  /**\n   * Set theme-specific CSS custom property\n   */\n  setThemeValue(property, value) {\n    document.documentElement.style.setProperty(property, value)\n  }\n\n  /**\n   * Create theme-aware color scheme\n   */\n  createColorScheme(lightColor, darkColor) {\n    const effectiveTheme = this.getEffectiveTheme()\n    return effectiveTheme === 'dark' ? darkColor : lightColor\n  }\n\n  /**\n   * Destroy theme manager\n   */\n  destroy() {\n    this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange.bind(this))\n    this.listeners.clear()\n  }\n\n  /**\n   * Get current theme info\n   */\n  getThemeInfo() {\n    return {\n      current: this.currentTheme,\n      effective: this.getEffectiveTheme(),\n      system: this.systemTheme,\n      isAuto: this.currentTheme === 'auto'\n    }\n  }\n}\n", "/**\n * Device Detector Utility\n * Detects device capabilities and features for optimal glass effects\n */\n\nexport class DeviceDetector {\n  constructor() {\n    this.userAgent = navigator.userAgent.toLowerCase()\n    this.capabilities = this.detectCapabilities()\n  }\n\n  /**\n   * Check if device is mobile\n   */\n  isMobile() {\n    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is tablet\n   */\n  isTablet() {\n    return /ipad|android(?!.*mobile)/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is desktop\n   */\n  isDesktop() {\n    return !this.isMobile() && !this.isTablet()\n  }\n\n  /**\n   * Check if device is iOS\n   */\n  isIOS() {\n    return /iphone|ipad|ipod/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is Android\n   */\n  isAndroid() {\n    return /android/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device supports touch\n   */\n  supportsTouch() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0\n  }\n\n  /**\n   * Check if device supports device orientation\n   */\n  supportsDeviceOrientation() {\n    return 'DeviceOrientationEvent' in window\n  }\n\n  /**\n   * Check if device supports device motion\n   */\n  supportsDeviceMotion() {\n    return 'DeviceMotionEvent' in window\n  }\n\n  /**\n   * Check if browser supports backdrop-filter\n   */\n  supportsBackdropFilter() {\n    return CSS.supports('backdrop-filter', 'blur(1px)') || \n           CSS.supports('-webkit-backdrop-filter', 'blur(1px)')\n  }\n\n  /**\n   * Check if user prefers reduced motion\n   */\n  prefersReducedMotion() {\n    return window.matchMedia('(prefers-reduced-motion: reduce)').matches\n  }\n\n  /**\n   * Get device pixel ratio\n   */\n  getPixelRatio() {\n    return window.devicePixelRatio || 1\n  }\n\n  /**\n   * Get viewport dimensions\n   */\n  getViewport() {\n    return {\n      width: window.innerWidth,\n      height: window.innerHeight,\n    }\n  }\n\n  /**\n   * Check if device has high refresh rate\n   */\n  hasHighRefreshRate() {\n    // Estimate based on requestAnimationFrame timing\n    return new Promise((resolve) => {\n      let frames = 0\n      const start = performance.now()\n      \n      const checkFrame = () => {\n        frames++\n        if (frames < 60) {\n          requestAnimationFrame(checkFrame)\n        } else {\n          const duration = performance.now() - start\n          const fps = Math.round(frames / (duration / 1000))\n          resolve(fps > 60)\n        }\n      }\n      \n      requestAnimationFrame(checkFrame)\n    })\n  }\n\n  /**\n   * Detect all device capabilities\n   */\n  detectCapabilities() {\n    return {\n      isMobile: this.isMobile(),\n      isTablet: this.isTablet(),\n      isDesktop: this.isDesktop(),\n      isIOS: this.isIOS(),\n      isAndroid: this.isAndroid(),\n      supportsTouch: this.supportsTouch(),\n      supportsDeviceOrientation: this.supportsDeviceOrientation(),\n      supportsDeviceMotion: this.supportsDeviceMotion(),\n      supportsBackdropFilter: this.supportsBackdropFilter(),\n      prefersReducedMotion: this.prefersReducedMotion(),\n      pixelRatio: this.getPixelRatio(),\n      viewport: this.getViewport(),\n    }\n  }\n\n  /**\n   * Get optimal glass effect settings based on device\n   */\n  getOptimalSettings() {\n    const settings = {\n      blurIntensity: 'medium',\n      animationDuration: 'normal',\n      enableCursorEffects: false,\n      enableMotionEffects: false,\n      enableParallax: false,\n      enableHighFrameRate: false,\n    }\n\n    // Desktop optimizations\n    if (this.isDesktop()) {\n      settings.enableCursorEffects = true\n      settings.blurIntensity = 'high'\n      settings.enableHighFrameRate = true\n    }\n\n    // Mobile optimizations\n    if (this.isMobile()) {\n      settings.enableMotionEffects = this.supportsDeviceOrientation()\n      settings.blurIntensity = 'low'\n      settings.animationDuration = 'fast'\n    }\n\n    // High-end device optimizations\n    if (this.getPixelRatio() > 2) {\n      settings.enableParallax = true\n    }\n\n    // Reduced motion preferences\n    if (this.prefersReducedMotion()) {\n      settings.animationDuration = 'none'\n      settings.enableMotionEffects = false\n      settings.enableParallax = false\n    }\n\n    // Backdrop filter fallback\n    if (!this.supportsBackdropFilter()) {\n      settings.blurIntensity = 'none'\n    }\n\n    return settings\n  }\n\n  /**\n   * Add device classes to document\n   */\n  addDeviceClasses() {\n    const classes = []\n    \n    if (this.isMobile()) classes.push('device-mobile')\n    if (this.isTablet()) classes.push('device-tablet')\n    if (this.isDesktop()) classes.push('device-desktop')\n    if (this.isIOS()) classes.push('device-ios')\n    if (this.isAndroid()) classes.push('device-android')\n    if (this.supportsTouch()) classes.push('supports-touch')\n    if (this.supportsBackdropFilter()) classes.push('supports-backdrop-filter')\n    if (this.prefersReducedMotion()) classes.push('prefers-reduced-motion')\n    \n    document.documentElement.classList.add(...classes)\n  }\n\n  /**\n   * Listen for viewport changes\n   */\n  onViewportChange(callback) {\n    let timeout\n    const handleResize = () => {\n      clearTimeout(timeout)\n      timeout = setTimeout(() => {\n        this.capabilities.viewport = this.getViewport()\n        callback(this.capabilities.viewport)\n      }, 100)\n    }\n\n    window.addEventListener('resize', handleResize)\n    window.addEventListener('orientationchange', handleResize)\n\n    return () => {\n      window.removeEventListener('resize', handleResize)\n      window.removeEventListener('orientationchange', handleResize)\n    }\n  }\n}\n", "/**\n * Liquid Glass UI - Main Entry Point\n * A comprehensive JavaScript/CSS UI framework that replicates Apple's liquid glass design aesthetic\n */\n\n// Import CSS\nimport './css/index.css'\n\n// Import core utilities\nimport { GlassEffects } from './js/effects/glass-effects.js'\nimport { CursorEffects } from './js/effects/cursor-effects.js'\nimport { MotionEffects } from './js/effects/motion-effects.js'\n\n// Import components\nimport { GlassButton } from './js/components/button.js'\nimport { GlassCard } from './js/components/card.js'\nimport { GlassModal } from './js/components/modal.js'\nimport { GlassNavigation } from './js/components/navigation.js'\n\n// Import utilities\nimport { ThemeManager } from './js/utils/theme-manager.js'\nimport { DeviceDetector } from './js/utils/device-detector.js'\n\n/**\n * Main LiquidGlassUI class\n */\nclass LiquidGlassUI {\n  constructor(options = {}) {\n    this.options = {\n      enableCursorEffects: true,\n      enableMotionEffects: true,\n      enableAnimations: true,\n      theme: 'auto', // 'light', 'dark', 'auto'\n      ...options,\n    }\n\n    this.effects = new GlassEffects(this.options)\n    this.themeManager = new ThemeManager(this.options.theme)\n    this.deviceDetector = new DeviceDetector()\n\n    this.init()\n  }\n\n  /**\n   * Initialize the framework\n   */\n  init() {\n    // Initialize theme\n    this.themeManager.init()\n\n    // Initialize effects based on device capabilities\n    if (this.deviceDetector.isDesktop() && this.options.enableCursorEffects) {\n      this.cursorEffects = new CursorEffects()\n      this.cursorEffects.init()\n    }\n\n    if (this.deviceDetector.isMobile() && this.options.enableMotionEffects) {\n      this.motionEffects = new MotionEffects()\n      this.motionEffects.init()\n    }\n\n    // Initialize components\n    this.initComponents()\n\n    // Mark as initialized\n    document.documentElement.setAttribute('data-liquid-glass-ui', 'initialized')\n  }\n\n  /**\n   * Initialize all components\n   */\n  initComponents() {\n    // Auto-initialize components with data attributes\n    this.initButtons()\n    this.initCards()\n    this.initModals()\n    this.initNavigation()\n  }\n\n  /**\n   * Initialize button components\n   */\n  initButtons() {\n    const buttons = document.querySelectorAll('[data-glass-button]')\n    buttons.forEach(button => new GlassButton(button))\n  }\n\n  /**\n   * Initialize card components\n   */\n  initCards() {\n    const cards = document.querySelectorAll('[data-glass-card]')\n    cards.forEach(card => new GlassCard(card))\n  }\n\n  /**\n   * Initialize modal components\n   */\n  initModals() {\n    const modals = document.querySelectorAll('[data-glass-modal]')\n    modals.forEach(modal => new GlassModal(modal))\n  }\n\n  /**\n   * Initialize navigation components\n   */\n  initNavigation() {\n    const navs = document.querySelectorAll('[data-glass-nav]')\n    navs.forEach(nav => new GlassNavigation(nav))\n  }\n\n  /**\n   * Update theme\n   */\n  setTheme(theme) {\n    this.themeManager.setTheme(theme)\n  }\n\n  /**\n   * Destroy the framework instance\n   */\n  destroy() {\n    if (this.cursorEffects) this.cursorEffects.destroy()\n    if (this.motionEffects) this.motionEffects.destroy()\n    this.themeManager.destroy()\n    \n    document.documentElement.removeAttribute('data-liquid-glass-ui')\n  }\n}\n\n// Export for module usage\nexport default LiquidGlassUI\n\n// Export individual components and utilities\nexport {\n  GlassEffects,\n  CursorEffects,\n  MotionEffects,\n  GlassButton,\n  GlassCard,\n  GlassModal,\n  GlassNavigation,\n  ThemeManager,\n  DeviceDetector,\n}\n\n// Auto-initialize if not in module environment\nif (typeof window !== 'undefined' && !window.LiquidGlassUI) {\n  window.LiquidGlassUI = LiquidGlassUI\n  \n  // Auto-initialize on DOM ready\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', () => {\n      if (!document.querySelector('[data-no-auto-init]')) {\n        new LiquidGlassUI()\n      }\n    })\n  } else {\n    if (!document.querySelector('[data-no-auto-init]')) {\n      new LiquidGlassUI()\n    }\n  }\n}\n"], "names": ["GlassEffects", "options", "deviceMemory", "hardwareConcurrency", "connection", "performanceScore", "entries", "entry", "element", "effectData", "resizeTimeout", "selector", "baseOptions", "rect", "event", "handler", "isHovering", "isFocused", "x", "y", "ripple", "animationType", "cleanup", "CursorEffects", "animate", "centerX", "centerY", "deltaX", "deltaY", "distance", "normalizedDistance", "intensity", "relativeX", "relativeY", "shadowIntensity", "shadowX", "shadowY", "<PERSON><PERSON><PERSON><PERSON>", "shadowOpacity", "shadow", "lightIntensity", "gradientX", "gradientY", "gradient", "handleClick", "newOptions", "MotionEffects", "permission", "error", "beta", "gamma", "normalizedX", "normalizedY", "tiltX", "tiltY", "parallaxX", "parallaxY", "shakeClass", "value", "min", "max", "GlassButton", "loading", "disabled", "variant", "variantClasses", "size", "sizeClasses", "state", "eventName", "detail", "duration", "animationClass", "GlassCard", "text", "badgeOptions", "badge", "existingBadge", "content", "body", "GlassModal", "GlassNavigation", "ThemeManager", "initialTheme", "savedTheme", "theme", "newTheme", "effectiveTheme", "metaThemeColor", "colors", "toggle", "callback", "property", "lightColor", "darkColor", "DeviceDetector", "resolve", "frames", "start", "checkFrame", "fps", "settings", "classes", "timeout", "handleResize", "LiquidGlassUI", "button", "card", "modal", "nav"], "mappings": "qOAKO,MAAMA,CAAa,CACxB,YAAYC,EAAU,GAAI,CACxB,KAAK,QAAU,CACb,iBAAkB,GAClB,mBAAoB,GACpB,gBAAiB,OACjB,GAAGA,CACT,EAEI,KAAK,SAAW,IAAI,IACpB,KAAK,UAAY,IAAI,IACrB,KAAK,cAAgB,EACvB,CAKA,MAAO,CACD,KAAK,gBAET,KAAK,8BAA6B,EAClC,KAAK,0BAAyB,EAC9B,KAAK,iBAAgB,EACrB,KAAK,gBAAe,EAEpB,KAAK,cAAgB,GACvB,CAKA,+BAAgC,CAC9B,GAAI,KAAK,QAAQ,kBAAoB,OAAQ,OAE7C,MAAMC,EAAe,UAAU,cAAgB,EACzCC,EAAsB,UAAU,qBAAuB,EACvDC,EAAa,UAAU,WAE7B,IAAIC,EAAmB,EAGnBH,GAAgB,EAAGG,GAAoB,EAClCH,GAAgB,EAAGG,GAAoB,EACvCH,GAAgB,IAAGG,GAAoB,GAG5CF,GAAuB,EAAGE,GAAoB,EACzCF,GAAuB,EAAGE,GAAoB,EAC9CF,GAAuB,IAAGE,GAAoB,GAGnDD,EACEA,EAAW,gBAAkB,KAAMC,GAAoB,EAClDD,EAAW,gBAAkB,OAAMC,GAAoB,GAEhEA,GAAoB,EAIlBA,GAAoB,EAAG,KAAK,QAAQ,gBAAkB,OACjDA,GAAoB,EAAG,KAAK,QAAQ,gBAAkB,SAC1D,KAAK,QAAQ,gBAAkB,MAGpC,SAAS,gBAAgB,UAAU,IAAI,eAAe,KAAK,QAAQ,eAAe,EAAE,CACtF,CAKA,2BAA4B,CACpB,yBAA0B,SAEhC,KAAK,qBAAuB,IAAI,qBAC7BC,GAAY,CACXA,EAAQ,QAAQC,GAAS,CACvB,MAAMC,EAAUD,EAAM,OAChBE,EAAa,KAAK,SAAS,IAAID,CAAO,EAEvCC,IAEDF,EAAM,eACR,KAAK,gBAAgBC,EAASC,CAAU,EAExC,KAAK,kBAAkBD,EAASC,CAAU,EAE9C,CAAC,CACH,EACA,CACE,WAAY,OACZ,UAAW,EACnB,CACA,EACE,CAKA,kBAAmB,CAEjB,SAAS,iBAAiB,mBAAoB,IAAM,CAC9C,SAAS,OACX,KAAK,gBAAe,EAEpB,KAAK,iBAAgB,CAEzB,CAAC,EAGD,IAAIC,EACJ,OAAO,iBAAiB,SAAU,IAAM,CACtC,aAAaA,CAAa,EAC1BA,EAAgB,WAAW,IAAM,CAC/B,KAAK,kBAAiB,CACxB,EAAG,GAAG,CACR,CAAC,CACH,CAKA,iBAAkB,CACE,CAChB,sBACA,SACA,gBACA,eACA,gBACA,gBACA,gBACN,EAEc,QAAQC,GAAY,CAC5B,SAAS,iBAAiBA,CAAQ,EAAE,QAAQH,GAAW,CACrD,KAAK,WAAWA,CAAO,CACzB,CAAC,CACH,CAAC,CACH,CAKA,WAAWA,EAASP,EAAU,GAAI,CAChC,GAAI,KAAK,SAAS,IAAIO,CAAO,EAAG,OAEhC,MAAMC,EAAa,CACjB,QAAAD,EACA,QAAS,CAAE,GAAG,KAAK,kBAAiB,EAAI,GAAGP,CAAO,EAClD,SAAU,GACV,WAAY,IAAI,IAChB,UAAW,IAAI,GACrB,EAEI,KAAK,SAAS,IAAIO,EAASC,CAAU,EAGjC,KAAK,qBACP,KAAK,qBAAqB,QAAQD,CAAO,EAEzC,KAAK,gBAAgBA,EAASC,CAAU,EAI1C,KAAK,kBAAkBD,EAASC,CAAU,CAC5C,CAKA,mBAAoB,CAClB,MAAMG,EAAc,CAClB,WAAY,GACZ,cAAe,GACf,iBAAkB,KAAK,QAAQ,iBAC/B,mBAAoB,KAAK,QAAQ,kBACvC,EAEI,OAAQ,KAAK,QAAQ,gBAAe,CAClC,IAAK,MACH,MAAO,CACL,GAAGA,EACH,WAAY,GACZ,iBAAkB,GAClB,QAAS,CACnB,EACM,IAAK,SACH,MAAO,CACL,GAAGA,EACH,QAAS,CACnB,EACM,IAAK,OACH,MAAO,CACL,GAAGA,EACH,QAAS,EACnB,EACM,QACE,OAAOA,CACf,CACE,CAKA,kBAAkBJ,EAASC,EAAY,CACrC,KAAM,CAAE,QAAAR,CAAO,EAAKQ,EAGpBD,EAAQ,UAAU,IAAI,eAAe,EAGrCA,EAAQ,UAAU,IAAI,qBAAqB,KAAK,QAAQ,eAAe,EAAE,EAGzE,KAAK,wBAAwBA,EAASC,CAAU,EAG5CR,EAAQ,oBACV,KAAK,wBAAwBO,EAASC,CAAU,CAEpD,CAKA,wBAAwBD,EAASC,EAAY,CAC3C,KAAM,CAAE,QAAAR,CAAO,EAAKQ,EACdI,EAAOL,EAAQ,sBAAqB,EAG1CA,EAAQ,MAAM,YAAY,kBAAmB,GAAGK,EAAK,KAAK,IAAI,EAC9DL,EAAQ,MAAM,YAAY,mBAAoB,GAAGK,EAAK,MAAM,IAAI,EAGhEL,EAAQ,MAAM,YAAY,aAAc,GAAGP,EAAQ,SAAW,CAAC,IAAI,EACnEO,EAAQ,MAAM,YAAY,sBAAuBP,EAAQ,iBAAmB,IAAM,GAAG,CACvF,CAKA,wBAAwBO,EAASC,EAAY,CAS3C,OAAO,QARW,CAChB,WAAY,IAAM,KAAK,mBAAmBD,EAASC,EAAY,EAAI,EACnE,WAAY,IAAM,KAAK,mBAAmBD,EAASC,EAAY,EAAK,EACpE,MAAO,IAAM,KAAK,mBAAmBD,EAASC,EAAY,EAAI,EAC9D,KAAM,IAAM,KAAK,mBAAmBD,EAASC,EAAY,EAAK,EAC9D,MAAQK,GAAU,KAAK,mBAAmBN,EAASC,EAAYK,CAAK,CAC1E,CAE4B,EAAE,QAAQ,CAAC,CAACA,EAAOC,CAAO,IAAM,CACtDP,EAAQ,iBAAiBM,EAAOC,CAAO,EACvCN,EAAW,UAAU,IAAI,IAAM,CAC7BD,EAAQ,oBAAoBM,EAAOC,CAAO,CAC5C,CAAC,CACH,CAAC,CACH,CAKA,mBAAmBP,EAASC,EAAYO,EAAY,CAC7CP,EAAW,QAAQ,qBAExBD,EAAQ,UAAU,OAAO,cAAeQ,CAAU,EAE9CA,EACF,KAAK,aAAaR,EAAS,UAAU,EAErC,KAAK,aAAaA,EAAS,WAAW,EAE1C,CAKA,mBAAmBA,EAASC,EAAYQ,EAAW,CAC5CR,EAAW,QAAQ,oBAExBD,EAAQ,UAAU,OAAO,cAAeS,CAAS,CACnD,CAKA,mBAAmBT,EAASC,EAAYK,EAAO,CACxCL,EAAW,QAAQ,qBAExB,KAAK,mBAAmBD,EAASM,CAAK,EACtC,KAAK,aAAaN,EAAS,OAAO,EACpC,CAKA,mBAAmBA,EAASM,EAAO,CACjC,MAAMD,EAAOL,EAAQ,sBAAqB,EACpCU,EAAIJ,EAAM,QAAUD,EAAK,KACzBM,EAAIL,EAAM,QAAUD,EAAK,IAEzBO,EAAS,SAAS,cAAc,KAAK,EAC3CA,EAAO,UAAY,eACnBA,EAAO,MAAM,QAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWVX,EAAQ,MAAM,SAAW,WACzBA,EAAQ,YAAYY,CAAM,EAE1B,WAAW,IAAM,CACXA,EAAO,YACTA,EAAO,WAAW,YAAYA,CAAM,CAExC,EAAG,GAAG,CACR,CAKA,aAAaZ,EAASa,EAAe,CACnC,MAAMZ,EAAa,KAAK,SAAS,IAAID,CAAO,EACxC,CAACC,GAAc,CAACA,EAAW,QAAQ,mBAEvCA,EAAW,WAAW,IAAIY,CAAa,EACvCb,EAAQ,UAAU,IAAI,iBAAiBa,CAAa,EAAE,EAEtD,WAAW,IAAM,CACfZ,EAAW,WAAW,OAAOY,CAAa,EAC1Cb,EAAQ,UAAU,OAAO,iBAAiBa,CAAa,EAAE,CAC3D,EAAG,GAAG,EACR,CAKA,gBAAgBb,EAASC,EAAY,CAC/BA,EAAW,WAEfA,EAAW,SAAW,GACtBD,EAAQ,UAAU,IAAI,cAAc,EACtC,CAKA,kBAAkBA,EAASC,EAAY,CAChCA,EAAW,WAEhBA,EAAW,SAAW,GACtBD,EAAQ,UAAU,OAAO,cAAc,EACzC,CAKA,cAAcA,EAAS,CACrB,MAAMC,EAAa,KAAK,SAAS,IAAID,CAAO,EACvCC,IAGD,KAAK,sBACP,KAAK,qBAAqB,UAAUD,CAAO,EAI7CC,EAAW,UAAU,QAAQa,GAAWA,EAAO,CAAE,EAGjDd,EAAQ,UAAU,OAAO,gBAAiB,eAAgB,cAAe,aAAa,EAGtF,KAAK,SAAS,OAAOA,CAAO,EAC9B,CAKA,mBAAoB,CAClB,KAAK,SAAS,QAAQ,CAACC,EAAYD,IAAY,CAC7C,KAAK,wBAAwBA,EAASC,CAAU,CAClD,CAAC,CACH,CAKA,iBAAkB,CAChB,KAAK,SAAS,QAAQ,CAACA,EAAYD,IAAY,CAC7CA,EAAQ,UAAU,IAAI,cAAc,CACtC,CAAC,CACH,CAKA,kBAAmB,CACjB,KAAK,SAAS,QAAQ,CAACC,EAAYD,IAAY,CAC7CA,EAAQ,UAAU,OAAO,cAAc,CACzC,CAAC,CACH,CAKA,SAAU,CAER,KAAK,SAAS,QAAQ,CAACC,EAAYD,IAAY,CAC7C,KAAK,cAAcA,CAAO,CAC5B,CAAC,EAGG,KAAK,sBACP,KAAK,qBAAqB,WAAU,EAGtC,KAAK,SAAS,MAAK,EACnB,KAAK,UAAU,MAAK,EACpB,KAAK,cAAgB,EACvB,CACF,CC5aO,MAAMe,CAAc,CACzB,YAAYtB,EAAU,GAAI,CACxB,KAAK,QAAU,CACb,cAAe,GACf,eAAgB,GAChB,gBAAiB,GACjB,kBAAmB,GACnB,YAAa,IACb,UAAW,GACX,GAAGA,CACT,EAEI,KAAK,OAAS,CAAE,EAAG,EAAG,EAAG,CAAC,EAC1B,KAAK,aAAe,CAAE,EAAG,EAAG,EAAG,CAAC,EAChC,KAAK,SAAW,IAAI,IACpB,KAAK,SAAW,GAChB,KAAK,eAAiB,IACxB,CAKA,MAAO,CACD,KAAK,WAET,KAAK,SAAW,GAChB,KAAK,WAAU,EACf,KAAK,aAAY,EACjB,KAAK,eAAc,EACrB,CAKA,YAAa,CACX,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EAEvD,SAAS,iBAAiB,YAAa,KAAK,eAAe,EAC3D,SAAS,iBAAiB,aAAc,KAAK,gBAAgB,CAC/D,CAKA,cAAe,CACK,CAChB,uBACA,uBACA,SACA,gBACA,aACN,EAEc,QAAQU,GAAY,CAC5B,SAAS,iBAAiBA,CAAQ,EAAE,QAAQH,GAAW,CACrD,KAAK,WAAWA,CAAO,CACzB,CAAC,CACH,CAAC,CACH,CAKA,WAAWA,EAAS,CACd,KAAK,SAAS,IAAIA,CAAO,IAE7B,KAAK,SAAS,IAAIA,CAAO,EAGzBA,EAAQ,MAAM,YAAY,aAAc,GAAG,EAC3CA,EAAQ,MAAM,YAAY,aAAc,GAAG,EAC3CA,EAAQ,MAAM,YAAY,oBAAqB,GAAG,EAClDA,EAAQ,MAAM,YAAY,qBAAsB,GAAG,EAGnDA,EAAQ,UAAU,IAAI,mBAAmB,EAC3C,CAKA,cAAcA,EAAS,CAChB,KAAK,SAAS,IAAIA,CAAO,IAE9B,KAAK,SAAS,OAAOA,CAAO,EAC5BA,EAAQ,UAAU,OAAO,mBAAmB,EAG5CA,EAAQ,MAAM,eAAe,YAAY,EACzCA,EAAQ,MAAM,eAAe,YAAY,EACzCA,EAAQ,MAAM,eAAe,mBAAmB,EAChDA,EAAQ,MAAM,eAAe,oBAAoB,EACnD,CAKA,gBAAgBM,EAAO,CACrB,KAAK,OAAO,EAAIA,EAAM,QACtB,KAAK,OAAO,EAAIA,EAAM,OACxB,CAKA,kBAAmB,CACjB,KAAK,OAAO,EAAI,KAChB,KAAK,OAAO,EAAI,IAClB,CAKA,gBAAiB,CACf,MAAMU,EAAU,IAAM,CACf,KAAK,WAEV,KAAK,aAAY,EACjB,KAAK,eAAc,EAEnB,KAAK,eAAiB,sBAAsBA,CAAO,EACrD,EAEAA,EAAO,CACT,CAKA,cAAe,CACb,KAAK,aAAa,IAAM,KAAK,OAAO,EAAI,KAAK,aAAa,GAAK,KAAK,QAAQ,UAC5E,KAAK,aAAa,IAAM,KAAK,OAAO,EAAI,KAAK,aAAa,GAAK,KAAK,QAAQ,SAC9E,CAKA,gBAAiB,CACf,KAAK,SAAS,QAAQhB,GAAW,CAC/B,KAAK,cAAcA,CAAO,CAC5B,CAAC,CACH,CAKA,cAAcA,EAAS,CACrB,MAAMK,EAAOL,EAAQ,sBAAqB,EACpCiB,EAAUZ,EAAK,KAAOA,EAAK,MAAQ,EACnCa,EAAUb,EAAK,IAAMA,EAAK,OAAS,EAGnCc,EAAS,KAAK,aAAa,EAAIF,EAC/BG,EAAS,KAAK,aAAa,EAAIF,EAC/BG,EAAW,KAAK,KAAKF,EAASA,EAASC,EAASA,CAAM,EAGtDE,EAAqB,KAAK,IAAID,EAAW,KAAK,QAAQ,YAAa,CAAC,EACpEE,EAAY,EAAID,EAGhBE,GAAa,KAAK,aAAa,EAAInB,EAAK,MAAQA,EAAK,MACrDoB,GAAa,KAAK,aAAa,EAAIpB,EAAK,KAAOA,EAAK,OAG1DL,EAAQ,MAAM,YAAY,aAAcwB,EAAU,QAAQ,CAAC,CAAC,EAC5DxB,EAAQ,MAAM,YAAY,aAAcyB,EAAU,QAAQ,CAAC,CAAC,EAC5DzB,EAAQ,MAAM,YAAY,oBAAqBsB,EAAmB,QAAQ,CAAC,CAAC,EAC5EtB,EAAQ,MAAM,YAAY,qBAAsBuB,EAAU,QAAQ,CAAC,CAAC,EAGhE,KAAK,QAAQ,eACf,KAAK,kBAAkBvB,EAASmB,EAAQC,EAAQG,CAAS,EAIvD,KAAK,QAAQ,gBACf,KAAK,oBAAoBvB,EAASwB,EAAWC,EAAWF,CAAS,CAErE,CAKA,kBAAkBvB,EAASmB,EAAQC,EAAQG,EAAW,CACpD,MAAMG,EAAkBH,EAAY,KAAK,QAAQ,gBAC3CI,EAAU,CAACR,EAAS,GAAMO,EAC1BE,EAAU,CAACR,EAAS,GAAMM,EAC1BG,EAAa,GAAKH,EAClBI,EAAgB,GAAMJ,EAEtBK,EAAS,GAAGJ,CAAO,MAAMC,CAAO,MAAMC,CAAU,oBAAoBC,CAAa,IACvF9B,EAAQ,MAAM,YAAY,kBAAmB+B,CAAM,CACrD,CAKA,oBAAoB/B,EAASwB,EAAWC,EAAWF,EAAW,CAC5D,MAAMS,EAAiBT,EAAY,KAAK,QAAQ,kBAG1CU,EAAYT,EAAY,IACxBU,EAAYT,EAAY,IAGxBU,EAAW,mBAFI,IAAMZ,CAEqB,gBAAgBU,CAAS,KAAKC,CAAS,0BAA0BF,CAAc,yBAC/HhC,EAAQ,MAAM,YAAY,iBAAkBmC,CAAQ,CACtD,CAKA,aAAanC,EAASM,EAAO,CAC3B,MAAMD,EAAOL,EAAQ,sBAAqB,EACpCU,EAAIJ,EAAM,QAAUD,EAAK,KACzBM,EAAIL,EAAM,QAAUD,EAAK,IAEzBO,EAAS,SAAS,cAAc,KAAK,EAC3CA,EAAO,UAAY,gBACnBA,EAAO,MAAM,QAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWVX,EAAQ,MAAM,SAAW,WACzBA,EAAQ,YAAYY,CAAM,EAG1B,WAAW,IAAM,CACXA,EAAO,YACTA,EAAO,WAAW,YAAYA,CAAM,CAExC,EAAG,GAAG,CACR,CAKA,gBAAgBZ,EAAS,CACvB,MAAMoC,EAAe9B,GAAU,CAC7B,KAAK,aAAaN,EAASM,CAAK,CAClC,EAEA,OAAAN,EAAQ,iBAAiB,QAASoC,CAAW,EAEtC,IAAM,CACXpC,EAAQ,oBAAoB,QAASoC,CAAW,CAClD,CACF,CAKA,cAAcC,EAAY,CACxB,KAAK,QAAU,CAAE,GAAG,KAAK,QAAS,GAAGA,CAAU,CACjD,CAKA,OAAQ,CACN,KAAK,SAAW,GACZ,KAAK,iBACP,qBAAqB,KAAK,cAAc,EACxC,KAAK,eAAiB,KAE1B,CAKA,QAAS,CACF,KAAK,WACR,KAAK,SAAW,GAChB,KAAK,eAAc,EAEvB,CAKA,SAAU,CACR,KAAK,MAAK,EAGV,SAAS,oBAAoB,YAAa,KAAK,eAAe,EAC9D,SAAS,oBAAoB,aAAc,KAAK,gBAAgB,EAGhE,KAAK,SAAS,QAAQrC,GAAW,CAC/B,KAAK,cAAcA,CAAO,CAC5B,CAAC,EAED,KAAK,SAAS,MAAK,CACrB,CAKA,mBAAoB,CAClB,MAAO,CAAE,GAAG,KAAK,YAAY,CAC/B,CAKA,WAAY,CACV,OAAO,KAAK,QACd,CACF,CChUO,MAAMsC,CAAc,CACzB,YAAY7C,EAAU,GAAI,CACxB,KAAK,QAAU,CACb,WAAY,GACZ,eAAgB,GAChB,eAAgB,GAChB,YAAa,EACb,QAAS,GACT,UAAW,GACX,GAAGA,CACT,EAEI,KAAK,YAAc,CAAE,MAAO,EAAG,KAAM,EAAG,MAAO,CAAC,EAChD,KAAK,kBAAoB,CAAE,MAAO,EAAG,KAAM,EAAG,MAAO,CAAC,EACtD,KAAK,SAAW,IAAI,IACpB,KAAK,SAAW,GAChB,KAAK,eAAiB,KACtB,KAAK,kBAAoB,EAC3B,CAKA,MAAM,MAAO,CACX,GAAI,MAAK,SAGT,IAAI,CAAC,KAAK,cACR,eAAQ,KAAK,kCAAkC,EACxC,GAIT,GAAI,OAAO,uBAAuB,mBAAsB,WACtD,GAAI,CACF,MAAM8C,EAAa,MAAM,uBAAuB,kBAAiB,EACjE,KAAK,kBAAoBA,IAAe,SAC1C,OAASC,EAAO,CACd,eAAQ,KAAK,wCAAyCA,CAAK,EACpD,EACT,MAEA,KAAK,kBAAoB,GAG3B,OAAK,KAAK,mBAIV,KAAK,SAAW,GAChB,KAAK,WAAU,EACf,KAAK,aAAY,EACjB,KAAK,eAAc,EAEZ,IARE,GASX,CAKA,aAAc,CACZ,MAAO,2BAA4B,MACrC,CAKA,YAAa,CACX,KAAK,wBAA0B,KAAK,wBAAwB,KAAK,IAAI,EACrE,OAAO,iBAAiB,oBAAqB,KAAK,uBAAuB,CAC3E,CAKA,cAAe,CACK,CAChB,uBACA,uBACA,eACA,kBACN,EAEc,QAAQrC,GAAY,CAC5B,SAAS,iBAAiBA,CAAQ,EAAE,QAAQH,GAAW,CACrD,KAAK,WAAWA,CAAO,CACzB,CAAC,CACH,CAAC,CACH,CAKA,WAAWA,EAAS,CACd,KAAK,SAAS,IAAIA,CAAO,IAE7B,KAAK,SAAS,IAAIA,CAAO,EAGzBA,EAAQ,MAAM,YAAY,aAAc,GAAG,EAC3CA,EAAQ,MAAM,YAAY,aAAc,GAAG,EAC3CA,EAAQ,MAAM,YAAY,aAAc,GAAG,EAC3CA,EAAQ,MAAM,YAAY,qBAAsB,GAAG,EAGnDA,EAAQ,UAAU,IAAI,mBAAmB,EAC3C,CAKA,cAAcA,EAAS,CAChB,KAAK,SAAS,IAAIA,CAAO,IAE9B,KAAK,SAAS,OAAOA,CAAO,EAC5BA,EAAQ,UAAU,OAAO,mBAAmB,EAG5CA,EAAQ,MAAM,eAAe,YAAY,EACzCA,EAAQ,MAAM,eAAe,YAAY,EACzCA,EAAQ,MAAM,eAAe,YAAY,EACzCA,EAAQ,MAAM,eAAe,oBAAoB,EACnD,CAKA,wBAAwBM,EAAO,CAC7B,KAAK,YAAY,MAAQA,EAAM,OAAS,EACxC,KAAK,YAAY,KAAOA,EAAM,MAAQ,EACtC,KAAK,YAAY,MAAQA,EAAM,OAAS,CAC1C,CAKA,gBAAiB,CACf,MAAMU,EAAU,IAAM,CACf,KAAK,WAEV,KAAK,kBAAiB,EACtB,KAAK,eAAc,EAEnB,KAAK,eAAiB,sBAAsBA,CAAO,EACrD,EAEAA,EAAO,CACT,CAKA,mBAAoB,CAClB,KAAK,kBAAkB,QAAU,KAAK,YAAY,MAAQ,KAAK,kBAAkB,OAAS,KAAK,QAAQ,UACvG,KAAK,kBAAkB,OAAS,KAAK,YAAY,KAAO,KAAK,kBAAkB,MAAQ,KAAK,QAAQ,UACpG,KAAK,kBAAkB,QAAU,KAAK,YAAY,MAAQ,KAAK,kBAAkB,OAAS,KAAK,QAAQ,SACzG,CAKA,gBAAiB,CACf,KAAK,SAAS,QAAQhB,GAAW,CAC/B,KAAK,cAAcA,CAAO,CAC5B,CAAC,CACH,CAKA,cAAcA,EAAS,CACrB,KAAM,CAAE,KAAAyC,EAAM,MAAAC,CAAK,EAAK,KAAK,kBAGvBC,EAAc,KAAK,MAAMD,EAAQ,GAAI,GAAI,CAAC,EAAI,KAAK,QAAQ,YAC3DE,EAAc,KAAK,MAAMH,EAAO,IAAK,GAAI,CAAC,EAAI,KAAK,QAAQ,YAC3DlB,EAAY,KAAK,KAAKoB,EAAcA,EAAcC,EAAcA,CAAW,EAGjF5C,EAAQ,MAAM,YAAY,aAAc2C,EAAY,QAAQ,CAAC,CAAC,EAC9D3C,EAAQ,MAAM,YAAY,aAAc4C,EAAY,QAAQ,CAAC,CAAC,EAC9D5C,EAAQ,MAAM,YAAY,qBAAsB,KAAK,IAAIuB,EAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,EAG7E,KAAK,QAAQ,YACf,KAAK,gBAAgBvB,EAAS2C,EAAaC,CAAW,EAGpD,KAAK,QAAQ,gBACf,KAAK,oBAAoB5C,EAAS2C,EAAaC,CAAW,EAGxD,KAAK,QAAQ,gBACf,KAAK,oBAAoB5C,EAAS2C,EAAaC,EAAarB,CAAS,CAEzE,CAKA,gBAAgBvB,EAASU,EAAGC,EAAG,CAC7B,MAAMkC,EAAQlC,EAAI,KAAK,QAAQ,QACzBmC,EAAQ,CAACpC,EAAI,KAAK,QAAQ,QAEhCV,EAAQ,MAAM,YAAY,kBAAmB,GAAG6C,CAAK,KAAK,EAC1D7C,EAAQ,MAAM,YAAY,kBAAmB,GAAG8C,CAAK,KAAK,CAC5D,CAKA,oBAAoB9C,EAASU,EAAGC,EAAG,CACjC,MAAMoC,EAAYrC,EAAI,GAChBsC,EAAYrC,EAAI,GAEtBX,EAAQ,MAAM,YAAY,sBAAuB,GAAG+C,CAAS,IAAI,EACjE/C,EAAQ,MAAM,YAAY,sBAAuB,GAAGgD,CAAS,IAAI,CACnE,CAKA,oBAAoBhD,EAASU,EAAGC,EAAGY,EAAW,CAE5C,MAAMU,GAAavB,EAAI,GAAK,GACtBwB,GAAavB,EAAI,GAAK,GACtBqB,EAAiBT,EAAY,GAE7BY,EAAW,6BAA6BF,CAAS,KAAKC,CAAS,0BAA0BF,CAAc,yBAC7GhC,EAAQ,MAAM,YAAY,iBAAkBmC,CAAQ,CACtD,CAKA,YAAYnC,EAASuB,EAAY,EAAG,CAClC,MAAM0B,EAAa,gBAAgB,KAAK,MAAM1B,EAAY,CAAC,EAAI,CAAC,GAChEvB,EAAQ,UAAU,IAAIiD,CAAU,EAEhC,WAAW,IAAM,CACfjD,EAAQ,UAAU,OAAOiD,CAAU,CACrC,EAAG,GAAG,CACR,CAKA,MAAMC,EAAOC,EAAKC,EAAK,CACrB,OAAO,KAAK,IAAI,KAAK,IAAIF,EAAOC,CAAG,EAAGC,CAAG,CAC3C,CAKA,MAAM,mBAAoB,CACxB,GAAI,OAAO,uBAAuB,mBAAsB,WACtD,GAAI,CACF,MAAMb,EAAa,MAAM,uBAAuB,kBAAiB,EACjE,YAAK,kBAAoBA,IAAe,UACjC,KAAK,iBACd,OAASC,EAAO,CACd,eAAQ,MAAM,kDAAmDA,CAAK,EAC/D,EACT,CAGF,YAAK,kBAAoB,GAClB,EACT,CAKA,WAAY,CAEV,KAAK,YAAc,CAAE,MAAO,EAAG,KAAM,EAAG,MAAO,CAAC,EAChD,KAAK,kBAAoB,CAAE,MAAO,EAAG,KAAM,EAAG,MAAO,CAAC,CACxD,CAKA,cAAcH,EAAY,CACxB,KAAK,QAAU,CAAE,GAAG,KAAK,QAAS,GAAGA,CAAU,CACjD,CAKA,OAAQ,CACN,KAAK,SAAW,GACZ,KAAK,iBACP,qBAAqB,KAAK,cAAc,EACxC,KAAK,eAAiB,KAE1B,CAKA,QAAS,CACH,CAAC,KAAK,UAAY,KAAK,oBACzB,KAAK,SAAW,GAChB,KAAK,eAAc,EAEvB,CAKA,SAAU,CACR,KAAK,MAAK,EAGV,OAAO,oBAAoB,oBAAqB,KAAK,uBAAuB,EAG5E,KAAK,SAAS,QAAQrC,GAAW,CAC/B,KAAK,cAAcA,CAAO,CAC5B,CAAC,EAED,KAAK,SAAS,MAAK,CACrB,CAKA,gBAAiB,CACf,MAAO,CAAE,GAAG,KAAK,iBAAiB,CACpC,CAKA,WAAY,CACV,OAAO,KAAK,UAAY,KAAK,iBAC/B,CACF,CCjVO,MAAMqD,CAAY,CACvB,YAAYrD,EAASP,EAAU,GAAI,CACjC,KAAK,QAAUO,EACf,KAAK,QAAU,CACb,aAAc,GACd,YAAa,GACb,YAAa,GACb,cAAe,GACf,YAAa,2BACb,GAAGP,CACT,EAEI,KAAK,UAAY,GACjB,KAAK,QAAU,IAAI,IAEnB,KAAK,KAAI,CACX,CAKA,MAAO,CACL,KAAK,aAAY,EACjB,KAAK,WAAU,EACf,KAAK,mBAAkB,CACzB,CAKA,cAAe,CAER,KAAK,QAAQ,UAAU,SAAS,WAAW,GAC9C,KAAK,QAAQ,UAAU,IAAI,WAAW,EAIxC,KAAK,QAAQ,aAAa,oBAAqB,aAAa,EAG5D,KAAK,YAAW,CAClB,CAKA,YAAa,CAEX,KAAK,QAAQ,iBAAiB,QAAS,KAAK,YAAY,KAAK,IAAI,CAAC,EAG9D,KAAK,QAAQ,cACf,KAAK,QAAQ,iBAAiB,aAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,EAC5E,KAAK,QAAQ,iBAAiB,aAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,GAI1E,KAAK,QAAQ,cACf,KAAK,QAAQ,iBAAiB,QAAS,KAAK,YAAY,KAAK,IAAI,CAAC,EAClE,KAAK,QAAQ,iBAAiB,OAAQ,KAAK,WAAW,KAAK,IAAI,CAAC,GAIlE,KAAK,QAAQ,iBAAiB,UAAW,KAAK,cAAc,KAAK,IAAI,CAAC,CACxE,CAKA,oBAAqB,CAEf,CAAC,KAAK,QAAQ,aAAa,MAAM,GAAK,KAAK,QAAQ,UAAY,UACjE,KAAK,QAAQ,aAAa,OAAQ,QAAQ,EAIxC,CAAC,KAAK,QAAQ,aAAa,UAAU,GAAK,KAAK,QAAQ,UAAY,UACrE,KAAK,QAAQ,aAAa,WAAY,GAAG,EAIvC,KAAK,QAAQ,eACf,KAAK,QAAQ,aAAa,YAAa,OAAO,CAElD,CAKA,YAAYa,EAAO,CACjB,GAAI,KAAK,WAAa,KAAK,QAAQ,SAAU,CAC3CA,EAAM,eAAc,EACpB,MACF,CAGI,KAAK,QAAQ,cACf,KAAK,aAAaA,CAAK,EAIzB,KAAK,cAAc,qBAAsB,CAAE,cAAeA,CAAK,CAAE,CACnE,CAKA,iBAAiBA,EAAO,CACtB,KAAK,QAAQ,UAAU,IAAI,iBAAiB,EAC5C,KAAK,cAAc,qBAAsB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACnF,CAKA,iBAAiBA,EAAO,CACtB,KAAK,QAAQ,UAAU,OAAO,iBAAiB,EAC/C,KAAK,cAAc,qBAAsB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACnF,CAKA,YAAYA,EAAO,CACjB,KAAK,QAAQ,UAAU,IAAI,iBAAiB,EAC5C,KAAK,cAAc,qBAAsB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACnF,CAKA,WAAWA,EAAO,CAChB,KAAK,QAAQ,UAAU,OAAO,iBAAiB,EAC/C,KAAK,cAAc,qBAAsB,CAAE,MAAO,OAAQ,cAAeA,CAAK,CAAE,CAClF,CAKA,cAAcA,EAAO,EAEfA,EAAM,MAAQ,SAAWA,EAAM,MAAQ,OACzCA,EAAM,eAAc,EACpB,KAAK,QAAQ,MAAK,EAEtB,CAKA,aAAaA,EAAO,CAClB,MAAMD,EAAO,KAAK,QAAQ,sBAAqB,EACzCK,EAAIJ,EAAM,QAAUD,EAAK,KACzBM,EAAIL,EAAM,QAAUD,EAAK,IAEzBO,EAAS,SAAS,cAAc,KAAK,EAC3CA,EAAO,UAAY,mBACnBA,EAAO,MAAM,QAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIM,KAAK,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,MAQpC,iBAAiB,KAAK,OAAO,EAAE,WAAa,WAC9C,KAAK,QAAQ,MAAM,SAAW,YAGhC,KAAK,QAAQ,YAAYC,CAAM,EAC/B,KAAK,QAAQ,IAAIA,CAAM,EAGvB,WAAW,IAAM,CACXA,EAAO,YACTA,EAAO,WAAW,YAAYA,CAAM,EAEtC,KAAK,QAAQ,OAAOA,CAAM,CAC5B,EAAG,GAAG,CACR,CAKA,WAAW0C,EAAU,GAAM,CACzB,KAAK,UAAYA,EAEbA,GACF,KAAK,QAAQ,UAAU,IAAI,mBAAmB,EAC9C,KAAK,QAAQ,aAAa,YAAa,MAAM,EAC7C,KAAK,QAAQ,SAAW,KAExB,KAAK,QAAQ,UAAU,OAAO,mBAAmB,EACjD,KAAK,QAAQ,aAAa,YAAa,OAAO,EAC9C,KAAK,QAAQ,SAAW,IAG1B,KAAK,YAAW,EAChB,KAAK,cAAc,uBAAwB,CAAE,QAAAA,CAAO,CAAE,CACxD,CAKA,YAAYC,EAAW,GAAM,CAC3B,KAAK,QAAQ,SAAWA,EACxB,KAAK,QAAQ,UAAU,OAAO,qBAAsBA,CAAQ,EAC5D,KAAK,YAAW,EAChB,KAAK,cAAc,wBAAyB,CAAE,SAAAA,CAAQ,CAAE,CAC1D,CAKA,WAAWC,EAAS,CAElB,MAAMC,EAAiB,CACrB,oBACA,sBACA,oBACA,oBACA,mBACA,iBACN,EAEI,KAAK,QAAQ,UAAU,OAAO,GAAGA,CAAc,EAG3CD,GAAWA,IAAY,WACzB,KAAK,QAAQ,UAAU,IAAI,aAAaA,CAAO,EAAE,EAGnD,KAAK,cAAc,uBAAwB,CAAE,QAAAA,CAAO,CAAE,CACxD,CAKA,QAAQE,EAAM,CAEZ,MAAMC,EAAc,CAAC,eAAgB,eAAgB,eAAgB,cAAc,EACnF,KAAK,QAAQ,UAAU,OAAO,GAAGA,CAAW,EAGxCD,GAAQA,IAAS,WACnB,KAAK,QAAQ,UAAU,IAAI,aAAaA,CAAI,EAAE,EAGhD,KAAK,cAAc,oBAAqB,CAAE,KAAAA,CAAI,CAAE,CAClD,CAKA,aAAc,CACZ,MAAME,EAAQ,CACZ,QAAS,KAAK,UACd,SAAU,KAAK,QAAQ,SACvB,QAAS,KAAK,QAAQ,UAAU,SAAS,iBAAiB,EAC1D,QAAS,KAAK,QAAQ,UAAU,SAAS,iBAAiB,CAChE,EAEI,KAAK,QAAQ,aAAa,aAAc,KAAK,UAAUA,CAAK,CAAC,CAC/D,CAKA,cAAcC,EAAWC,EAAS,GAAI,CACpC,MAAMxD,EAAQ,IAAI,YAAYuD,EAAW,CACvC,OAAQ,CACN,OAAQ,KACR,QAAS,KAAK,QACd,GAAGC,CACX,EACM,QAAS,GACT,WAAY,EAClB,CAAK,EAED,KAAK,QAAQ,cAAcxD,CAAK,CAClC,CAKA,aAAaO,EAAekD,EAAW,IAAM,CAC3C,MAAMC,EAAiB,qBAAqBnD,CAAa,GACzD,KAAK,QAAQ,UAAU,IAAImD,CAAc,EAEzC,WAAW,IAAM,CACf,KAAK,QAAQ,UAAU,OAAOA,CAAc,CAC9C,EAAGD,CAAQ,CACb,CAKA,cAAc1B,EAAY,CACxB,KAAK,QAAU,CAAE,GAAG,KAAK,QAAS,GAAGA,CAAU,CACjD,CAKA,UAAW,CACT,MAAO,CACL,QAAS,KAAK,UACd,SAAU,KAAK,QAAQ,SACvB,QAAS,KAAK,WAAU,EACxB,KAAM,KAAK,QAAO,CACxB,CACE,CAKA,YAAa,CAEX,MADiB,CAAC,UAAW,YAAa,UAAW,UAAW,SAAU,OAAO,EACjE,KAAKmB,GAAW,KAAK,QAAQ,UAAU,SAAS,aAAaA,CAAO,EAAE,CAAC,GAAK,SAC9F,CAKA,SAAU,CAER,MADc,CAAC,KAAM,KAAM,KAAM,IAAI,EACxB,KAAKE,GAAQ,KAAK,QAAQ,UAAU,SAAS,aAAaA,CAAI,EAAE,CAAC,GAAK,SACrF,CAKA,SAAU,CAER,KAAK,QAAQ,QAAQ9C,GAAU,CACzBA,EAAO,YACTA,EAAO,WAAW,YAAYA,CAAM,CAExC,CAAC,EACD,KAAK,QAAQ,MAAK,EAKlB,KAAK,QAAQ,gBAAgB,mBAAmB,EAGhD,KAAK,QAAQ,UAAU,OAAO,kBAAmB,kBAAmB,oBAAqB,oBAAoB,CAC/G,CACF,CCpWO,MAAMqD,CAAU,CACrB,YAAYjE,EAASP,EAAU,GAAI,CACjC,KAAK,QAAUO,EACf,KAAK,QAAU,CACb,YAAa,GACb,YAAa,GACb,cAAe,GACf,YAAa,OACb,GAAGP,CACT,EAEI,KAAK,UAAY,GAEjB,KAAK,KAAI,CACX,CAKA,MAAO,CACL,KAAK,aAAY,EACjB,KAAK,WAAU,EACf,KAAK,mBAAkB,CACzB,CAKA,cAAe,CAER,KAAK,QAAQ,UAAU,SAAS,YAAY,GAC/C,KAAK,QAAQ,UAAU,IAAI,YAAY,EAIzC,KAAK,QAAQ,aAAa,kBAAmB,aAAa,EAG1D,KAAK,YAAW,CAClB,CAKA,YAAa,CAEP,KAAK,QAAQ,aAAe,KAAK,QAAQ,UAAU,SAAS,wBAAwB,GACtF,KAAK,QAAQ,iBAAiB,QAAS,KAAK,YAAY,KAAK,IAAI,CAAC,EAIhE,KAAK,QAAQ,cACf,KAAK,QAAQ,iBAAiB,aAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,EAC5E,KAAK,QAAQ,iBAAiB,aAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,GAI9E,KAAK,QAAQ,iBAAiB,QAAS,KAAK,YAAY,KAAK,IAAI,CAAC,EAClE,KAAK,QAAQ,iBAAiB,OAAQ,KAAK,WAAW,KAAK,IAAI,CAAC,EAG5D,KAAK,QAAQ,UAAU,SAAS,wBAAwB,GAC1D,KAAK,QAAQ,iBAAiB,UAAW,KAAK,cAAc,KAAK,IAAI,CAAC,CAE1E,CAKA,oBAAqB,CAEf,KAAK,QAAQ,UAAU,SAAS,wBAAwB,IACrD,KAAK,QAAQ,aAAa,UAAU,GACvC,KAAK,QAAQ,aAAa,WAAY,GAAG,EAEtC,KAAK,QAAQ,aAAa,MAAM,GACnC,KAAK,QAAQ,aAAa,OAAQ,QAAQ,GAK1C,KAAK,QAAQ,eACf,KAAK,QAAQ,aAAa,YAAa,OAAO,CAElD,CAKA,YAAYa,EAAO,CACjB,GAAI,KAAK,UAAW,CAClBA,EAAM,eAAc,EACpB,MACF,CAGA,KAAK,cAAc,mBAAoB,CAAE,cAAeA,CAAK,CAAE,CACjE,CAKA,iBAAiBA,EAAO,CAItB,OAHA,KAAK,QAAQ,UAAU,IAAI,kBAAkB,EAGrC,KAAK,QAAQ,YAAW,CAC9B,IAAK,OACH,KAAK,QAAQ,UAAU,IAAI,iBAAiB,EAC5C,MACF,IAAK,QACH,KAAK,QAAQ,UAAU,IAAI,kBAAkB,EAC7C,KAIR,CAEI,KAAK,cAAc,mBAAoB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACjF,CAKA,iBAAiBA,EAAO,CACtB,KAAK,QAAQ,UAAU,OAAO,mBAAoB,kBAAmB,kBAAkB,EACvF,KAAK,cAAc,mBAAoB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACjF,CAKA,YAAYA,EAAO,CACjB,KAAK,QAAQ,UAAU,IAAI,kBAAkB,EAC7C,KAAK,cAAc,mBAAoB,CAAE,MAAO,QAAS,cAAeA,CAAK,CAAE,CACjF,CAKA,WAAWA,EAAO,CAChB,KAAK,QAAQ,UAAU,OAAO,kBAAkB,EAChD,KAAK,cAAc,mBAAoB,CAAE,MAAO,OAAQ,cAAeA,CAAK,CAAE,CAChF,CAKA,cAAcA,EAAO,EAEfA,EAAM,MAAQ,SAAWA,EAAM,MAAQ,OACzCA,EAAM,eAAc,EACpB,KAAK,QAAQ,MAAK,EAEtB,CAKA,WAAWgD,EAAU,GAAM,CACzB,KAAK,UAAYA,EAEbA,GACF,KAAK,QAAQ,UAAU,IAAI,oBAAoB,EAC/C,KAAK,QAAQ,aAAa,YAAa,MAAM,IAE7C,KAAK,QAAQ,UAAU,OAAO,oBAAoB,EAClD,KAAK,QAAQ,aAAa,YAAa,OAAO,GAGhD,KAAK,YAAW,EAChB,KAAK,cAAc,qBAAsB,CAAE,QAAAA,CAAO,CAAE,CACtD,CAKA,WAAWE,EAAS,CAElB,MAAMC,EAAiB,CACrB,sBACA,oBACA,sBACA,mBACN,EAEI,KAAK,QAAQ,UAAU,OAAO,GAAGA,CAAc,EAG3CD,GAAWA,IAAY,WACzB,KAAK,QAAQ,UAAU,IAAI,cAAcA,CAAO,EAAE,EAGpD,KAAK,cAAc,qBAAsB,CAAE,QAAAA,CAAO,CAAE,CACtD,CAKA,QAAQE,EAAM,CAEZ,MAAMC,EAAc,CAAC,gBAAiB,eAAe,EACrD,KAAK,QAAQ,UAAU,OAAO,GAAGA,CAAW,EAGxCD,GAAQA,IAAS,WACnB,KAAK,QAAQ,UAAU,IAAI,cAAcA,CAAI,EAAE,EAGjD,KAAK,cAAc,kBAAmB,CAAE,KAAAA,CAAI,CAAE,CAChD,CAKA,SAASQ,EAAMzE,EAAU,GAAI,CAC3B,MAAM0E,EAAe,CACnB,SAAU,YACV,QAAS,UACT,GAAG1E,CACT,EAGI,KAAK,YAAW,EAEhB,MAAM2E,EAAQ,SAAS,cAAc,KAAK,EAC1CA,EAAM,UAAY,qCAAqCD,EAAa,QAAQ,GAC5EC,EAAM,YAAcF,EAEhBC,EAAa,UAAY,WAC3BC,EAAM,UAAU,IAAI,oBAAoBD,EAAa,OAAO,EAAE,EAGhE,KAAK,QAAQ,YAAYC,CAAK,EAC9B,KAAK,cAAc,yBAA0B,CAAE,KAAAF,EAAM,QAASC,CAAY,CAAE,CAC9E,CAKA,aAAc,CACZ,MAAME,EAAgB,KAAK,QAAQ,cAAc,mBAAmB,EAChEA,IACFA,EAAc,OAAM,EACpB,KAAK,cAAc,0BAA0B,EAEjD,CAKA,cAAcC,EAAS,CACrB,MAAMC,EAAO,KAAK,QAAQ,cAAc,kBAAkB,EACtDA,IACE,OAAOD,GAAY,SACrBC,EAAK,UAAYD,EACRA,aAAmB,cAC5BC,EAAK,UAAY,GACjBA,EAAK,YAAYD,CAAO,GAE1B,KAAK,cAAc,6BAA8B,CAAE,QAAAA,CAAO,CAAE,EAEhE,CAKA,aAAc,CACZ,MAAMV,EAAQ,CACZ,QAAS,KAAK,UACd,YAAa,KAAK,QAAQ,UAAU,SAAS,wBAAwB,EACrE,QAAS,KAAK,QAAQ,UAAU,SAAS,kBAAkB,EAC3D,QAAS,KAAK,QAAQ,UAAU,SAAS,kBAAkB,CACjE,EAEI,KAAK,QAAQ,aAAa,aAAc,KAAK,UAAUA,CAAK,CAAC,CAC/D,CAKA,cAAcC,EAAWC,EAAS,GAAI,CACpC,MAAMxD,EAAQ,IAAI,YAAYuD,EAAW,CACvC,OAAQ,CACN,KAAM,KACN,QAAS,KAAK,QACd,GAAGC,CACX,EACM,QAAS,GACT,WAAY,EAClB,CAAK,EAED,KAAK,QAAQ,cAAcxD,CAAK,CAClC,CAKA,aAAaO,EAAekD,EAAW,IAAM,CAC3C,MAAMC,EAAiB,sBAAsBnD,CAAa,GAC1D,KAAK,QAAQ,UAAU,IAAImD,CAAc,EAEzC,WAAW,IAAM,CACf,KAAK,QAAQ,UAAU,OAAOA,CAAc,CAC9C,EAAGD,CAAQ,CACb,CAKA,cAAc1B,EAAY,CACxB,KAAK,QAAU,CAAE,GAAG,KAAK,QAAS,GAAGA,CAAU,CACjD,CAKA,UAAW,CACT,MAAO,CACL,QAAS,KAAK,UACd,YAAa,KAAK,QAAQ,UAAU,SAAS,wBAAwB,EACrE,QAAS,KAAK,WAAU,EACxB,KAAM,KAAK,QAAO,CACxB,CACE,CAKA,YAAa,CAEX,MADiB,CAAC,WAAY,SAAU,WAAY,QAAQ,EAC5C,KAAKmB,GAAW,KAAK,QAAQ,UAAU,SAAS,cAAcA,CAAO,EAAE,CAAC,GAAK,SAC/F,CAKA,SAAU,CAER,MADc,CAAC,KAAM,IAAI,EACZ,KAAKE,GAAQ,KAAK,QAAQ,UAAU,SAAS,cAAcA,CAAI,EAAE,CAAC,GAAK,SACtF,CAKA,SAAU,CAER,KAAK,QAAQ,gBAAgB,iBAAiB,EAG9C,KAAK,QAAQ,UAAU,OAAO,mBAAoB,mBAAoB,qBAAsB,kBAAmB,kBAAkB,EAGjI,KAAK,YAAW,CAClB,CACF,CCnWO,MAAMc,CAAW,CACtB,YAAYxE,EAASP,EAAU,GAAI,CACjC,KAAK,QAAUO,EACf,KAAK,QAAU,CACb,SAAU,GACV,SAAU,GACV,MAAO,GACP,GAAGP,CACT,EAEI,KAAK,OAAS,GACd,KAAK,KAAI,CACX,CAEA,MAAO,CACL,KAAK,QAAQ,aAAa,mBAAoB,aAAa,CAC7D,CAEA,MAAO,CACL,KAAK,OAAS,GACd,KAAK,QAAQ,UAAU,IAAI,kBAAkB,CAC/C,CAEA,OAAQ,CACN,KAAK,OAAS,GACd,KAAK,QAAQ,UAAU,OAAO,kBAAkB,CAClD,CAEA,SAAU,CACR,KAAK,QAAQ,gBAAgB,kBAAkB,CACjD,CACF,CC/BO,MAAMgF,CAAgB,CAC3B,YAAYzE,EAASP,EAAU,GAAI,CACjC,KAAK,QAAUO,EACf,KAAK,QAAU,CACb,OAAQ,GACR,YAAa,GACb,GAAGP,CACT,EAEI,KAAK,KAAI,CACX,CAEA,MAAO,CACL,KAAK,QAAQ,aAAa,iBAAkB,aAAa,CAC3D,CAEA,SAAU,CACR,KAAK,QAAQ,gBAAgB,gBAAgB,CAC/C,CACF,CCnBO,MAAMiF,CAAa,CACxB,YAAYC,EAAe,OAAQ,CACjC,KAAK,aAAeA,EACpB,KAAK,YAAc,KAAK,eAAc,EACtC,KAAK,WAAa,OAAO,WAAW,8BAA8B,EAClE,KAAK,UAAY,IAAI,IAErB,KAAK,WAAa,uBACpB,CAKA,MAAO,CAEL,MAAMC,EAAa,KAAK,UAAS,EAC7BA,IACF,KAAK,aAAeA,GAItB,KAAK,WAAU,EAGf,KAAK,WAAW,iBAAiB,SAAU,KAAK,wBAAwB,KAAK,IAAI,CAAC,EAGlF,KAAK,iBAAgB,CACvB,CAKA,gBAAiB,CACf,OAAO,KAAK,WAAW,QAAU,OAAS,OAC5C,CAKA,mBAAoB,CAClB,OAAI,KAAK,eAAiB,OACjB,KAAK,YAEP,KAAK,YACd,CAKA,SAASC,EAAO,CACT,CAAC,QAAS,OAAQ,MAAM,EAAE,SAASA,CAAK,IAC3C,QAAQ,KAAK,kBAAkBA,CAAK,yBAAyB,EAC7DA,EAAQ,QAGV,KAAK,aAAeA,EACpB,KAAK,WAAU,EACf,KAAK,UAAS,EACd,KAAK,gBAAe,CACtB,CAKA,aAAc,CAEZ,MAAMC,EADiB,KAAK,kBAAiB,IACT,QAAU,OAAS,QACvD,KAAK,SAASA,CAAQ,CACxB,CAKA,YAAa,CACX,MAAMC,EAAiB,KAAK,kBAAiB,EAG7C,SAAS,gBAAgB,UAAU,OAAO,cAAe,YAAY,EACrE,SAAS,gBAAgB,gBAAgB,YAAY,EAGrD,SAAS,gBAAgB,UAAU,IAAI,SAASA,CAAc,EAAE,EAChE,SAAS,gBAAgB,aAAa,aAAcA,CAAc,EAGlE,KAAK,qBAAqBA,CAAc,EAGxC,KAAK,mBAAmBA,CAAc,CACxC,CAKA,qBAAqBF,EAAO,CAC1B,IAAIG,EAAiB,SAAS,cAAc,0BAA0B,EAEjEA,IACHA,EAAiB,SAAS,cAAc,MAAM,EAC9CA,EAAe,KAAO,cACtB,SAAS,KAAK,YAAYA,CAAc,GAG1C,MAAMC,EAAS,CACb,MAAO,UACP,KAAM,SACZ,EAEID,EAAe,QAAUC,EAAOJ,CAAK,GAAKI,EAAO,KACnD,CAKA,mBAAmBJ,EAAO,CACxB,MAAMvE,EAAQ,IAAI,YAAY,cAAe,CAC3C,OAAQ,CACN,MAAAuE,EACA,cAAe,KAAK,cACpB,cAAe,KAAK,eAAiB,MAC7C,CACA,CAAK,EAED,KAAK,cAAgBA,EACrB,SAAS,cAAcvE,CAAK,CAC9B,CAKA,wBAAwBA,EAAO,CAC7B,KAAK,YAAcA,EAAM,QAAU,OAAS,QAExC,KAAK,eAAiB,SACxB,KAAK,WAAU,EACf,KAAK,gBAAe,EAExB,CAKA,kBAAmB,CACD,SAAS,iBAAiB,qBAAqB,EAEvD,QAAQ4E,GAAU,CACxBA,EAAO,iBAAiB,QAAS,IAAM,CACrC,KAAK,YAAW,CAClB,CAAC,CACH,CAAC,EAGiB,SAAS,iBAAiB,uBAAuB,EAEzD,QAAQ/E,GAAY,CAC5BA,EAAS,iBAAiB,SAAWG,GAAU,CAC7C,KAAK,SAASA,EAAM,OAAO,KAAK,CAClC,CAAC,EAGDH,EAAS,MAAQ,KAAK,YACxB,CAAC,CACH,CAKA,YAAYgF,EAAU,CACpB,YAAK,UAAU,IAAIA,CAAQ,EAEpB,IAAM,CACX,KAAK,UAAU,OAAOA,CAAQ,CAChC,CACF,CAKA,iBAAkB,CAChB,MAAMJ,EAAiB,KAAK,kBAAiB,EAE7C,KAAK,UAAU,QAAQI,GAAY,CACjC,GAAI,CACFA,EAASJ,EAAgB,KAAK,YAAY,CAC5C,OAASvC,EAAO,CACd,QAAQ,MAAM,kCAAmCA,CAAK,CACxD,CACF,CAAC,CACH,CAKA,WAAY,CACV,GAAI,CACF,aAAa,QAAQ,KAAK,WAAY,KAAK,YAAY,CACzD,OAASA,EAAO,CACd,QAAQ,KAAK,mCAAoCA,CAAK,CACxD,CACF,CAKA,WAAY,CACV,GAAI,CACF,OAAO,aAAa,QAAQ,KAAK,UAAU,CAC7C,OAASA,EAAO,CACd,eAAQ,KAAK,mCAAoCA,CAAK,EAC/C,IACT,CACF,CAKA,cAAc4C,EAAU,CACtB,OAAO,iBAAiB,SAAS,eAAe,EAAE,iBAAiBA,CAAQ,CAC7E,CAKA,cAAcA,EAAUlC,EAAO,CAC7B,SAAS,gBAAgB,MAAM,YAAYkC,EAAUlC,CAAK,CAC5D,CAKA,kBAAkBmC,EAAYC,EAAW,CAEvC,OADuB,KAAK,kBAAiB,IACnB,OAASA,EAAYD,CACjD,CAKA,SAAU,CACR,KAAK,WAAW,oBAAoB,SAAU,KAAK,wBAAwB,KAAK,IAAI,CAAC,EACrF,KAAK,UAAU,MAAK,CACtB,CAKA,cAAe,CACb,MAAO,CACL,QAAS,KAAK,aACd,UAAW,KAAK,kBAAiB,EACjC,OAAQ,KAAK,YACb,OAAQ,KAAK,eAAiB,MACpC,CACE,CACF,CC/PO,MAAME,CAAe,CAC1B,aAAc,CACZ,KAAK,UAAY,UAAU,UAAU,YAAW,EAChD,KAAK,aAAe,KAAK,mBAAkB,CAC7C,CAKA,UAAW,CACT,MAAO,iEAAiE,KAAK,KAAK,SAAS,CAC7F,CAKA,UAAW,CACT,MAAO,4BAA4B,KAAK,KAAK,SAAS,CACxD,CAKA,WAAY,CACV,MAAO,CAAC,KAAK,YAAc,CAAC,KAAK,SAAQ,CAC3C,CAKA,OAAQ,CACN,MAAO,oBAAoB,KAAK,KAAK,SAAS,CAChD,CAKA,WAAY,CACV,MAAO,WAAW,KAAK,KAAK,SAAS,CACvC,CAKA,eAAgB,CACd,MAAO,iBAAkB,QAAU,UAAU,eAAiB,CAChE,CAKA,2BAA4B,CAC1B,MAAO,2BAA4B,MACrC,CAKA,sBAAuB,CACrB,MAAO,sBAAuB,MAChC,CAKA,wBAAyB,CACvB,OAAO,IAAI,SAAS,kBAAmB,WAAW,GAC3C,IAAI,SAAS,0BAA2B,WAAW,CAC5D,CAKA,sBAAuB,CACrB,OAAO,OAAO,WAAW,kCAAkC,EAAE,OAC/D,CAKA,eAAgB,CACd,OAAO,OAAO,kBAAoB,CACpC,CAKA,aAAc,CACZ,MAAO,CACL,MAAO,OAAO,WACd,OAAQ,OAAO,WACrB,CACE,CAKA,oBAAqB,CAEnB,OAAO,IAAI,QAASC,GAAY,CAC9B,IAAIC,EAAS,EACb,MAAMC,EAAQ,YAAY,IAAG,EAEvBC,EAAa,IAAM,CAEvB,GADAF,IACIA,EAAS,GACX,sBAAsBE,CAAU,MAC3B,CACL,MAAM5B,EAAW,YAAY,MAAQ2B,EAC/BE,EAAM,KAAK,MAAMH,GAAU1B,EAAW,IAAK,EACjDyB,EAAQI,EAAM,EAAE,CAClB,CACF,EAEA,sBAAsBD,CAAU,CAClC,CAAC,CACH,CAKA,oBAAqB,CACnB,MAAO,CACL,SAAU,KAAK,SAAQ,EACvB,SAAU,KAAK,SAAQ,EACvB,UAAW,KAAK,UAAS,EACzB,MAAO,KAAK,MAAK,EACjB,UAAW,KAAK,UAAS,EACzB,cAAe,KAAK,cAAa,EACjC,0BAA2B,KAAK,0BAAyB,EACzD,qBAAsB,KAAK,qBAAoB,EAC/C,uBAAwB,KAAK,uBAAsB,EACnD,qBAAsB,KAAK,qBAAoB,EAC/C,WAAY,KAAK,cAAa,EAC9B,SAAU,KAAK,YAAW,CAChC,CACE,CAKA,oBAAqB,CACnB,MAAME,EAAW,CACf,cAAe,SACf,kBAAmB,SACnB,oBAAqB,GACrB,oBAAqB,GACrB,eAAgB,GAChB,oBAAqB,EAC3B,EAGI,OAAI,KAAK,cACPA,EAAS,oBAAsB,GAC/BA,EAAS,cAAgB,OACzBA,EAAS,oBAAsB,IAI7B,KAAK,aACPA,EAAS,oBAAsB,KAAK,0BAAyB,EAC7DA,EAAS,cAAgB,MACzBA,EAAS,kBAAoB,QAI3B,KAAK,cAAa,EAAK,IACzBA,EAAS,eAAiB,IAIxB,KAAK,yBACPA,EAAS,kBAAoB,OAC7BA,EAAS,oBAAsB,GAC/BA,EAAS,eAAiB,IAIvB,KAAK,2BACRA,EAAS,cAAgB,QAGpBA,CACT,CAKA,kBAAmB,CACjB,MAAMC,EAAU,CAAA,EAEZ,KAAK,SAAQ,GAAIA,EAAQ,KAAK,eAAe,EAC7C,KAAK,SAAQ,GAAIA,EAAQ,KAAK,eAAe,EAC7C,KAAK,UAAS,GAAIA,EAAQ,KAAK,gBAAgB,EAC/C,KAAK,MAAK,GAAIA,EAAQ,KAAK,YAAY,EACvC,KAAK,UAAS,GAAIA,EAAQ,KAAK,gBAAgB,EAC/C,KAAK,cAAa,GAAIA,EAAQ,KAAK,gBAAgB,EACnD,KAAK,uBAAsB,GAAIA,EAAQ,KAAK,0BAA0B,EACtE,KAAK,qBAAoB,GAAIA,EAAQ,KAAK,wBAAwB,EAEtE,SAAS,gBAAgB,UAAU,IAAI,GAAGA,CAAO,CACnD,CAKA,iBAAiBX,EAAU,CACzB,IAAIY,EACJ,MAAMC,EAAe,IAAM,CACzB,aAAaD,CAAO,EACpBA,EAAU,WAAW,IAAM,CACzB,KAAK,aAAa,SAAW,KAAK,YAAW,EAC7CZ,EAAS,KAAK,aAAa,QAAQ,CACrC,EAAG,GAAG,CACR,EAEA,cAAO,iBAAiB,SAAUa,CAAY,EAC9C,OAAO,iBAAiB,oBAAqBA,CAAY,EAElD,IAAM,CACX,OAAO,oBAAoB,SAAUA,CAAY,EACjD,OAAO,oBAAoB,oBAAqBA,CAAY,CAC9D,CACF,CACF,CC3MA,MAAMC,CAAc,CAClB,YAAYxG,EAAU,GAAI,CACxB,KAAK,QAAU,CACb,oBAAqB,GACrB,oBAAqB,GACrB,iBAAkB,GAClB,MAAO,OACP,GAAGA,CACT,EAEI,KAAK,QAAU,IAAID,EAAa,KAAK,OAAO,EAC5C,KAAK,aAAe,IAAIkF,EAAa,KAAK,QAAQ,KAAK,EACvD,KAAK,eAAiB,IAAIa,EAE1B,KAAK,KAAI,CACX,CAKA,MAAO,CAEL,KAAK,aAAa,KAAI,EAGlB,KAAK,eAAe,UAAS,GAAM,KAAK,QAAQ,sBAClD,KAAK,cAAgB,IAAIxE,EACzB,KAAK,cAAc,KAAI,GAGrB,KAAK,eAAe,SAAQ,GAAM,KAAK,QAAQ,sBACjD,KAAK,cAAgB,IAAIuB,EACzB,KAAK,cAAc,KAAI,GAIzB,KAAK,eAAc,EAGnB,SAAS,gBAAgB,aAAa,uBAAwB,aAAa,CAC7E,CAKA,gBAAiB,CAEf,KAAK,YAAW,EAChB,KAAK,UAAS,EACd,KAAK,WAAU,EACf,KAAK,eAAc,CACrB,CAKA,aAAc,CACI,SAAS,iBAAiB,qBAAqB,EACvD,QAAQ4D,GAAU,IAAI7C,EAAY6C,CAAM,CAAC,CACnD,CAKA,WAAY,CACI,SAAS,iBAAiB,mBAAmB,EACrD,QAAQC,GAAQ,IAAIlC,EAAUkC,CAAI,CAAC,CAC3C,CAKA,YAAa,CACI,SAAS,iBAAiB,oBAAoB,EACtD,QAAQC,GAAS,IAAI5B,EAAW4B,CAAK,CAAC,CAC/C,CAKA,gBAAiB,CACF,SAAS,iBAAiB,kBAAkB,EACpD,QAAQC,GAAO,IAAI5B,EAAgB4B,CAAG,CAAC,CAC9C,CAKA,SAASxB,EAAO,CACd,KAAK,aAAa,SAASA,CAAK,CAClC,CAKA,SAAU,CACJ,KAAK,eAAe,KAAK,cAAc,QAAO,EAC9C,KAAK,eAAe,KAAK,cAAc,QAAO,EAClD,KAAK,aAAa,QAAO,EAEzB,SAAS,gBAAgB,gBAAgB,sBAAsB,CACjE,CACF,CAmBI,OAAO,OAAW,KAAe,CAAC,OAAO,gBAC3C,OAAO,cAAgBoB,EAGnB,SAAS,aAAe,UAC1B,SAAS,iBAAiB,mBAAoB,IAAM,CAC7C,SAAS,cAAc,qBAAqB,GAC/C,IAAIA,CAER,CAAC,EAEI,SAAS,cAAc,qBAAqB,GAC/C,IAAIA"}