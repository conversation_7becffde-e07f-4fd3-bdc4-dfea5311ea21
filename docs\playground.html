<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass UI - Interactive Playground</title>
    <link rel="stylesheet" href="../src/css/index.css">
    <link rel="stylesheet" href="./docs.css">
    <link rel="stylesheet" href="./playground.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 min-h-screen">
    <!-- Navigation -->
    <nav class="glass-nav fixed top-0 left-0 right-0 z-50 glass backdrop-blur-xl bg-white/10 dark:bg-gray-900/10 border-b border-white/20 dark:border-gray-700/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="glass-logo flex items-center space-x-2">
                        <div class="w-8 h-8 rounded-lg glass bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">LG</span>
                        </div>
                        <span class="font-bold text-xl text-gray-900 dark:text-white">Liquid Glass UI</span>
                    </a>
                    <span class="text-gray-500 dark:text-gray-400">/</span>
                    <span class="text-gray-700 dark:text-gray-300">Playground</span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button data-theme-toggle class="glass-btn glass-btn-ghost p-2">
                        <svg class="w-5 h-5 theme-icon-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <svg class="w-5 h-5 theme-icon-dark hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    
                    <a href="index.html" class="glass-btn glass-btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Docs
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-24 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                    <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                        Interactive Playground
                    </span>
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Experiment with different glass effects, customize components, and see the results in real-time.
                </p>
            </div>

            <!-- Playground Layout -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Controls Panel -->
                <div class="lg:col-span-1">
                    <div class="glass-card p-6 sticky top-24">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Customize</h3>
                        
                        <!-- Component Type -->
                        <div class="control-group mb-6">
                            <label for="component-type">Component Type</label>
                            <select id="component-type" class="w-full">
                                <option value="button">Button</option>
                                <option value="card">Card</option>
                                <option value="glass-effect">Glass Effect</option>
                            </select>
                        </div>

                        <!-- Button Controls -->
                        <div id="button-controls" class="component-controls">
                            <div class="control-group mb-4">
                                <label for="button-variant">Variant</label>
                                <select id="button-variant" class="w-full">
                                    <option value="primary">Primary</option>
                                    <option value="secondary">Secondary</option>
                                    <option value="success">Success</option>
                                    <option value="warning">Warning</option>
                                    <option value="danger">Danger</option>
                                    <option value="ghost">Ghost</option>
                                </select>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="button-size">Size</label>
                                <select id="button-size" class="w-full">
                                    <option value="xs">Extra Small</option>
                                    <option value="sm">Small</option>
                                    <option value="">Default</option>
                                    <option value="lg">Large</option>
                                    <option value="xl">Extra Large</option>
                                </select>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="button-shape">Shape</label>
                                <select id="button-shape" class="w-full">
                                    <option value="">Default</option>
                                    <option value="circle">Circle</option>
                                    <option value="pill">Pill</option>
                                    <option value="square">Square</option>
                                </select>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="button-text">Button Text</label>
                                <input type="text" id="button-text" value="Click Me" class="w-full">
                            </div>
                        </div>

                        <!-- Card Controls -->
                        <div id="card-controls" class="component-controls hidden">
                            <div class="control-group mb-4">
                                <label for="card-variant">Variant</label>
                                <select id="card-variant" class="w-full">
                                    <option value="">Default</option>
                                    <option value="subtle">Subtle</option>
                                    <option value="elevated">Elevated</option>
                                    <option value="outlined">Outlined</option>
                                    <option value="filled">Filled</option>
                                </select>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="card-title">Card Title</label>
                                <input type="text" id="card-title" value="Card Title" class="w-full">
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="card-content">Card Content</label>
                                <textarea id="card-content" rows="3" class="w-full">This is a beautiful glass card with customizable content.</textarea>
                            </div>
                        </div>

                        <!-- Glass Effect Controls -->
                        <div id="glass-controls" class="component-controls hidden">
                            <div class="control-group mb-4">
                                <label for="glass-intensity">Glass Intensity</label>
                                <select id="glass-intensity" class="w-full">
                                    <option value="subtle">Subtle</option>
                                    <option value="light">Light</option>
                                    <option value="">Default</option>
                                    <option value="medium">Medium</option>
                                    <option value="strong">Strong</option>
                                    <option value="intense">Intense</option>
                                </select>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="blur-amount">Blur Amount</label>
                                <input type="range" id="blur-amount" min="0" max="20" value="8" class="w-full">
                                <span class="text-sm text-gray-500" id="blur-value">8px</span>
                            </div>
                            
                            <div class="control-group mb-4">
                                <label for="opacity-amount">Opacity</label>
                                <input type="range" id="opacity-amount" min="0" max="100" value="15" class="w-full">
                                <span class="text-sm text-gray-500" id="opacity-value">15%</span>
                            </div>
                        </div>

                        <!-- Generate Code Button -->
                        <button id="generate-code" class="glass-btn glass-btn-primary w-full mt-6">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                            </svg>
                            Generate Code
                        </button>
                    </div>
                </div>

                <!-- Preview Panel -->
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <!-- Live Preview -->
                        <div class="glass-card p-8">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Live Preview</h3>
                            <div id="preview-container" class="min-h-[200px] flex items-center justify-center bg-gradient-to-br from-blue-100/50 to-purple-100/50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-white/20">
                                <!-- Preview content will be inserted here -->
                                <button class="glass-btn glass-btn-primary" data-glass-button>Click Me</button>
                            </div>
                        </div>

                        <!-- Generated Code -->
                        <div class="glass-card p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Generated Code</h3>
                                <button id="copy-generated-code" class="glass-btn glass-btn-ghost glass-btn-sm">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    Copy
                                </button>
                            </div>
                            <div class="code-block">
                                <pre><code id="generated-code" class="language-html">&lt;button class="glass-btn glass-btn-primary" data-glass-button&gt;Click Me&lt;/button&gt;</code></pre>
                            </div>
                        </div>

                        <!-- CSS Variables -->
                        <div class="glass-card p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">CSS Variables</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4">
                                You can customize these CSS variables to fine-tune the glass effects:
                            </p>
                            <div class="code-block">
                                <pre><code id="css-variables" class="language-css">:root {
  --glass-blur-md: 8px;
  --glass-opacity-medium: 0.15;
  --glass-border-opacity: 0.2;
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="../src/index.js"></script>
    <script src="./docs.js"></script>
    <script src="./playground.js"></script>
</body>
</html>
