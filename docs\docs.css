/* Documentation Styles */

/* Base Styles */
* {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.glass-nav {
    transition: all 0.3s ease;
}

.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.nav-link.active {
    background: rgba(59, 130, 246, 0.2);
    color: rgb(59, 130, 246);
}

/* Theme Toggle */
.theme-icon-light,
.theme-icon-dark {
    transition: all 0.2s ease;
}

[data-theme="dark"] .theme-icon-light {
    display: none;
}

[data-theme="dark"] .theme-icon-dark {
    display: block !important;
}

[data-theme="light"] .theme-icon-dark {
    display: none;
}

[data-theme="light"] .theme-icon-light {
    display: block;
}

/* Code Blocks */
.code-block {
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.75rem;
    overflow: hidden;
    margin: 1rem 0;
}

.code-block pre {
    margin: 0;
    padding: 1.5rem;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block code {
    color: #e2e8f0;
    background: none;
    padding: 0;
    border-radius: 0;
    font-size: inherit;
}

.copy-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.copy-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.copy-btn.copied {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
}

/* Component Tabs */
.component-tab {
    transition: all 0.2s ease;
}

.component-tab.active {
    background: rgba(59, 130, 246, 0.2);
    color: rgb(59, 130, 246);
    border-color: rgba(59, 130, 246, 0.3);
}

/* Demo Sections */
.demo-section {
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.demo-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.demo-item {
    padding: 1.5rem;
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.demo-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
}

.demo-item h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.demo-item p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.demo-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    margin: 1rem 0;
    min-height: 100px;
}

/* Color Palette */
.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.color-item {
    text-align: center;
}

.color-swatch {
    width: 100%;
    height: 80px;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.color-swatch::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    backdrop-filter: blur(10px);
}

.color-name {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
}

.color-value {
    font-size: 0.625rem;
    color: var(--text-secondary);
    font-family: monospace;
}

/* Glass Effect Demos */
.glass-demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.glass-demo-item {
    height: 150px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
}

.glass-demo-item::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.3) 0%, 
        rgba(147, 51, 234, 0.3) 50%, 
        rgba(236, 72, 153, 0.3) 100%);
    z-index: -1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .demo-grid {
        grid-template-columns: 1fr;
    }
    
    .glass-demo-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .demo-preview {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
    }
}

[data-theme="light"] {
    --text-primary: #1e293b;
    --text-secondary: #64748b;
}

[data-theme="dark"] {
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
}

/* Interactive Elements */
.interactive-demo {
    padding: 2rem;
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.control-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.control-group input,
.control-group select {
    padding: 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    backdrop-filter: blur(10px);
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
