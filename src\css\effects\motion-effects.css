/* Liquid Glass UI - Motion Effects */

/* Motion Effect Base Styles */
.has-motion-effect {
  position: relative;
  transform-style: preserve-3d;
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

/* Motion Tilt Effects */
.motion-tilt {
  transform: perspective(1000px) 
             rotateX(var(--motion-tilt-x, 0deg)) 
             rotateY(var(--motion-tilt-y, 0deg));
}

.motion-tilt-subtle {
  transform: perspective(1000px) 
             rotateX(calc(var(--motion-tilt-x, 0deg) * 0.5)) 
             rotateY(calc(var(--motion-tilt-y, 0deg) * 0.5));
}

.motion-tilt-strong {
  transform: perspective(800px) 
             rotateX(calc(var(--motion-tilt-x, 0deg) * 1.5)) 
             rotateY(calc(var(--motion-tilt-y, 0deg) * 1.5));
}

/* Motion Parallax Effects */
.motion-parallax > * {
  transform: translate(
    var(--motion-parallax-x, 0px),
    var(--motion-parallax-y, 0px)
  );
  transition: transform var(--glass-duration-normal) var(--glass-ease-out);
}

.motion-parallax-layers > *:nth-child(1) {
  transform: translate(
    calc(var(--motion-parallax-x, 0px) * 0.2),
    calc(var(--motion-parallax-y, 0px) * 0.2)
  );
}

.motion-parallax-layers > *:nth-child(2) {
  transform: translate(
    calc(var(--motion-parallax-x, 0px) * 0.5),
    calc(var(--motion-parallax-y, 0px) * 0.5)
  );
}

.motion-parallax-layers > *:nth-child(3) {
  transform: translate(
    calc(var(--motion-parallax-x, 0px) * 0.8),
    calc(var(--motion-parallax-y, 0px) * 0.8)
  );
}

.motion-parallax-layers > *:nth-child(4) {
  transform: translate(
    var(--motion-parallax-x, 0px),
    var(--motion-parallax-y, 0px)
  );
}

/* Motion Lighting Effects */
.motion-lighting::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--motion-light, transparent);
  border-radius: inherit;
  pointer-events: none;
  opacity: var(--motion-intensity, 0);
  transition: opacity var(--glass-duration-normal) var(--glass-ease-out);
  z-index: 1;
}

.motion-lighting > * {
  position: relative;
  z-index: 2;
}

/* Motion Depth Effects */
.motion-depth {
  position: relative;
}

.motion-depth::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  filter: blur(8px);
  opacity: calc(var(--motion-intensity, 0) * 0.3);
  transform: translateZ(-10px) 
             translate(
               calc(var(--motion-x, 0) * 5px),
               calc(var(--motion-y, 0) * 5px)
             );
  transition: opacity var(--glass-duration-normal) var(--glass-ease-out),
              transform var(--glass-duration-normal) var(--glass-ease-out);
  z-index: -1;
}

/* Motion Float Effects */
.motion-float {
  transform: translateZ(0) 
             translate(
               calc(var(--motion-x, 0) * 10px),
               calc(var(--motion-y, 0) * 10px)
             );
}

/* Motion Gyro Effects */
.motion-gyro {
  transform: perspective(1000px) 
             rotateX(calc(var(--motion-y, 0) * 15deg)) 
             rotateY(calc(var(--motion-x, 0) * 15deg))
             translateZ(calc(var(--motion-intensity, 0) * 20px));
}

/* Motion Shake Effects */
@keyframes motion-shake-1 {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes motion-shake-2 {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(-3px, -1px); }
  50% { transform: translate(3px, 1px); }
  75% { transform: translate(-1px, 3px); }
}

@keyframes motion-shake-3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-4px, -2px) rotate(-1deg); }
  50% { transform: translate(4px, 2px) rotate(1deg); }
  75% { transform: translate(-2px, 4px) rotate(-0.5deg); }
}

.motion-shake-1 {
  animation: motion-shake-1 0.5s ease-in-out;
}

.motion-shake-2 {
  animation: motion-shake-2 0.5s ease-in-out;
}

.motion-shake-3 {
  animation: motion-shake-3 0.5s ease-in-out;
}

/* Motion Ripple Effects */
.motion-ripple {
  position: relative;
  overflow: hidden;
}

.motion-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--glass-duration-slow) var(--glass-ease-out),
              height var(--glass-duration-slow) var(--glass-ease-out);
  pointer-events: none;
  z-index: 1;
}

.motion-ripple.active::after {
  width: 200px;
  height: 200px;
}

/* Motion Blur Effects */
.motion-blur {
  filter: blur(calc(var(--motion-intensity, 0) * 2px));
  transition: filter var(--glass-duration-normal) var(--glass-ease-out);
}

/* Motion Scale Effects */
.motion-scale {
  transform: scale(calc(1 + var(--motion-intensity, 0) * 0.1));
}

/* Motion Gradient Effects */
.motion-gradient {
  background: linear-gradient(
    calc(var(--motion-x, 0) * 180deg + 45deg),
    var(--glass-white-800) 0%,
    var(--glass-white-700) 50%,
    var(--glass-white-800) 100%
  );
  transition: background var(--glass-duration-normal) var(--glass-ease-out);
}

[data-theme="dark"] .motion-gradient {
  background: linear-gradient(
    calc(var(--motion-x, 0) * 180deg + 45deg),
    var(--glass-black-800) 0%,
    var(--glass-black-700) 50%,
    var(--glass-black-800) 100%
  );
}

/* Motion Perspective Effects */
.motion-perspective {
  perspective: 1000px;
  perspective-origin: calc(50% + var(--motion-x, 0) * 50%) calc(50% + var(--motion-y, 0) * 50%);
  transition: perspective-origin var(--glass-duration-normal) var(--glass-ease-out);
}

/* Motion Compound Effects */
.motion-compound {
  transform: perspective(1000px) 
             rotateX(calc(var(--motion-y, 0) * 10deg)) 
             rotateY(calc(var(--motion-x, 0) * 10deg))
             translateZ(calc(var(--motion-intensity, 0) * 10px))
             scale(calc(1 + var(--motion-intensity, 0) * 0.05));
  filter: blur(calc(var(--motion-intensity, 0) * 1px));
}

/* Motion Layers */
.motion-layer-1 {
  transform: translate(
    calc(var(--motion-x, 0) * 5px),
    calc(var(--motion-y, 0) * 5px)
  );
}

.motion-layer-2 {
  transform: translate(
    calc(var(--motion-x, 0) * 10px),
    calc(var(--motion-y, 0) * 10px)
  );
}

.motion-layer-3 {
  transform: translate(
    calc(var(--motion-x, 0) * 15px),
    calc(var(--motion-y, 0) * 15px)
  );
}

/* Motion Disabled States */
.motion-disabled {
  transform: none !important;
  filter: none !important;
  transition: none !important;
}

/* Responsive Motion Effects */
@media (max-width: 768px) {
  .motion-mobile-reduced .has-motion-effect {
    transform: translate(
      calc(var(--motion-x, 0) * 5px),
      calc(var(--motion-y, 0) * 5px)
    ) !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .has-motion-effect,
  .motion-tilt,
  .motion-tilt-subtle,
  .motion-tilt-strong,
  .motion-parallax > *,
  .motion-float,
  .motion-gyro,
  .motion-blur,
  .motion-scale,
  .motion-gradient,
  .motion-compound {
    transform: none !important;
    filter: none !important;
    transition: none !important;
    animation: none !important;
  }

  .motion-lighting::before,
  .motion-depth::after,
  .motion-ripple::after {
    display: none !important;
  }
}
