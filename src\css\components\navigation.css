/* Apple Liquid Glass UI - Navigation Components */

/* Mobile-First Navigation Bar */
.glass-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--glass-z-modal);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border-bottom: var(--glass-border-ultra-thin);
  box-shadow: var(--glass-shadow-regular);
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
}

.glass-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-navbar-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--glass-space-medium) var(--glass-space-large);
  max-width: 1200px;
  margin: 0 auto;
}

.glass-navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--glass-space-medium);
  font-weight: 600;
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.9);
  text-decoration: none;
  letter-spacing: -0.02em;
}

.glass-navbar-logo {
  width: 32px;
  height: 32px;
  border-radius: var(--glass-radius-medium);
  background: linear-gradient(135deg, #007AFF, #5856D6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 0.875rem;
}

/* Mobile Menu Toggle */
.glass-navbar-toggle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--glass-space-small);
  border-radius: var(--glass-radius-medium);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-navbar-toggle:hover {
  background: var(--glass-material-thin);
  transform: scale(1.05);
}

.glass-navbar-toggle span {
  width: 20px;
  height: 2px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 1px;
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-navbar-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.glass-navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.glass-navbar-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Desktop Navigation Menu */
.glass-navbar-menu {
  display: none;
  align-items: center;
  gap: var(--glass-space-large);
}

.glass-navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--glass-space-large);
  list-style: none;
  margin: 0;
  padding: 0;
}

.glass-navbar-link {
  color: rgba(0, 0, 0, 0.8);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--glass-space-small) var(--glass-space-medium);
  border-radius: var(--glass-radius-medium);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  letter-spacing: -0.01em;
}

.glass-navbar-link:hover {
  color: rgba(0, 0, 0, 0.9);
  background: var(--glass-material-thin);
  transform: translateY(-1px);
}

.glass-navbar-link.active {
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

/* Mobile Menu Overlay */
.glass-navbar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-material-heavy);
  backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(180%);
  z-index: var(--glass-z-overlay);
  opacity: 0;
  visibility: hidden;
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
}

.glass-navbar-overlay.active {
  opacity: 1;
  visibility: visible;
}

.glass-navbar-mobile-menu {
  position: fixed;
  top: 80px;
  left: var(--glass-space-large);
  right: var(--glass-space-large);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-extra-large);
  box-shadow: var(--glass-shadow-heavy);
  z-index: var(--glass-z-popover);
  transform: translateY(-20px) scale(0.95);
  opacity: 0;
  visibility: hidden;
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
  overflow: hidden;
}

.glass-navbar-mobile-menu::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-navbar-mobile-menu.active {
  transform: translateY(0) scale(1);
  opacity: 1;
  visibility: visible;
}

.glass-navbar-mobile-nav {
  position: relative;
  z-index: 2;
  padding: var(--glass-space-large);
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-medium);
}

.glass-navbar-mobile-link {
  color: rgba(0, 0, 0, 0.8);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: var(--glass-space-medium);
  border-radius: var(--glass-radius-large);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  letter-spacing: -0.01em;
}

.glass-navbar-mobile-link:hover {
  color: rgba(0, 0, 0, 0.9);
  background: var(--glass-material-thin);
  transform: translateX(4px);
}

.glass-navbar-mobile-link.active {
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

/* Responsive Design */
@media (min-width: 768px) {
  .glass-navbar-toggle {
    display: none;
  }
  
  .glass-navbar-menu {
    display: flex;
  }
}

/* Tab Bar (Mobile Bottom Navigation) */
.glass-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--glass-z-modal);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border-top: var(--glass-border-ultra-thin);
  box-shadow: var(--glass-shadow-regular);
  padding: var(--glass-space-small) var(--glass-space-medium) calc(var(--glass-space-small) + env(safe-area-inset-bottom));
}

.glass-tabbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-tabbar-nav {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
}

.glass-tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: var(--glass-space-small);
  border-radius: var(--glass-radius-medium);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
  text-decoration: none;
  color: rgba(0, 0, 0, 0.6);
  min-width: 60px;
}

.glass-tabbar-item:hover {
  background: var(--glass-material-thin);
  transform: translateY(-2px);
}

.glass-tabbar-item.active {
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

.glass-tabbar-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.glass-tabbar-label {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Hide tabbar on desktop */
@media (min-width: 768px) {
  .glass-tabbar {
    display: none;
  }
}

/* Breadcrumb Navigation */
.glass-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--glass-space-small);
  padding: var(--glass-space-medium);
  background: var(--glass-material-thin);
  border-radius: var(--glass-radius-large);
  font-size: 0.875rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.glass-breadcrumb::-webkit-scrollbar {
  display: none;
}

.glass-breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--glass-space-small);
  white-space: nowrap;
}

.glass-breadcrumb-link {
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
  padding: var(--glass-space-small);
  border-radius: var(--glass-radius-small);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-breadcrumb-link:hover {
  color: rgba(0, 0, 0, 0.9);
  background: var(--glass-material-thin);
}

.glass-breadcrumb-separator {
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.75rem;
}

.glass-breadcrumb-current {
  color: rgba(0, 0, 0, 0.9);
  font-weight: 500;
}
