(function(o,d){typeof exports=="object"&&typeof module<"u"?d(exports):typeof define=="function"&&define.amd?define(["exports"],d):(o=typeof globalThis<"u"?globalThis:o||self,d(o.LiquidGlassUI={}))})(this,function(o){"use strict";class d{constructor(e={}){this.options={enableAnimations:!0,enableInteractions:!0,performanceMode:"auto",...e},this.elements=new Map,this.observers=new Map,this.isInitialized=!1}init(){this.isInitialized||(this.detectPerformanceCapabilities(),this.setupIntersectionObserver(),this.bindGlobalEvents(),this.scanForElements(),this.isInitialized=!0)}detectPerformanceCapabilities(){if(this.options.performanceMode!=="auto")return;const e=navigator.deviceMemory||4,t=navigator.hardwareConcurrency||4,s=navigator.connection;let i=0;e>=8?i+=3:e>=4?i+=2:e>=2&&(i+=1),t>=8?i+=3:t>=4?i+=2:t>=2&&(i+=1),s?s.effectiveType==="4g"?i+=2:s.effectiveType==="3g"&&(i+=1):i+=2,i>=6?this.options.performanceMode="high":i>=4?this.options.performanceMode="medium":this.options.performanceMode="low",document.documentElement.classList.add(`performance-${this.options.performanceMode}`)}setupIntersectionObserver(){"IntersectionObserver"in window&&(this.intersectionObserver=new IntersectionObserver(e=>{e.forEach(t=>{const s=t.target,i=this.elements.get(s);i&&(t.isIntersecting?this.activateElement(s,i):this.deactivateElement(s,i))})},{rootMargin:"50px",threshold:.1}))}bindGlobalEvents(){document.addEventListener("visibilitychange",()=>{document.hidden?this.pauseAllEffects():this.resumeAllEffects()});let e;window.addEventListener("resize",()=>{clearTimeout(e),e=setTimeout(()=>{this.updateAllElements()},100)})}scanForElements(){["[data-glass-effect]",".glass",".glass-subtle",".glass-light",".glass-medium",".glass-strong",".glass-intense"].forEach(t=>{document.querySelectorAll(t).forEach(s=>{this.addElement(s)})})}addElement(e,t={}){if(this.elements.has(e))return;const s={element:e,options:{...this.getDefaultOptions(),...t},isActive:!1,animations:new Set,observers:new Set};this.elements.set(e,s),this.intersectionObserver?this.intersectionObserver.observe(e):this.activateElement(e,s),this.initializeElement(e,s)}getDefaultOptions(){const e={enableBlur:!0,enableShadows:!0,enableAnimations:this.options.enableAnimations,enableInteractions:this.options.enableInteractions};switch(this.options.performanceMode){case"low":return{...e,enableBlur:!1,enableAnimations:!1,maxBlur:4};case"medium":return{...e,maxBlur:8};case"high":return{...e,maxBlur:16};default:return e}}initializeElement(e,t){const{options:s}=t;e.classList.add("glass-element"),e.classList.add(`glass-performance-${this.options.performanceMode}`),this.updateElementProperties(e,t),s.enableInteractions&&this.addInteractionListeners(e,t)}updateElementProperties(e,t){const{options:s}=t,i=e.getBoundingClientRect();e.style.setProperty("--element-width",`${i.width}px`),e.style.setProperty("--element-height",`${i.height}px`),e.style.setProperty("--max-blur",`${s.maxBlur||8}px`),e.style.setProperty("--enable-animations",s.enableAnimations?"1":"0")}addInteractionListeners(e,t){Object.entries({mouseenter:()=>this.handleElementHover(e,t,!0),mouseleave:()=>this.handleElementHover(e,t,!1),focus:()=>this.handleElementFocus(e,t,!0),blur:()=>this.handleElementFocus(e,t,!1),click:i=>this.handleElementClick(e,t,i)}).forEach(([i,n])=>{e.addEventListener(i,n),t.observers.add(()=>{e.removeEventListener(i,n)})})}handleElementHover(e,t,s){t.options.enableInteractions&&(e.classList.toggle("glass-hover",s),s?this.addAnimation(e,"hover-in"):this.addAnimation(e,"hover-out"))}handleElementFocus(e,t,s){t.options.enableInteractions&&e.classList.toggle("glass-focus",s)}handleElementClick(e,t,s){t.options.enableInteractions&&(this.createRippleEffect(e,s),this.addAnimation(e,"click"))}createRippleEffect(e,t){const s=e.getBoundingClientRect(),i=t.clientX-s.left,n=t.clientY-s.top,a=document.createElement("div");a.className="glass-ripple",a.style.cssText=`
      position: absolute;
      left: ${i}px;
      top: ${n}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: glass-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `,e.style.position="relative",e.appendChild(a),setTimeout(()=>{a.parentNode&&a.parentNode.removeChild(a)},600)}addAnimation(e,t){const s=this.elements.get(e);!s||!s.options.enableAnimations||(s.animations.add(t),e.classList.add(`glass-animate-${t}`),setTimeout(()=>{s.animations.delete(t),e.classList.remove(`glass-animate-${t}`)},300))}activateElement(e,t){t.isActive||(t.isActive=!0,e.classList.add("glass-active"))}deactivateElement(e,t){t.isActive&&(t.isActive=!1,e.classList.remove("glass-active"))}removeElement(e){const t=this.elements.get(e);t&&(this.intersectionObserver&&this.intersectionObserver.unobserve(e),t.observers.forEach(s=>s()),e.classList.remove("glass-element","glass-active","glass-hover","glass-focus"),this.elements.delete(e))}updateAllElements(){this.elements.forEach((e,t)=>{this.updateElementProperties(t,e)})}pauseAllEffects(){this.elements.forEach((e,t)=>{t.classList.add("glass-paused")})}resumeAllEffects(){this.elements.forEach((e,t)=>{t.classList.remove("glass-paused")})}destroy(){this.elements.forEach((e,t)=>{this.removeElement(t)}),this.intersectionObserver&&this.intersectionObserver.disconnect(),this.elements.clear(),this.observers.clear(),this.isInitialized=!1}}class p{constructor(e={}){this.options={enableShadows:!0,enableLighting:!0,shadowIntensity:.3,lightingIntensity:.2,maxDistance:200,smoothing:.1,...e},this.cursor={x:0,y:0},this.smoothCursor={x:0,y:0},this.elements=new Set,this.isActive=!1,this.animationFrame=null}init(){this.isActive||(this.isActive=!0,this.bindEvents(),this.findElements(),this.startAnimation())}bindEvents(){this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseleave",this.handleMouseLeave)}findElements(){["[data-cursor-effect]",".glass-cursor-effect",".glass",".glass-button",".glass-card"].forEach(t=>{document.querySelectorAll(t).forEach(s=>{this.addElement(s)})})}addElement(e){this.elements.has(e)||(this.elements.add(e),e.style.setProperty("--cursor-x","0"),e.style.setProperty("--cursor-y","0"),e.style.setProperty("--cursor-distance","1"),e.style.setProperty("--cursor-intensity","0"),e.classList.add("has-cursor-effect"))}removeElement(e){this.elements.has(e)&&(this.elements.delete(e),e.classList.remove("has-cursor-effect"),e.style.removeProperty("--cursor-x"),e.style.removeProperty("--cursor-y"),e.style.removeProperty("--cursor-distance"),e.style.removeProperty("--cursor-intensity"))}handleMouseMove(e){this.cursor.x=e.clientX,this.cursor.y=e.clientY}handleMouseLeave(){this.cursor.x=-1e3,this.cursor.y=-1e3}startAnimation(){const e=()=>{this.isActive&&(this.updateCursor(),this.updateElements(),this.animationFrame=requestAnimationFrame(e))};e()}updateCursor(){this.smoothCursor.x+=(this.cursor.x-this.smoothCursor.x)*this.options.smoothing,this.smoothCursor.y+=(this.cursor.y-this.smoothCursor.y)*this.options.smoothing}updateElements(){this.elements.forEach(e=>{this.updateElement(e)})}updateElement(e){const t=e.getBoundingClientRect(),s=t.left+t.width/2,i=t.top+t.height/2,n=this.smoothCursor.x-s,a=this.smoothCursor.y-i,l=Math.sqrt(n*n+a*a),h=Math.min(l/this.options.maxDistance,1),c=1-h,m=(this.smoothCursor.x-t.left)/t.width,w=(this.smoothCursor.y-t.top)/t.height;e.style.setProperty("--cursor-x",m.toFixed(3)),e.style.setProperty("--cursor-y",w.toFixed(3)),e.style.setProperty("--cursor-distance",h.toFixed(3)),e.style.setProperty("--cursor-intensity",c.toFixed(3)),this.options.enableShadows&&this.applyShadowEffect(e,n,a,c),this.options.enableLighting&&this.applyLightingEffect(e,m,w,c)}applyShadowEffect(e,t,s,i){const n=i*this.options.shadowIntensity,a=-t*.1*n,l=-s*.1*n,h=20*n,c=.3*n,m=`${a}px ${l}px ${h}px rgba(0, 0, 0, ${c})`;e.style.setProperty("--cursor-shadow",m)}applyLightingEffect(e,t,s,i){const n=i*this.options.lightingIntensity,a=t*100,l=s*100,c=`radial-gradient(${150*i}px circle at ${a}% ${l}%, rgba(255, 255, 255, ${n}) 0%, transparent 70%)`;e.style.setProperty("--cursor-light",c)}createRipple(e,t){const s=e.getBoundingClientRect(),i=t.clientX-s.left,n=t.clientY-s.top,a=document.createElement("div");a.className="cursor-ripple",a.style.cssText=`
      position: absolute;
      left: ${i}px;
      top: ${n}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      animation: cursor-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `,e.style.position="relative",e.appendChild(a),setTimeout(()=>{a.parentNode&&a.parentNode.removeChild(a)},600)}addRippleEffect(e){const t=s=>{this.createRipple(e,s)};return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)}}updateOptions(e){this.options={...this.options,...e}}pause(){this.isActive=!1,this.animationFrame&&(cancelAnimationFrame(this.animationFrame),this.animationFrame=null)}resume(){this.isActive||(this.isActive=!0,this.startAnimation())}destroy(){this.pause(),document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseleave",this.handleMouseLeave),this.elements.forEach(e=>{this.removeElement(e)}),this.elements.clear()}getCursorPosition(){return{...this.smoothCursor}}isEnabled(){return this.isActive}}class f{constructor(e={}){this.options={enableTilt:!0,enableParallax:!0,enableLighting:!0,sensitivity:1,maxTilt:15,smoothing:.1,...e},this.orientation={alpha:0,beta:0,gamma:0},this.smoothOrientation={alpha:0,beta:0,gamma:0},this.elements=new Set,this.isActive=!1,this.animationFrame=null,this.permissionGranted=!1}async init(){if(!this.isActive){if(!this.isSupported())return console.warn("Device orientation not supported"),!1;if(typeof DeviceOrientationEvent.requestPermission=="function")try{const e=await DeviceOrientationEvent.requestPermission();this.permissionGranted=e==="granted"}catch(e){return console.warn("Device orientation permission denied:",e),!1}else this.permissionGranted=!0;return this.permissionGranted?(this.isActive=!0,this.bindEvents(),this.findElements(),this.startAnimation(),!0):!1}}isSupported(){return"DeviceOrientationEvent"in window}bindEvents(){this.handleDeviceOrientation=this.handleDeviceOrientation.bind(this),window.addEventListener("deviceorientation",this.handleDeviceOrientation)}findElements(){["[data-motion-effect]",".glass-motion-effect",".motion-tilt",".motion-parallax"].forEach(t=>{document.querySelectorAll(t).forEach(s=>{this.addElement(s)})})}addElement(e){this.elements.has(e)||(this.elements.add(e),e.style.setProperty("--motion-x","0"),e.style.setProperty("--motion-y","0"),e.style.setProperty("--motion-z","0"),e.style.setProperty("--motion-intensity","0"),e.classList.add("has-motion-effect"))}removeElement(e){this.elements.has(e)&&(this.elements.delete(e),e.classList.remove("has-motion-effect"),e.style.removeProperty("--motion-x"),e.style.removeProperty("--motion-y"),e.style.removeProperty("--motion-z"),e.style.removeProperty("--motion-intensity"))}handleDeviceOrientation(e){this.orientation.alpha=e.alpha||0,this.orientation.beta=e.beta||0,this.orientation.gamma=e.gamma||0}startAnimation(){const e=()=>{this.isActive&&(this.updateOrientation(),this.updateElements(),this.animationFrame=requestAnimationFrame(e))};e()}updateOrientation(){this.smoothOrientation.alpha+=(this.orientation.alpha-this.smoothOrientation.alpha)*this.options.smoothing,this.smoothOrientation.beta+=(this.orientation.beta-this.smoothOrientation.beta)*this.options.smoothing,this.smoothOrientation.gamma+=(this.orientation.gamma-this.smoothOrientation.gamma)*this.options.smoothing}updateElements(){this.elements.forEach(e=>{this.updateElement(e)})}updateElement(e){const{beta:t,gamma:s}=this.smoothOrientation,i=this.clamp(s/90,-1,1)*this.options.sensitivity,n=this.clamp(t/180,-1,1)*this.options.sensitivity,a=Math.sqrt(i*i+n*n);e.style.setProperty("--motion-x",i.toFixed(3)),e.style.setProperty("--motion-y",n.toFixed(3)),e.style.setProperty("--motion-intensity",Math.min(a,1).toFixed(3)),this.options.enableTilt&&this.applyTiltEffect(e,i,n),this.options.enableParallax&&this.applyParallaxEffect(e,i,n),this.options.enableLighting&&this.applyLightingEffect(e,i,n,a)}applyTiltEffect(e,t,s){const i=s*this.options.maxTilt,n=-t*this.options.maxTilt;e.style.setProperty("--motion-tilt-x",`${i}deg`),e.style.setProperty("--motion-tilt-y",`${n}deg`)}applyParallaxEffect(e,t,s){const i=t*20,n=s*20;e.style.setProperty("--motion-parallax-x",`${i}px`),e.style.setProperty("--motion-parallax-y",`${n}px`)}applyLightingEffect(e,t,s,i){const n=(t+1)*50,a=(s+1)*50,l=i*.3,h=`radial-gradient(circle at ${n}% ${a}%, rgba(255, 255, 255, ${l}) 0%, transparent 70%)`;e.style.setProperty("--motion-light",h)}createShake(e,t=1){const s=`motion-shake-${Math.floor(t*3)+1}`;e.classList.add(s),setTimeout(()=>{e.classList.remove(s)},500)}clamp(e,t,s){return Math.min(Math.max(e,t),s)}async requestPermission(){if(typeof DeviceOrientationEvent.requestPermission=="function")try{const e=await DeviceOrientationEvent.requestPermission();return this.permissionGranted=e==="granted",this.permissionGranted}catch(e){return console.error("Error requesting device orientation permission:",e),!1}return this.permissionGranted=!0,!0}calibrate(){this.orientation={alpha:0,beta:0,gamma:0},this.smoothOrientation={alpha:0,beta:0,gamma:0}}updateOptions(e){this.options={...this.options,...e}}pause(){this.isActive=!1,this.animationFrame&&(cancelAnimationFrame(this.animationFrame),this.animationFrame=null)}resume(){!this.isActive&&this.permissionGranted&&(this.isActive=!0,this.startAnimation())}destroy(){this.pause(),window.removeEventListener("deviceorientation",this.handleDeviceOrientation),this.elements.forEach(e=>{this.removeElement(e)}),this.elements.clear()}getOrientation(){return{...this.smoothOrientation}}isEnabled(){return this.isActive&&this.permissionGranted}}class g{constructor(e,t={}){this.element=e,this.options={enableRipple:!0,enableHover:!0,enableFocus:!0,enableLoading:!0,rippleColor:"rgba(255, 255, 255, 0.3)",...t},this.isLoading=!1,this.ripples=new Set,this.init()}init(){this.setupElement(),this.bindEvents(),this.setupAccessibility()}setupElement(){this.element.classList.contains("glass-btn")||this.element.classList.add("glass-btn"),this.element.setAttribute("data-glass-button","initialized"),this.updateState()}bindEvents(){this.element.addEventListener("click",this.handleClick.bind(this)),this.options.enableHover&&(this.element.addEventListener("mouseenter",this.handleMouseEnter.bind(this)),this.element.addEventListener("mouseleave",this.handleMouseLeave.bind(this))),this.options.enableFocus&&(this.element.addEventListener("focus",this.handleFocus.bind(this)),this.element.addEventListener("blur",this.handleBlur.bind(this))),this.element.addEventListener("keydown",this.handleKeyDown.bind(this))}setupAccessibility(){!this.element.getAttribute("role")&&this.element.tagName!=="BUTTON"&&this.element.setAttribute("role","button"),!this.element.hasAttribute("tabindex")&&this.element.tagName!=="BUTTON"&&this.element.setAttribute("tabindex","0"),this.options.enableLoading&&this.element.setAttribute("aria-busy","false")}handleClick(e){if(this.isLoading||this.element.disabled){e.preventDefault();return}this.options.enableRipple&&this.createRipple(e),this.dispatchEvent("glass-button-click",{originalEvent:e})}handleMouseEnter(e){this.element.classList.add("glass-btn-hover"),this.dispatchEvent("glass-button-hover",{state:"enter",originalEvent:e})}handleMouseLeave(e){this.element.classList.remove("glass-btn-hover"),this.dispatchEvent("glass-button-hover",{state:"leave",originalEvent:e})}handleFocus(e){this.element.classList.add("glass-btn-focus"),this.dispatchEvent("glass-button-focus",{state:"focus",originalEvent:e})}handleBlur(e){this.element.classList.remove("glass-btn-focus"),this.dispatchEvent("glass-button-focus",{state:"blur",originalEvent:e})}handleKeyDown(e){(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),this.element.click())}createRipple(e){const t=this.element.getBoundingClientRect(),s=e.clientX-t.left,i=e.clientY-t.top,n=document.createElement("div");n.className="glass-btn-ripple",n.style.cssText=`
      position: absolute;
      left: ${s}px;
      top: ${i}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: ${this.options.rippleColor};
      transform: translate(-50%, -50%);
      animation: glass-btn-ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 1;
    `,getComputedStyle(this.element).position==="static"&&(this.element.style.position="relative"),this.element.appendChild(n),this.ripples.add(n),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),this.ripples.delete(n)},600)}setLoading(e=!0){this.isLoading=e,e?(this.element.classList.add("glass-btn-loading"),this.element.setAttribute("aria-busy","true"),this.element.disabled=!0):(this.element.classList.remove("glass-btn-loading"),this.element.setAttribute("aria-busy","false"),this.element.disabled=!1),this.updateState(),this.dispatchEvent("glass-button-loading",{loading:e})}setDisabled(e=!0){this.element.disabled=e,this.element.classList.toggle("glass-btn-disabled",e),this.updateState(),this.dispatchEvent("glass-button-disabled",{disabled:e})}setVariant(e){const t=["glass-btn-primary","glass-btn-secondary","glass-btn-success","glass-btn-warning","glass-btn-danger","glass-btn-ghost"];this.element.classList.remove(...t),e&&e!=="default"&&this.element.classList.add(`glass-btn-${e}`),this.dispatchEvent("glass-button-variant",{variant:e})}setSize(e){const t=["glass-btn-xs","glass-btn-sm","glass-btn-lg","glass-btn-xl"];this.element.classList.remove(...t),e&&e!=="default"&&this.element.classList.add(`glass-btn-${e}`),this.dispatchEvent("glass-button-size",{size:e})}updateState(){const e={loading:this.isLoading,disabled:this.element.disabled,focused:this.element.classList.contains("glass-btn-focus"),hovered:this.element.classList.contains("glass-btn-hover")};this.element.setAttribute("data-state",JSON.stringify(e))}dispatchEvent(e,t={}){const s=new CustomEvent(e,{detail:{button:this,element:this.element,...t},bubbles:!0,cancelable:!0});this.element.dispatchEvent(s)}addAnimation(e,t=1e3){const s=`glass-btn-animate-${e}`;this.element.classList.add(s),setTimeout(()=>{this.element.classList.remove(s)},t)}updateOptions(e){this.options={...this.options,...e}}getState(){return{loading:this.isLoading,disabled:this.element.disabled,variant:this.getVariant(),size:this.getSize()}}getVariant(){return["primary","secondary","success","warning","danger","ghost"].find(t=>this.element.classList.contains(`glass-btn-${t}`))||"default"}getSize(){return["xs","sm","lg","xl"].find(t=>this.element.classList.contains(`glass-btn-${t}`))||"default"}destroy(){this.ripples.forEach(e=>{e.parentNode&&e.parentNode.removeChild(e)}),this.ripples.clear(),this.element.removeAttribute("data-glass-button"),this.element.classList.remove("glass-btn-hover","glass-btn-focus","glass-btn-loading","glass-btn-disabled")}}class v{constructor(e,t={}){this.element=e,this.options={enableHover:!0,enableClick:!0,enableLoading:!0,hoverEffect:"lift",...t},this.isLoading=!1,this.init()}init(){this.setupElement(),this.bindEvents(),this.setupAccessibility()}setupElement(){this.element.classList.contains("glass-card")||this.element.classList.add("glass-card"),this.element.setAttribute("data-glass-card","initialized"),this.updateState()}bindEvents(){this.options.enableClick&&this.element.classList.contains("glass-card-interactive")&&this.element.addEventListener("click",this.handleClick.bind(this)),this.options.enableHover&&(this.element.addEventListener("mouseenter",this.handleMouseEnter.bind(this)),this.element.addEventListener("mouseleave",this.handleMouseLeave.bind(this))),this.element.addEventListener("focus",this.handleFocus.bind(this)),this.element.addEventListener("blur",this.handleBlur.bind(this)),this.element.classList.contains("glass-card-interactive")&&this.element.addEventListener("keydown",this.handleKeyDown.bind(this))}setupAccessibility(){this.element.classList.contains("glass-card-interactive")&&(this.element.hasAttribute("tabindex")||this.element.setAttribute("tabindex","0"),this.element.getAttribute("role")||this.element.setAttribute("role","button")),this.options.enableLoading&&this.element.setAttribute("aria-busy","false")}handleClick(e){if(this.isLoading){e.preventDefault();return}this.dispatchEvent("glass-card-click",{originalEvent:e})}handleMouseEnter(e){switch(this.element.classList.add("glass-card-hover"),this.options.hoverEffect){case"glow":this.element.classList.add("glass-card-glow");break;case"scale":this.element.classList.add("glass-card-scale");break}this.dispatchEvent("glass-card-hover",{state:"enter",originalEvent:e})}handleMouseLeave(e){this.element.classList.remove("glass-card-hover","glass-card-glow","glass-card-scale"),this.dispatchEvent("glass-card-hover",{state:"leave",originalEvent:e})}handleFocus(e){this.element.classList.add("glass-card-focus"),this.dispatchEvent("glass-card-focus",{state:"focus",originalEvent:e})}handleBlur(e){this.element.classList.remove("glass-card-focus"),this.dispatchEvent("glass-card-focus",{state:"blur",originalEvent:e})}handleKeyDown(e){(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),this.element.click())}setLoading(e=!0){this.isLoading=e,e?(this.element.classList.add("glass-card-loading"),this.element.setAttribute("aria-busy","true")):(this.element.classList.remove("glass-card-loading"),this.element.setAttribute("aria-busy","false")),this.updateState(),this.dispatchEvent("glass-card-loading",{loading:e})}setVariant(e){const t=["glass-card-elevated","glass-card-subtle","glass-card-outlined","glass-card-filled"];this.element.classList.remove(...t),e&&e!=="default"&&this.element.classList.add(`glass-card-${e}`),this.dispatchEvent("glass-card-variant",{variant:e})}setSize(e){const t=["glass-card-sm","glass-card-lg"];this.element.classList.remove(...t),e&&e!=="default"&&this.element.classList.add(`glass-card-${e}`),this.dispatchEvent("glass-card-size",{size:e})}addBadge(e,t={}){const s={position:"top-right",variant:"default",...t};this.removeBadge();const i=document.createElement("div");i.className=`glass-card-badge glass-card-badge-${s.position}`,i.textContent=e,s.variant!=="default"&&i.classList.add(`glass-card-badge-${s.variant}`),this.element.appendChild(i),this.dispatchEvent("glass-card-badge-added",{text:e,options:s})}removeBadge(){const e=this.element.querySelector(".glass-card-badge");e&&(e.remove(),this.dispatchEvent("glass-card-badge-removed"))}updateContent(e){const t=this.element.querySelector(".glass-card-body");t&&(typeof e=="string"?t.innerHTML=e:e instanceof HTMLElement&&(t.innerHTML="",t.appendChild(e)),this.dispatchEvent("glass-card-content-updated",{content:e}))}updateState(){const e={loading:this.isLoading,interactive:this.element.classList.contains("glass-card-interactive"),focused:this.element.classList.contains("glass-card-focus"),hovered:this.element.classList.contains("glass-card-hover")};this.element.setAttribute("data-state",JSON.stringify(e))}dispatchEvent(e,t={}){const s=new CustomEvent(e,{detail:{card:this,element:this.element,...t},bubbles:!0,cancelable:!0});this.element.dispatchEvent(s)}addAnimation(e,t=1e3){const s=`glass-card-animate-${e}`;this.element.classList.add(s),setTimeout(()=>{this.element.classList.remove(s)},t)}updateOptions(e){this.options={...this.options,...e}}getState(){return{loading:this.isLoading,interactive:this.element.classList.contains("glass-card-interactive"),variant:this.getVariant(),size:this.getSize()}}getVariant(){return["elevated","subtle","outlined","filled"].find(t=>this.element.classList.contains(`glass-card-${t}`))||"default"}getSize(){return["sm","lg"].find(t=>this.element.classList.contains(`glass-card-${t}`))||"default"}destroy(){this.element.removeAttribute("data-glass-card"),this.element.classList.remove("glass-card-hover","glass-card-focus","glass-card-loading","glass-card-glow","glass-card-scale"),this.removeBadge()}}class b{constructor(e,t={}){this.element=e,this.options={backdrop:!0,keyboard:!0,focus:!0,...t},this.isOpen=!1,this.init()}init(){this.element.setAttribute("data-glass-modal","initialized")}open(){this.isOpen=!0,this.element.classList.add("glass-modal-open")}close(){this.isOpen=!1,this.element.classList.remove("glass-modal-open")}destroy(){this.element.removeAttribute("data-glass-modal")}}class E{constructor(e,t={}){this.element=e,this.options={sticky:!1,collapsible:!0,...t},this.init()}init(){this.element.setAttribute("data-glass-nav","initialized")}destroy(){this.element.removeAttribute("data-glass-nav")}}class y{constructor(e="auto"){this.currentTheme=e,this.systemTheme=this.getSystemTheme(),this.mediaQuery=window.matchMedia("(prefers-color-scheme: dark)"),this.listeners=new Set,this.storageKey="liquid-glass-ui-theme"}init(){const e=this.loadTheme();e&&(this.currentTheme=e),this.applyTheme(),this.mediaQuery.addEventListener("change",this.handleSystemThemeChange.bind(this)),this.initThemeToggles()}getSystemTheme(){return this.mediaQuery.matches?"dark":"light"}getEffectiveTheme(){return this.currentTheme==="auto"?this.systemTheme:this.currentTheme}setTheme(e){["light","dark","auto"].includes(e)||(console.warn(`Invalid theme: ${e}. Using 'auto' instead.`),e="auto"),this.currentTheme=e,this.applyTheme(),this.saveTheme(),this.notifyListeners()}toggleTheme(){const t=this.getEffectiveTheme()==="light"?"dark":"light";this.setTheme(t)}applyTheme(){const e=this.getEffectiveTheme();document.documentElement.classList.remove("theme-light","theme-dark"),document.documentElement.removeAttribute("data-theme"),document.documentElement.classList.add(`theme-${e}`),document.documentElement.setAttribute("data-theme",e),this.updateMetaThemeColor(e),this.dispatchThemeEvent(e)}updateMetaThemeColor(e){let t=document.querySelector('meta[name="theme-color"]');t||(t=document.createElement("meta"),t.name="theme-color",document.head.appendChild(t));const s={light:"#ffffff",dark:"#000000"};t.content=s[e]||s.light}dispatchThemeEvent(e){const t=new CustomEvent("themechange",{detail:{theme:e,previousTheme:this.previousTheme,isSystemTheme:this.currentTheme==="auto"}});this.previousTheme=e,document.dispatchEvent(t)}handleSystemThemeChange(e){this.systemTheme=e.matches?"dark":"light",this.currentTheme==="auto"&&(this.applyTheme(),this.notifyListeners())}initThemeToggles(){document.querySelectorAll("[data-theme-toggle]").forEach(s=>{s.addEventListener("click",()=>{this.toggleTheme()})}),document.querySelectorAll("[data-theme-selector]").forEach(s=>{s.addEventListener("change",i=>{this.setTheme(i.target.value)}),s.value=this.currentTheme})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}notifyListeners(){const e=this.getEffectiveTheme();this.listeners.forEach(t=>{try{t(e,this.currentTheme)}catch(s){console.error("Error in theme change listener:",s)}})}saveTheme(){try{localStorage.setItem(this.storageKey,this.currentTheme)}catch(e){console.warn("Could not save theme preference:",e)}}loadTheme(){try{return localStorage.getItem(this.storageKey)}catch(e){return console.warn("Could not load theme preference:",e),null}}getThemeValue(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}setThemeValue(e,t){document.documentElement.style.setProperty(e,t)}createColorScheme(e,t){return this.getEffectiveTheme()==="dark"?t:e}destroy(){this.mediaQuery.removeEventListener("change",this.handleSystemThemeChange.bind(this)),this.listeners.clear()}getThemeInfo(){return{current:this.currentTheme,effective:this.getEffectiveTheme(),system:this.systemTheme,isAuto:this.currentTheme==="auto"}}}class L{constructor(){this.userAgent=navigator.userAgent.toLowerCase(),this.capabilities=this.detectCapabilities()}isMobile(){return/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent)}isTablet(){return/ipad|android(?!.*mobile)/i.test(this.userAgent)}isDesktop(){return!this.isMobile()&&!this.isTablet()}isIOS(){return/iphone|ipad|ipod/i.test(this.userAgent)}isAndroid(){return/android/i.test(this.userAgent)}supportsTouch(){return"ontouchstart"in window||navigator.maxTouchPoints>0}supportsDeviceOrientation(){return"DeviceOrientationEvent"in window}supportsDeviceMotion(){return"DeviceMotionEvent"in window}supportsBackdropFilter(){return CSS.supports("backdrop-filter","blur(1px)")||CSS.supports("-webkit-backdrop-filter","blur(1px)")}prefersReducedMotion(){return window.matchMedia("(prefers-reduced-motion: reduce)").matches}getPixelRatio(){return window.devicePixelRatio||1}getViewport(){return{width:window.innerWidth,height:window.innerHeight}}hasHighRefreshRate(){return new Promise(e=>{let t=0;const s=performance.now(),i=()=>{if(t++,t<60)requestAnimationFrame(i);else{const n=performance.now()-s,a=Math.round(t/(n/1e3));e(a>60)}};requestAnimationFrame(i)})}detectCapabilities(){return{isMobile:this.isMobile(),isTablet:this.isTablet(),isDesktop:this.isDesktop(),isIOS:this.isIOS(),isAndroid:this.isAndroid(),supportsTouch:this.supportsTouch(),supportsDeviceOrientation:this.supportsDeviceOrientation(),supportsDeviceMotion:this.supportsDeviceMotion(),supportsBackdropFilter:this.supportsBackdropFilter(),prefersReducedMotion:this.prefersReducedMotion(),pixelRatio:this.getPixelRatio(),viewport:this.getViewport()}}getOptimalSettings(){const e={blurIntensity:"medium",animationDuration:"normal",enableCursorEffects:!1,enableMotionEffects:!1,enableParallax:!1,enableHighFrameRate:!1};return this.isDesktop()&&(e.enableCursorEffects=!0,e.blurIntensity="high",e.enableHighFrameRate=!0),this.isMobile()&&(e.enableMotionEffects=this.supportsDeviceOrientation(),e.blurIntensity="low",e.animationDuration="fast"),this.getPixelRatio()>2&&(e.enableParallax=!0),this.prefersReducedMotion()&&(e.animationDuration="none",e.enableMotionEffects=!1,e.enableParallax=!1),this.supportsBackdropFilter()||(e.blurIntensity="none"),e}addDeviceClasses(){const e=[];this.isMobile()&&e.push("device-mobile"),this.isTablet()&&e.push("device-tablet"),this.isDesktop()&&e.push("device-desktop"),this.isIOS()&&e.push("device-ios"),this.isAndroid()&&e.push("device-android"),this.supportsTouch()&&e.push("supports-touch"),this.supportsBackdropFilter()&&e.push("supports-backdrop-filter"),this.prefersReducedMotion()&&e.push("prefers-reduced-motion"),document.documentElement.classList.add(...e)}onViewportChange(e){let t;const s=()=>{clearTimeout(t),t=setTimeout(()=>{this.capabilities.viewport=this.getViewport(),e(this.capabilities.viewport)},100)};return window.addEventListener("resize",s),window.addEventListener("orientationchange",s),()=>{window.removeEventListener("resize",s),window.removeEventListener("orientationchange",s)}}}class u{constructor(e={}){this.options={enableCursorEffects:!0,enableMotionEffects:!0,enableAnimations:!0,theme:"auto",...e},this.effects=new d(this.options),this.themeManager=new y(this.options.theme),this.deviceDetector=new L,this.init()}init(){this.themeManager.init(),this.deviceDetector.isDesktop()&&this.options.enableCursorEffects&&(this.cursorEffects=new p,this.cursorEffects.init()),this.deviceDetector.isMobile()&&this.options.enableMotionEffects&&(this.motionEffects=new f,this.motionEffects.init()),this.initComponents(),document.documentElement.setAttribute("data-liquid-glass-ui","initialized")}initComponents(){this.initButtons(),this.initCards(),this.initModals(),this.initNavigation()}initButtons(){document.querySelectorAll("[data-glass-button]").forEach(t=>new g(t))}initCards(){document.querySelectorAll("[data-glass-card]").forEach(t=>new v(t))}initModals(){document.querySelectorAll("[data-glass-modal]").forEach(t=>new b(t))}initNavigation(){document.querySelectorAll("[data-glass-nav]").forEach(t=>new E(t))}setTheme(e){this.themeManager.setTheme(e)}destroy(){this.cursorEffects&&this.cursorEffects.destroy(),this.motionEffects&&this.motionEffects.destroy(),this.themeManager.destroy(),document.documentElement.removeAttribute("data-liquid-glass-ui")}}typeof window<"u"&&!window.LiquidGlassUI&&(window.LiquidGlassUI=u,document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{document.querySelector("[data-no-auto-init]")||new u}):document.querySelector("[data-no-auto-init]")||new u),o.CursorEffects=p,o.DeviceDetector=L,o.GlassButton=g,o.GlassCard=v,o.GlassEffects=d,o.GlassModal=b,o.GlassNavigation=E,o.MotionEffects=f,o.ThemeManager=y,o.default=u,Object.defineProperties(o,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
//# sourceMappingURL=index.umd.js.map
