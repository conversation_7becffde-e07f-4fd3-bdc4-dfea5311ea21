/**
 * Liquid Glass UI - Main Entry Point
 * A comprehensive JavaScript/CSS UI framework that replicates Apple's liquid glass design aesthetic
 */

// Import CSS
import './css/index.css'

// Import core utilities
import { GlassEffects } from './js/effects/glass-effects.js'
import { CursorEffects } from './js/effects/cursor-effects.js'
import { MotionEffects } from './js/effects/motion-effects.js'

// Import components
import { GlassButton } from './js/components/button.js'
import { GlassCard } from './js/components/card.js'
import { GlassModal } from './js/components/modal.js'
import { GlassNavigation } from './js/components/navigation.js'

// Import utilities
import { ThemeManager } from './js/utils/theme-manager.js'
import { DeviceDetector } from './js/utils/device-detector.js'

/**
 * Main LiquidGlassUI class
 */
class LiquidGlassUI {
  constructor(options = {}) {
    this.options = {
      enableCursorEffects: true,
      enableMotionEffects: true,
      enableAnimations: true,
      theme: 'auto', // 'light', 'dark', 'auto'
      ...options,
    }

    this.effects = new GlassEffects(this.options)
    this.themeManager = new ThemeManager(this.options.theme)
    this.deviceDetector = new DeviceDetector()

    this.init()
  }

  /**
   * Initialize the framework
   */
  init() {
    // Initialize theme
    this.themeManager.init()

    // Initialize effects based on device capabilities
    if (this.deviceDetector.isDesktop() && this.options.enableCursorEffects) {
      this.cursorEffects = new CursorEffects()
      this.cursorEffects.init()
    }

    if (this.deviceDetector.isMobile() && this.options.enableMotionEffects) {
      this.motionEffects = new MotionEffects()
      this.motionEffects.init()
    }

    // Initialize components
    this.initComponents()

    // Mark as initialized
    document.documentElement.setAttribute('data-liquid-glass-ui', 'initialized')
  }

  /**
   * Initialize all components
   */
  initComponents() {
    // Auto-initialize components with data attributes
    this.initButtons()
    this.initCards()
    this.initModals()
    this.initNavigation()
  }

  /**
   * Initialize button components
   */
  initButtons() {
    const buttons = document.querySelectorAll('[data-glass-button]')
    buttons.forEach(button => new GlassButton(button))
  }

  /**
   * Initialize card components
   */
  initCards() {
    const cards = document.querySelectorAll('[data-glass-card]')
    cards.forEach(card => new GlassCard(card))
  }

  /**
   * Initialize modal components
   */
  initModals() {
    const modals = document.querySelectorAll('[data-glass-modal]')
    modals.forEach(modal => new GlassModal(modal))
  }

  /**
   * Initialize navigation components
   */
  initNavigation() {
    const navs = document.querySelectorAll('[data-glass-nav]')
    navs.forEach(nav => new GlassNavigation(nav))
  }

  /**
   * Update theme
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme)
  }

  /**
   * Destroy the framework instance
   */
  destroy() {
    if (this.cursorEffects) this.cursorEffects.destroy()
    if (this.motionEffects) this.motionEffects.destroy()
    this.themeManager.destroy()
    
    document.documentElement.removeAttribute('data-liquid-glass-ui')
  }
}

// Export for module usage
export default LiquidGlassUI

// Export individual components and utilities
export {
  GlassEffects,
  CursorEffects,
  MotionEffects,
  GlassButton,
  GlassCard,
  GlassModal,
  GlassNavigation,
  ThemeManager,
  DeviceDetector,
}

// Auto-initialize if not in module environment
if (typeof window !== 'undefined' && !window.LiquidGlassUI) {
  window.LiquidGlassUI = LiquidGlassUI
  
  // Auto-initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      if (!document.querySelector('[data-no-auto-init]')) {
        new LiquidGlassUI()
      }
    })
  } else {
    if (!document.querySelector('[data-no-auto-init]')) {
      new LiquidGlassUI()
    }
  }
}
