{"version": 3, "file": "index.es.js", "sources": ["../src/js/effects/glass-effects.js", "../src/js/effects/cursor-effects.js", "../src/js/effects/motion-effects.js", "../src/js/components/button.js", "../src/js/components/card.js", "../src/js/components/modal.js", "../src/js/components/navigation.js", "../src/js/utils/theme-manager.js", "../src/js/utils/device-detector.js", "../src/index.js"], "sourcesContent": ["/**\n * Glass Effects\n * Core glass effect management and utilities\n */\n\nexport class GlassEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableAnimations: true,\n      enableInteractions: true,\n      performanceMode: 'auto', // 'low', 'medium', 'high', 'auto'\n      ...options\n    }\n\n    this.elements = new Map()\n    this.observers = new Map()\n    this.isInitialized = false\n  }\n\n  /**\n   * Initialize glass effects system\n   */\n  init() {\n    if (this.isInitialized) return\n\n    this.detectPerformanceCapabilities()\n    this.setupIntersectionObserver()\n    this.bindGlobalEvents()\n    this.scanForElements()\n    \n    this.isInitialized = true\n  }\n\n  /**\n   * Detect device performance capabilities\n   */\n  detectPerformanceCapabilities() {\n    if (this.options.performanceMode !== 'auto') return\n\n    const deviceMemory = navigator.deviceMemory || 4\n    const hardwareConcurrency = navigator.hardwareConcurrency || 4\n    const connection = navigator.connection\n\n    let performanceScore = 0\n\n    // Memory score (0-3)\n    if (deviceMemory >= 8) performanceScore += 3\n    else if (deviceMemory >= 4) performanceScore += 2\n    else if (deviceMemory >= 2) performanceScore += 1\n\n    // CPU score (0-3)\n    if (hardwareConcurrency >= 8) performanceScore += 3\n    else if (hardwareConcurrency >= 4) performanceScore += 2\n    else if (hardwareConcurrency >= 2) performanceScore += 1\n\n    // Connection score (0-2)\n    if (connection) {\n      if (connection.effectiveType === '4g') performanceScore += 2\n      else if (connection.effectiveType === '3g') performanceScore += 1\n    } else {\n      performanceScore += 2 // Assume good connection if unknown\n    }\n\n    // Set performance mode based on score\n    if (performanceScore >= 6) this.options.performanceMode = 'high'\n    else if (performanceScore >= 4) this.options.performanceMode = 'medium'\n    else this.options.performanceMode = 'low'\n\n    // Add performance class to document\n    document.documentElement.classList.add(`performance-${this.options.performanceMode}`)\n  }\n\n  /**\n   * Setup intersection observer for performance optimization\n   */\n  setupIntersectionObserver() {\n    if (!('IntersectionObserver' in window)) return\n\n    this.intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          const element = entry.target\n          const effectData = this.elements.get(element)\n          \n          if (!effectData) return\n\n          if (entry.isIntersecting) {\n            this.activateElement(element, effectData)\n          } else {\n            this.deactivateElement(element, effectData)\n          }\n        })\n      },\n      {\n        rootMargin: '50px',\n        threshold: 0.1\n      }\n    )\n  }\n\n  /**\n   * Bind global events\n   */\n  bindGlobalEvents() {\n    // Handle visibility change for performance\n    document.addEventListener('visibilitychange', () => {\n      if (document.hidden) {\n        this.pauseAllEffects()\n      } else {\n        this.resumeAllEffects()\n      }\n    })\n\n    // Handle resize events\n    let resizeTimeout\n    window.addEventListener('resize', () => {\n      clearTimeout(resizeTimeout)\n      resizeTimeout = setTimeout(() => {\n        this.updateAllElements()\n      }, 100)\n    })\n  }\n\n  /**\n   * Scan for elements with glass effects\n   */\n  scanForElements() {\n    const selectors = [\n      '[data-glass-effect]',\n      '.glass',\n      '.glass-subtle',\n      '.glass-light',\n      '.glass-medium',\n      '.glass-strong',\n      '.glass-intense'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to glass effects system\n   */\n  addElement(element, options = {}) {\n    if (this.elements.has(element)) return\n\n    const effectData = {\n      element,\n      options: { ...this.getDefaultOptions(), ...options },\n      isActive: false,\n      animations: new Set(),\n      observers: new Set()\n    }\n\n    this.elements.set(element, effectData)\n    \n    // Add to intersection observer\n    if (this.intersectionObserver) {\n      this.intersectionObserver.observe(element)\n    } else {\n      this.activateElement(element, effectData)\n    }\n\n    // Initialize element\n    this.initializeElement(element, effectData)\n  }\n\n  /**\n   * Get default options based on performance mode\n   */\n  getDefaultOptions() {\n    const baseOptions = {\n      enableBlur: true,\n      enableShadows: true,\n      enableAnimations: this.options.enableAnimations,\n      enableInteractions: this.options.enableInteractions\n    }\n\n    switch (this.options.performanceMode) {\n      case 'low':\n        return {\n          ...baseOptions,\n          enableBlur: false,\n          enableAnimations: false,\n          maxBlur: 4\n        }\n      case 'medium':\n        return {\n          ...baseOptions,\n          maxBlur: 8\n        }\n      case 'high':\n        return {\n          ...baseOptions,\n          maxBlur: 16\n        }\n      default:\n        return baseOptions\n    }\n  }\n\n  /**\n   * Initialize element with glass effects\n   */\n  initializeElement(element, effectData) {\n    const { options } = effectData\n\n    // Add base glass classes\n    element.classList.add('glass-element')\n    \n    // Add performance-specific classes\n    element.classList.add(`glass-performance-${this.options.performanceMode}`)\n\n    // Set up CSS custom properties\n    this.updateElementProperties(element, effectData)\n\n    // Add interaction listeners if enabled\n    if (options.enableInteractions) {\n      this.addInteractionListeners(element, effectData)\n    }\n  }\n\n  /**\n   * Update element CSS custom properties\n   */\n  updateElementProperties(element, effectData) {\n    const { options } = effectData\n    const rect = element.getBoundingClientRect()\n\n    // Set size properties\n    element.style.setProperty('--element-width', `${rect.width}px`)\n    element.style.setProperty('--element-height', `${rect.height}px`)\n\n    // Set performance properties\n    element.style.setProperty('--max-blur', `${options.maxBlur || 8}px`)\n    element.style.setProperty('--enable-animations', options.enableAnimations ? '1' : '0')\n  }\n\n  /**\n   * Add interaction listeners to element\n   */\n  addInteractionListeners(element, effectData) {\n    const listeners = {\n      mouseenter: () => this.handleElementHover(element, effectData, true),\n      mouseleave: () => this.handleElementHover(element, effectData, false),\n      focus: () => this.handleElementFocus(element, effectData, true),\n      blur: () => this.handleElementFocus(element, effectData, false),\n      click: (event) => this.handleElementClick(element, effectData, event)\n    }\n\n    Object.entries(listeners).forEach(([event, handler]) => {\n      element.addEventListener(event, handler)\n      effectData.observers.add(() => {\n        element.removeEventListener(event, handler)\n      })\n    })\n  }\n\n  /**\n   * Handle element hover\n   */\n  handleElementHover(element, effectData, isHovering) {\n    if (!effectData.options.enableInteractions) return\n\n    element.classList.toggle('glass-hover', isHovering)\n    \n    if (isHovering) {\n      this.addAnimation(element, 'hover-in')\n    } else {\n      this.addAnimation(element, 'hover-out')\n    }\n  }\n\n  /**\n   * Handle element focus\n   */\n  handleElementFocus(element, effectData, isFocused) {\n    if (!effectData.options.enableInteractions) return\n\n    element.classList.toggle('glass-focus', isFocused)\n  }\n\n  /**\n   * Handle element click\n   */\n  handleElementClick(element, effectData, event) {\n    if (!effectData.options.enableInteractions) return\n\n    this.createRippleEffect(element, event)\n    this.addAnimation(element, 'click')\n  }\n\n  /**\n   * Create ripple effect\n   */\n  createRippleEffect(element, event) {\n    const rect = element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'glass-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.3);\n      transform: translate(-50%, -50%);\n      animation: glass-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1000;\n    `\n\n    element.style.position = 'relative'\n    element.appendChild(ripple)\n\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    }, 600)\n  }\n\n  /**\n   * Add animation to element\n   */\n  addAnimation(element, animationType) {\n    const effectData = this.elements.get(element)\n    if (!effectData || !effectData.options.enableAnimations) return\n\n    effectData.animations.add(animationType)\n    element.classList.add(`glass-animate-${animationType}`)\n\n    setTimeout(() => {\n      effectData.animations.delete(animationType)\n      element.classList.remove(`glass-animate-${animationType}`)\n    }, 300)\n  }\n\n  /**\n   * Activate element effects\n   */\n  activateElement(element, effectData) {\n    if (effectData.isActive) return\n\n    effectData.isActive = true\n    element.classList.add('glass-active')\n  }\n\n  /**\n   * Deactivate element effects\n   */\n  deactivateElement(element, effectData) {\n    if (!effectData.isActive) return\n\n    effectData.isActive = false\n    element.classList.remove('glass-active')\n  }\n\n  /**\n   * Remove element from glass effects system\n   */\n  removeElement(element) {\n    const effectData = this.elements.get(element)\n    if (!effectData) return\n\n    // Remove from intersection observer\n    if (this.intersectionObserver) {\n      this.intersectionObserver.unobserve(element)\n    }\n\n    // Clean up observers\n    effectData.observers.forEach(cleanup => cleanup())\n\n    // Remove classes\n    element.classList.remove('glass-element', 'glass-active', 'glass-hover', 'glass-focus')\n\n    // Remove from elements map\n    this.elements.delete(element)\n  }\n\n  /**\n   * Update all elements\n   */\n  updateAllElements() {\n    this.elements.forEach((effectData, element) => {\n      this.updateElementProperties(element, effectData)\n    })\n  }\n\n  /**\n   * Pause all effects\n   */\n  pauseAllEffects() {\n    this.elements.forEach((effectData, element) => {\n      element.classList.add('glass-paused')\n    })\n  }\n\n  /**\n   * Resume all effects\n   */\n  resumeAllEffects() {\n    this.elements.forEach((effectData, element) => {\n      element.classList.remove('glass-paused')\n    })\n  }\n\n  /**\n   * Destroy glass effects system\n   */\n  destroy() {\n    // Clean up all elements\n    this.elements.forEach((effectData, element) => {\n      this.removeElement(element)\n    })\n\n    // Clean up observers\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect()\n    }\n\n    this.elements.clear()\n    this.observers.clear()\n    this.isInitialized = false\n  }\n}\n", "/**\n * Cursor Effects\n * Creates dynamic cursor-based shadow and lighting effects for desktop users\n */\n\nexport class CursorEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableShadows: true,\n      enableLighting: true,\n      shadowIntensity: 0.3,\n      lightingIntensity: 0.2,\n      maxDistance: 200,\n      smoothing: 0.1,\n      ...options\n    }\n\n    this.cursor = { x: 0, y: 0 }\n    this.smoothCursor = { x: 0, y: 0 }\n    this.elements = new Set()\n    this.isActive = false\n    this.animationFrame = null\n  }\n\n  /**\n   * Initialize cursor effects\n   */\n  init() {\n    if (this.isActive) return\n\n    this.isActive = true\n    this.bindEvents()\n    this.findElements()\n    this.startAnimation()\n  }\n\n  /**\n   * Bind mouse events\n   */\n  bindEvents() {\n    this.handleMouseMove = this.handleMouseMove.bind(this)\n    this.handleMouseLeave = this.handleMouseLeave.bind(this)\n\n    document.addEventListener('mousemove', this.handleMouseMove)\n    document.addEventListener('mouseleave', this.handleMouseLeave)\n  }\n\n  /**\n   * Find elements with cursor effects\n   */\n  findElements() {\n    const selectors = [\n      '[data-cursor-effect]',\n      '.glass-cursor-effect',\n      '.glass',\n      '.glass-button',\n      '.glass-card'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to cursor effects\n   */\n  addElement(element) {\n    if (this.elements.has(element)) return\n\n    this.elements.add(element)\n    \n    // Add CSS custom properties for cursor effects\n    element.style.setProperty('--cursor-x', '0')\n    element.style.setProperty('--cursor-y', '0')\n    element.style.setProperty('--cursor-distance', '1')\n    element.style.setProperty('--cursor-intensity', '0')\n\n    // Add cursor effect class\n    element.classList.add('has-cursor-effect')\n  }\n\n  /**\n   * Remove element from cursor effects\n   */\n  removeElement(element) {\n    if (!this.elements.has(element)) return\n\n    this.elements.delete(element)\n    element.classList.remove('has-cursor-effect')\n    \n    // Reset CSS custom properties\n    element.style.removeProperty('--cursor-x')\n    element.style.removeProperty('--cursor-y')\n    element.style.removeProperty('--cursor-distance')\n    element.style.removeProperty('--cursor-intensity')\n  }\n\n  /**\n   * Handle mouse move\n   */\n  handleMouseMove(event) {\n    this.cursor.x = event.clientX\n    this.cursor.y = event.clientY\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave() {\n    this.cursor.x = -1000\n    this.cursor.y = -1000\n  }\n\n  /**\n   * Start animation loop\n   */\n  startAnimation() {\n    const animate = () => {\n      if (!this.isActive) return\n\n      this.updateCursor()\n      this.updateElements()\n      \n      this.animationFrame = requestAnimationFrame(animate)\n    }\n\n    animate()\n  }\n\n  /**\n   * Update smooth cursor position\n   */\n  updateCursor() {\n    this.smoothCursor.x += (this.cursor.x - this.smoothCursor.x) * this.options.smoothing\n    this.smoothCursor.y += (this.cursor.y - this.smoothCursor.y) * this.options.smoothing\n  }\n\n  /**\n   * Update all elements with cursor effects\n   */\n  updateElements() {\n    this.elements.forEach(element => {\n      this.updateElement(element)\n    })\n  }\n\n  /**\n   * Update individual element\n   */\n  updateElement(element) {\n    const rect = element.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n\n    // Calculate distance from cursor to element center\n    const deltaX = this.smoothCursor.x - centerX\n    const deltaY = this.smoothCursor.y - centerY\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)\n\n    // Calculate normalized values\n    const normalizedDistance = Math.min(distance / this.options.maxDistance, 1)\n    const intensity = 1 - normalizedDistance\n\n    // Calculate relative position within element\n    const relativeX = (this.smoothCursor.x - rect.left) / rect.width\n    const relativeY = (this.smoothCursor.y - rect.top) / rect.height\n\n    // Update CSS custom properties\n    element.style.setProperty('--cursor-x', relativeX.toFixed(3))\n    element.style.setProperty('--cursor-y', relativeY.toFixed(3))\n    element.style.setProperty('--cursor-distance', normalizedDistance.toFixed(3))\n    element.style.setProperty('--cursor-intensity', intensity.toFixed(3))\n\n    // Apply shadow effects\n    if (this.options.enableShadows) {\n      this.applyShadowEffect(element, deltaX, deltaY, intensity)\n    }\n\n    // Apply lighting effects\n    if (this.options.enableLighting) {\n      this.applyLightingEffect(element, relativeX, relativeY, intensity)\n    }\n  }\n\n  /**\n   * Apply shadow effect based on cursor position\n   */\n  applyShadowEffect(element, deltaX, deltaY, intensity) {\n    const shadowIntensity = intensity * this.options.shadowIntensity\n    const shadowX = -deltaX * 0.1 * shadowIntensity\n    const shadowY = -deltaY * 0.1 * shadowIntensity\n    const shadowBlur = 20 * shadowIntensity\n    const shadowOpacity = 0.3 * shadowIntensity\n\n    const shadow = `${shadowX}px ${shadowY}px ${shadowBlur}px rgba(0, 0, 0, ${shadowOpacity})`\n    element.style.setProperty('--cursor-shadow', shadow)\n  }\n\n  /**\n   * Apply lighting effect based on cursor position\n   */\n  applyLightingEffect(element, relativeX, relativeY, intensity) {\n    const lightIntensity = intensity * this.options.lightingIntensity\n    \n    // Create radial gradient for lighting effect\n    const gradientX = relativeX * 100\n    const gradientY = relativeY * 100\n    const gradientSize = 150 * intensity\n    \n    const gradient = `radial-gradient(${gradientSize}px circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`\n    element.style.setProperty('--cursor-light', gradient)\n  }\n\n  /**\n   * Create ripple effect at cursor position\n   */\n  createRipple(element, event) {\n    const rect = element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'cursor-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.3);\n      transform: translate(-50%, -50%);\n      animation: cursor-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1000;\n    `\n\n    element.style.position = 'relative'\n    element.appendChild(ripple)\n\n    // Remove ripple after animation\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    }, 600)\n  }\n\n  /**\n   * Add ripple effect to element\n   */\n  addRippleEffect(element) {\n    const handleClick = (event) => {\n      this.createRipple(element, event)\n    }\n\n    element.addEventListener('click', handleClick)\n    \n    return () => {\n      element.removeEventListener('click', handleClick)\n    }\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Pause cursor effects\n   */\n  pause() {\n    this.isActive = false\n    if (this.animationFrame) {\n      cancelAnimationFrame(this.animationFrame)\n      this.animationFrame = null\n    }\n  }\n\n  /**\n   * Resume cursor effects\n   */\n  resume() {\n    if (!this.isActive) {\n      this.isActive = true\n      this.startAnimation()\n    }\n  }\n\n  /**\n   * Destroy cursor effects\n   */\n  destroy() {\n    this.pause()\n    \n    // Remove event listeners\n    document.removeEventListener('mousemove', this.handleMouseMove)\n    document.removeEventListener('mouseleave', this.handleMouseLeave)\n\n    // Clean up elements\n    this.elements.forEach(element => {\n      this.removeElement(element)\n    })\n    \n    this.elements.clear()\n  }\n\n  /**\n   * Get cursor position\n   */\n  getCursorPosition() {\n    return { ...this.smoothCursor }\n  }\n\n  /**\n   * Check if cursor effects are active\n   */\n  isEnabled() {\n    return this.isActive\n  }\n}\n", "/**\n * Motion Effects\n * Creates device orientation-based effects for mobile devices\n */\n\nexport class MotionEffects {\n  constructor(options = {}) {\n    this.options = {\n      enableTilt: true,\n      enableParallax: true,\n      enableLighting: true,\n      sensitivity: 1,\n      maxTilt: 15,\n      smoothing: 0.1,\n      ...options\n    }\n\n    this.orientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.elements = new Set()\n    this.isActive = false\n    this.animationFrame = null\n    this.permissionGranted = false\n  }\n\n  /**\n   * Initialize motion effects\n   */\n  async init() {\n    if (this.isActive) return\n\n    // Check if device orientation is supported\n    if (!this.isSupported()) {\n      console.warn('Device orientation not supported')\n      return false\n    }\n\n    // Request permission for iOS 13+\n    if (typeof DeviceOrientationEvent.requestPermission === 'function') {\n      try {\n        const permission = await DeviceOrientationEvent.requestPermission()\n        this.permissionGranted = permission === 'granted'\n      } catch (error) {\n        console.warn('Device orientation permission denied:', error)\n        return false\n      }\n    } else {\n      this.permissionGranted = true\n    }\n\n    if (!this.permissionGranted) {\n      return false\n    }\n\n    this.isActive = true\n    this.bindEvents()\n    this.findElements()\n    this.startAnimation()\n    \n    return true\n  }\n\n  /**\n   * Check if device orientation is supported\n   */\n  isSupported() {\n    return 'DeviceOrientationEvent' in window\n  }\n\n  /**\n   * Bind device orientation events\n   */\n  bindEvents() {\n    this.handleDeviceOrientation = this.handleDeviceOrientation.bind(this)\n    window.addEventListener('deviceorientation', this.handleDeviceOrientation)\n  }\n\n  /**\n   * Find elements with motion effects\n   */\n  findElements() {\n    const selectors = [\n      '[data-motion-effect]',\n      '.glass-motion-effect',\n      '.motion-tilt',\n      '.motion-parallax'\n    ]\n\n    selectors.forEach(selector => {\n      document.querySelectorAll(selector).forEach(element => {\n        this.addElement(element)\n      })\n    })\n  }\n\n  /**\n   * Add element to motion effects\n   */\n  addElement(element) {\n    if (this.elements.has(element)) return\n\n    this.elements.add(element)\n    \n    // Add CSS custom properties for motion effects\n    element.style.setProperty('--motion-x', '0')\n    element.style.setProperty('--motion-y', '0')\n    element.style.setProperty('--motion-z', '0')\n    element.style.setProperty('--motion-intensity', '0')\n\n    // Add motion effect class\n    element.classList.add('has-motion-effect')\n  }\n\n  /**\n   * Remove element from motion effects\n   */\n  removeElement(element) {\n    if (!this.elements.has(element)) return\n\n    this.elements.delete(element)\n    element.classList.remove('has-motion-effect')\n    \n    // Reset CSS custom properties\n    element.style.removeProperty('--motion-x')\n    element.style.removeProperty('--motion-y')\n    element.style.removeProperty('--motion-z')\n    element.style.removeProperty('--motion-intensity')\n  }\n\n  /**\n   * Handle device orientation change\n   */\n  handleDeviceOrientation(event) {\n    this.orientation.alpha = event.alpha || 0  // Z axis (0-360)\n    this.orientation.beta = event.beta || 0    // X axis (-180 to 180)\n    this.orientation.gamma = event.gamma || 0  // Y axis (-90 to 90)\n  }\n\n  /**\n   * Start animation loop\n   */\n  startAnimation() {\n    const animate = () => {\n      if (!this.isActive) return\n\n      this.updateOrientation()\n      this.updateElements()\n      \n      this.animationFrame = requestAnimationFrame(animate)\n    }\n\n    animate()\n  }\n\n  /**\n   * Update smooth orientation values\n   */\n  updateOrientation() {\n    this.smoothOrientation.alpha += (this.orientation.alpha - this.smoothOrientation.alpha) * this.options.smoothing\n    this.smoothOrientation.beta += (this.orientation.beta - this.smoothOrientation.beta) * this.options.smoothing\n    this.smoothOrientation.gamma += (this.orientation.gamma - this.smoothOrientation.gamma) * this.options.smoothing\n  }\n\n  /**\n   * Update all elements with motion effects\n   */\n  updateElements() {\n    this.elements.forEach(element => {\n      this.updateElement(element)\n    })\n  }\n\n  /**\n   * Update individual element\n   */\n  updateElement(element) {\n    const { beta, gamma } = this.smoothOrientation\n    \n    // Normalize orientation values\n    const normalizedX = this.clamp(gamma / 90, -1, 1) * this.options.sensitivity\n    const normalizedY = this.clamp(beta / 180, -1, 1) * this.options.sensitivity\n    const intensity = Math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY)\n\n    // Update CSS custom properties\n    element.style.setProperty('--motion-x', normalizedX.toFixed(3))\n    element.style.setProperty('--motion-y', normalizedY.toFixed(3))\n    element.style.setProperty('--motion-intensity', Math.min(intensity, 1).toFixed(3))\n\n    // Apply specific effects\n    if (this.options.enableTilt) {\n      this.applyTiltEffect(element, normalizedX, normalizedY)\n    }\n\n    if (this.options.enableParallax) {\n      this.applyParallaxEffect(element, normalizedX, normalizedY)\n    }\n\n    if (this.options.enableLighting) {\n      this.applyLightingEffect(element, normalizedX, normalizedY, intensity)\n    }\n  }\n\n  /**\n   * Apply tilt effect based on device orientation\n   */\n  applyTiltEffect(element, x, y) {\n    const tiltX = y * this.options.maxTilt\n    const tiltY = -x * this.options.maxTilt\n    \n    element.style.setProperty('--motion-tilt-x', `${tiltX}deg`)\n    element.style.setProperty('--motion-tilt-y', `${tiltY}deg`)\n  }\n\n  /**\n   * Apply parallax effect based on device orientation\n   */\n  applyParallaxEffect(element, x, y) {\n    const parallaxX = x * 20\n    const parallaxY = y * 20\n    \n    element.style.setProperty('--motion-parallax-x', `${parallaxX}px`)\n    element.style.setProperty('--motion-parallax-y', `${parallaxY}px`)\n  }\n\n  /**\n   * Apply lighting effect based on device orientation\n   */\n  applyLightingEffect(element, x, y, intensity) {\n    // Create gradient based on tilt direction\n    const gradientX = (x + 1) * 50  // Convert -1,1 to 0,100\n    const gradientY = (y + 1) * 50  // Convert -1,1 to 0,100\n    const lightIntensity = intensity * 0.3\n    \n    const gradient = `radial-gradient(circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`\n    element.style.setProperty('--motion-light', gradient)\n  }\n\n  /**\n   * Create shake effect\n   */\n  createShake(element, intensity = 1) {\n    const shakeClass = `motion-shake-${Math.floor(intensity * 3) + 1}`\n    element.classList.add(shakeClass)\n    \n    setTimeout(() => {\n      element.classList.remove(shakeClass)\n    }, 500)\n  }\n\n  /**\n   * Clamp value between min and max\n   */\n  clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max)\n  }\n\n  /**\n   * Request permission for device orientation (iOS 13+)\n   */\n  async requestPermission() {\n    if (typeof DeviceOrientationEvent.requestPermission === 'function') {\n      try {\n        const permission = await DeviceOrientationEvent.requestPermission()\n        this.permissionGranted = permission === 'granted'\n        return this.permissionGranted\n      } catch (error) {\n        console.error('Error requesting device orientation permission:', error)\n        return false\n      }\n    }\n    \n    this.permissionGranted = true\n    return true\n  }\n\n  /**\n   * Calibrate device orientation\n   */\n  calibrate() {\n    // Reset orientation baseline\n    this.orientation = { alpha: 0, beta: 0, gamma: 0 }\n    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Pause motion effects\n   */\n  pause() {\n    this.isActive = false\n    if (this.animationFrame) {\n      cancelAnimationFrame(this.animationFrame)\n      this.animationFrame = null\n    }\n  }\n\n  /**\n   * Resume motion effects\n   */\n  resume() {\n    if (!this.isActive && this.permissionGranted) {\n      this.isActive = true\n      this.startAnimation()\n    }\n  }\n\n  /**\n   * Destroy motion effects\n   */\n  destroy() {\n    this.pause()\n    \n    // Remove event listeners\n    window.removeEventListener('deviceorientation', this.handleDeviceOrientation)\n\n    // Clean up elements\n    this.elements.forEach(element => {\n      this.removeElement(element)\n    })\n    \n    this.elements.clear()\n  }\n\n  /**\n   * Get current orientation\n   */\n  getOrientation() {\n    return { ...this.smoothOrientation }\n  }\n\n  /**\n   * Check if motion effects are active\n   */\n  isEnabled() {\n    return this.isActive && this.permissionGranted\n  }\n}\n", "/**\n * Glass Button Component\n * Enhanced button with glass effects and interactions\n */\n\nexport class GlassButton {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      enableRipple: true,\n      enableHover: true,\n      enableFocus: true,\n      enableLoading: true,\n      rippleColor: 'rgba(255, 255, 255, 0.3)',\n      ...options\n    }\n\n    this.isLoading = false\n    this.ripples = new Set()\n    \n    this.init()\n  }\n\n  /**\n   * Initialize button component\n   */\n  init() {\n    this.setupElement()\n    this.bindEvents()\n    this.setupAccessibility()\n  }\n\n  /**\n   * Setup button element\n   */\n  setupElement() {\n    // Add base glass button class if not present\n    if (!this.element.classList.contains('glass-btn')) {\n      this.element.classList.add('glass-btn')\n    }\n\n    // Add component identifier\n    this.element.setAttribute('data-glass-button', 'initialized')\n\n    // Setup initial state\n    this.updateState()\n  }\n\n  /**\n   * Bind event listeners\n   */\n  bindEvents() {\n    // Click events\n    this.element.addEventListener('click', this.handleClick.bind(this))\n    \n    // Mouse events for hover effects\n    if (this.options.enableHover) {\n      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))\n      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))\n    }\n\n    // Focus events\n    if (this.options.enableFocus) {\n      this.element.addEventListener('focus', this.handleFocus.bind(this))\n      this.element.addEventListener('blur', this.handleBlur.bind(this))\n    }\n\n    // Keyboard events\n    this.element.addEventListener('keydown', this.handleKeyDown.bind(this))\n  }\n\n  /**\n   * Setup accessibility features\n   */\n  setupAccessibility() {\n    // Ensure button has proper role\n    if (!this.element.getAttribute('role') && this.element.tagName !== 'BUTTON') {\n      this.element.setAttribute('role', 'button')\n    }\n\n    // Ensure button is focusable\n    if (!this.element.hasAttribute('tabindex') && this.element.tagName !== 'BUTTON') {\n      this.element.setAttribute('tabindex', '0')\n    }\n\n    // Add ARIA attributes for loading state\n    if (this.options.enableLoading) {\n      this.element.setAttribute('aria-busy', 'false')\n    }\n  }\n\n  /**\n   * Handle click events\n   */\n  handleClick(event) {\n    if (this.isLoading || this.element.disabled) {\n      event.preventDefault()\n      return\n    }\n\n    // Create ripple effect\n    if (this.options.enableRipple) {\n      this.createRipple(event)\n    }\n\n    // Dispatch custom event\n    this.dispatchEvent('glass-button-click', { originalEvent: event })\n  }\n\n  /**\n   * Handle mouse enter\n   */\n  handleMouseEnter(event) {\n    this.element.classList.add('glass-btn-hover')\n    this.dispatchEvent('glass-button-hover', { state: 'enter', originalEvent: event })\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave(event) {\n    this.element.classList.remove('glass-btn-hover')\n    this.dispatchEvent('glass-button-hover', { state: 'leave', originalEvent: event })\n  }\n\n  /**\n   * Handle focus\n   */\n  handleFocus(event) {\n    this.element.classList.add('glass-btn-focus')\n    this.dispatchEvent('glass-button-focus', { state: 'focus', originalEvent: event })\n  }\n\n  /**\n   * Handle blur\n   */\n  handleBlur(event) {\n    this.element.classList.remove('glass-btn-focus')\n    this.dispatchEvent('glass-button-focus', { state: 'blur', originalEvent: event })\n  }\n\n  /**\n   * Handle keyboard events\n   */\n  handleKeyDown(event) {\n    // Activate button with Enter or Space\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault()\n      this.element.click()\n    }\n  }\n\n  /**\n   * Create ripple effect\n   */\n  createRipple(event) {\n    const rect = this.element.getBoundingClientRect()\n    const x = event.clientX - rect.left\n    const y = event.clientY - rect.top\n\n    const ripple = document.createElement('div')\n    ripple.className = 'glass-btn-ripple'\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: 0;\n      height: 0;\n      border-radius: 50%;\n      background: ${this.options.rippleColor};\n      transform: translate(-50%, -50%);\n      animation: glass-btn-ripple 0.6s ease-out;\n      pointer-events: none;\n      z-index: 1;\n    `\n\n    // Ensure button has relative positioning\n    if (getComputedStyle(this.element).position === 'static') {\n      this.element.style.position = 'relative'\n    }\n\n    this.element.appendChild(ripple)\n    this.ripples.add(ripple)\n\n    // Remove ripple after animation\n    setTimeout(() => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n      this.ripples.delete(ripple)\n    }, 600)\n  }\n\n  /**\n   * Set loading state\n   */\n  setLoading(loading = true) {\n    this.isLoading = loading\n    \n    if (loading) {\n      this.element.classList.add('glass-btn-loading')\n      this.element.setAttribute('aria-busy', 'true')\n      this.element.disabled = true\n    } else {\n      this.element.classList.remove('glass-btn-loading')\n      this.element.setAttribute('aria-busy', 'false')\n      this.element.disabled = false\n    }\n\n    this.updateState()\n    this.dispatchEvent('glass-button-loading', { loading })\n  }\n\n  /**\n   * Set disabled state\n   */\n  setDisabled(disabled = true) {\n    this.element.disabled = disabled\n    this.element.classList.toggle('glass-btn-disabled', disabled)\n    this.updateState()\n    this.dispatchEvent('glass-button-disabled', { disabled })\n  }\n\n  /**\n   * Set button variant\n   */\n  setVariant(variant) {\n    // Remove existing variant classes\n    const variantClasses = [\n      'glass-btn-primary',\n      'glass-btn-secondary',\n      'glass-btn-success',\n      'glass-btn-warning',\n      'glass-btn-danger',\n      'glass-btn-ghost'\n    ]\n    \n    this.element.classList.remove(...variantClasses)\n    \n    // Add new variant class\n    if (variant && variant !== 'default') {\n      this.element.classList.add(`glass-btn-${variant}`)\n    }\n\n    this.dispatchEvent('glass-button-variant', { variant })\n  }\n\n  /**\n   * Set button size\n   */\n  setSize(size) {\n    // Remove existing size classes\n    const sizeClasses = ['glass-btn-xs', 'glass-btn-sm', 'glass-btn-lg', 'glass-btn-xl']\n    this.element.classList.remove(...sizeClasses)\n    \n    // Add new size class\n    if (size && size !== 'default') {\n      this.element.classList.add(`glass-btn-${size}`)\n    }\n\n    this.dispatchEvent('glass-button-size', { size })\n  }\n\n  /**\n   * Update button state\n   */\n  updateState() {\n    const state = {\n      loading: this.isLoading,\n      disabled: this.element.disabled,\n      focused: this.element.classList.contains('glass-btn-focus'),\n      hovered: this.element.classList.contains('glass-btn-hover')\n    }\n\n    this.element.setAttribute('data-state', JSON.stringify(state))\n  }\n\n  /**\n   * Dispatch custom event\n   */\n  dispatchEvent(eventName, detail = {}) {\n    const event = new CustomEvent(eventName, {\n      detail: {\n        button: this,\n        element: this.element,\n        ...detail\n      },\n      bubbles: true,\n      cancelable: true\n    })\n\n    this.element.dispatchEvent(event)\n  }\n\n  /**\n   * Add animation\n   */\n  addAnimation(animationType, duration = 1000) {\n    const animationClass = `glass-btn-animate-${animationType}`\n    this.element.classList.add(animationClass)\n\n    setTimeout(() => {\n      this.element.classList.remove(animationClass)\n    }, duration)\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Get button state\n   */\n  getState() {\n    return {\n      loading: this.isLoading,\n      disabled: this.element.disabled,\n      variant: this.getVariant(),\n      size: this.getSize()\n    }\n  }\n\n  /**\n   * Get current variant\n   */\n  getVariant() {\n    const variants = ['primary', 'secondary', 'success', 'warning', 'danger', 'ghost']\n    return variants.find(variant => this.element.classList.contains(`glass-btn-${variant}`)) || 'default'\n  }\n\n  /**\n   * Get current size\n   */\n  getSize() {\n    const sizes = ['xs', 'sm', 'lg', 'xl']\n    return sizes.find(size => this.element.classList.contains(`glass-btn-${size}`)) || 'default'\n  }\n\n  /**\n   * Destroy button component\n   */\n  destroy() {\n    // Remove all ripples\n    this.ripples.forEach(ripple => {\n      if (ripple.parentNode) {\n        ripple.parentNode.removeChild(ripple)\n      }\n    })\n    this.ripples.clear()\n\n    // Remove event listeners (they'll be garbage collected with the element)\n    \n    // Remove component identifier\n    this.element.removeAttribute('data-glass-button')\n    \n    // Remove state classes\n    this.element.classList.remove('glass-btn-hover', 'glass-btn-focus', 'glass-btn-loading', 'glass-btn-disabled')\n  }\n}\n", "/**\n * Glass Card Component\n * Enhanced card with glass effects and interactions\n */\n\nexport class GlassCard {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      enableHover: true,\n      enableClick: true,\n      enableLoading: true,\n      hoverEffect: 'lift', // 'lift', 'glow', 'scale'\n      ...options\n    }\n\n    this.isLoading = false\n    \n    this.init()\n  }\n\n  /**\n   * Initialize card component\n   */\n  init() {\n    this.setupElement()\n    this.bindEvents()\n    this.setupAccessibility()\n  }\n\n  /**\n   * Setup card element\n   */\n  setupElement() {\n    // Add base glass card class if not present\n    if (!this.element.classList.contains('glass-card')) {\n      this.element.classList.add('glass-card')\n    }\n\n    // Add component identifier\n    this.element.setAttribute('data-glass-card', 'initialized')\n\n    // Setup initial state\n    this.updateState()\n  }\n\n  /**\n   * Bind event listeners\n   */\n  bindEvents() {\n    // Click events for interactive cards\n    if (this.options.enableClick && this.element.classList.contains('glass-card-interactive')) {\n      this.element.addEventListener('click', this.handleClick.bind(this))\n    }\n\n    // Mouse events for hover effects\n    if (this.options.enableHover) {\n      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))\n      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))\n    }\n\n    // Focus events for accessibility\n    this.element.addEventListener('focus', this.handleFocus.bind(this))\n    this.element.addEventListener('blur', this.handleBlur.bind(this))\n\n    // Keyboard events for interactive cards\n    if (this.element.classList.contains('glass-card-interactive')) {\n      this.element.addEventListener('keydown', this.handleKeyDown.bind(this))\n    }\n  }\n\n  /**\n   * Setup accessibility features\n   */\n  setupAccessibility() {\n    // Make interactive cards focusable and add role\n    if (this.element.classList.contains('glass-card-interactive')) {\n      if (!this.element.hasAttribute('tabindex')) {\n        this.element.setAttribute('tabindex', '0')\n      }\n      if (!this.element.getAttribute('role')) {\n        this.element.setAttribute('role', 'button')\n      }\n    }\n\n    // Add ARIA attributes for loading state\n    if (this.options.enableLoading) {\n      this.element.setAttribute('aria-busy', 'false')\n    }\n  }\n\n  /**\n   * Handle click events\n   */\n  handleClick(event) {\n    if (this.isLoading) {\n      event.preventDefault()\n      return\n    }\n\n    // Dispatch custom event\n    this.dispatchEvent('glass-card-click', { originalEvent: event })\n  }\n\n  /**\n   * Handle mouse enter\n   */\n  handleMouseEnter(event) {\n    this.element.classList.add('glass-card-hover')\n    \n    // Apply hover effect based on options\n    switch (this.options.hoverEffect) {\n      case 'glow':\n        this.element.classList.add('glass-card-glow')\n        break\n      case 'scale':\n        this.element.classList.add('glass-card-scale')\n        break\n      default:\n        // 'lift' is handled by CSS\n        break\n    }\n\n    this.dispatchEvent('glass-card-hover', { state: 'enter', originalEvent: event })\n  }\n\n  /**\n   * Handle mouse leave\n   */\n  handleMouseLeave(event) {\n    this.element.classList.remove('glass-card-hover', 'glass-card-glow', 'glass-card-scale')\n    this.dispatchEvent('glass-card-hover', { state: 'leave', originalEvent: event })\n  }\n\n  /**\n   * Handle focus\n   */\n  handleFocus(event) {\n    this.element.classList.add('glass-card-focus')\n    this.dispatchEvent('glass-card-focus', { state: 'focus', originalEvent: event })\n  }\n\n  /**\n   * Handle blur\n   */\n  handleBlur(event) {\n    this.element.classList.remove('glass-card-focus')\n    this.dispatchEvent('glass-card-focus', { state: 'blur', originalEvent: event })\n  }\n\n  /**\n   * Handle keyboard events\n   */\n  handleKeyDown(event) {\n    // Activate card with Enter or Space\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault()\n      this.element.click()\n    }\n  }\n\n  /**\n   * Set loading state\n   */\n  setLoading(loading = true) {\n    this.isLoading = loading\n    \n    if (loading) {\n      this.element.classList.add('glass-card-loading')\n      this.element.setAttribute('aria-busy', 'true')\n    } else {\n      this.element.classList.remove('glass-card-loading')\n      this.element.setAttribute('aria-busy', 'false')\n    }\n\n    this.updateState()\n    this.dispatchEvent('glass-card-loading', { loading })\n  }\n\n  /**\n   * Set card variant\n   */\n  setVariant(variant) {\n    // Remove existing variant classes\n    const variantClasses = [\n      'glass-card-elevated',\n      'glass-card-subtle',\n      'glass-card-outlined',\n      'glass-card-filled'\n    ]\n    \n    this.element.classList.remove(...variantClasses)\n    \n    // Add new variant class\n    if (variant && variant !== 'default') {\n      this.element.classList.add(`glass-card-${variant}`)\n    }\n\n    this.dispatchEvent('glass-card-variant', { variant })\n  }\n\n  /**\n   * Set card size\n   */\n  setSize(size) {\n    // Remove existing size classes\n    const sizeClasses = ['glass-card-sm', 'glass-card-lg']\n    this.element.classList.remove(...sizeClasses)\n    \n    // Add new size class\n    if (size && size !== 'default') {\n      this.element.classList.add(`glass-card-${size}`)\n    }\n\n    this.dispatchEvent('glass-card-size', { size })\n  }\n\n  /**\n   * Add badge to card\n   */\n  addBadge(text, options = {}) {\n    const badgeOptions = {\n      position: 'top-right',\n      variant: 'default',\n      ...options\n    }\n\n    // Remove existing badge\n    this.removeBadge()\n\n    const badge = document.createElement('div')\n    badge.className = `glass-card-badge glass-card-badge-${badgeOptions.position}`\n    badge.textContent = text\n    \n    if (badgeOptions.variant !== 'default') {\n      badge.classList.add(`glass-card-badge-${badgeOptions.variant}`)\n    }\n\n    this.element.appendChild(badge)\n    this.dispatchEvent('glass-card-badge-added', { text, options: badgeOptions })\n  }\n\n  /**\n   * Remove badge from card\n   */\n  removeBadge() {\n    const existingBadge = this.element.querySelector('.glass-card-badge')\n    if (existingBadge) {\n      existingBadge.remove()\n      this.dispatchEvent('glass-card-badge-removed')\n    }\n  }\n\n  /**\n   * Update card content\n   */\n  updateContent(content) {\n    const body = this.element.querySelector('.glass-card-body')\n    if (body) {\n      if (typeof content === 'string') {\n        body.innerHTML = content\n      } else if (content instanceof HTMLElement) {\n        body.innerHTML = ''\n        body.appendChild(content)\n      }\n      this.dispatchEvent('glass-card-content-updated', { content })\n    }\n  }\n\n  /**\n   * Update card state\n   */\n  updateState() {\n    const state = {\n      loading: this.isLoading,\n      interactive: this.element.classList.contains('glass-card-interactive'),\n      focused: this.element.classList.contains('glass-card-focus'),\n      hovered: this.element.classList.contains('glass-card-hover')\n    }\n\n    this.element.setAttribute('data-state', JSON.stringify(state))\n  }\n\n  /**\n   * Dispatch custom event\n   */\n  dispatchEvent(eventName, detail = {}) {\n    const event = new CustomEvent(eventName, {\n      detail: {\n        card: this,\n        element: this.element,\n        ...detail\n      },\n      bubbles: true,\n      cancelable: true\n    })\n\n    this.element.dispatchEvent(event)\n  }\n\n  /**\n   * Add animation\n   */\n  addAnimation(animationType, duration = 1000) {\n    const animationClass = `glass-card-animate-${animationType}`\n    this.element.classList.add(animationClass)\n\n    setTimeout(() => {\n      this.element.classList.remove(animationClass)\n    }, duration)\n  }\n\n  /**\n   * Update options\n   */\n  updateOptions(newOptions) {\n    this.options = { ...this.options, ...newOptions }\n  }\n\n  /**\n   * Get card state\n   */\n  getState() {\n    return {\n      loading: this.isLoading,\n      interactive: this.element.classList.contains('glass-card-interactive'),\n      variant: this.getVariant(),\n      size: this.getSize()\n    }\n  }\n\n  /**\n   * Get current variant\n   */\n  getVariant() {\n    const variants = ['elevated', 'subtle', 'outlined', 'filled']\n    return variants.find(variant => this.element.classList.contains(`glass-card-${variant}`)) || 'default'\n  }\n\n  /**\n   * Get current size\n   */\n  getSize() {\n    const sizes = ['sm', 'lg']\n    return sizes.find(size => this.element.classList.contains(`glass-card-${size}`)) || 'default'\n  }\n\n  /**\n   * Destroy card component\n   */\n  destroy() {\n    // Remove component identifier\n    this.element.removeAttribute('data-glass-card')\n    \n    // Remove state classes\n    this.element.classList.remove('glass-card-hover', 'glass-card-focus', 'glass-card-loading', 'glass-card-glow', 'glass-card-scale')\n    \n    // Remove badge if present\n    this.removeBadge()\n  }\n}\n", "/**\n * Glass Modal Component\n * Enhanced modal with glass effects\n */\n\nexport class GlassModal {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      backdrop: true,\n      keyboard: true,\n      focus: true,\n      ...options\n    }\n\n    this.isOpen = false\n    this.init()\n  }\n\n  init() {\n    this.element.setAttribute('data-glass-modal', 'initialized')\n  }\n\n  open() {\n    this.isOpen = true\n    this.element.classList.add('glass-modal-open')\n  }\n\n  close() {\n    this.isOpen = false\n    this.element.classList.remove('glass-modal-open')\n  }\n\n  destroy() {\n    this.element.removeAttribute('data-glass-modal')\n  }\n}\n", "/**\n * Glass Navigation Component\n * Enhanced navigation with glass effects\n */\n\nexport class GlassNavigation {\n  constructor(element, options = {}) {\n    this.element = element\n    this.options = {\n      sticky: false,\n      collapsible: true,\n      ...options\n    }\n\n    this.init()\n  }\n\n  init() {\n    this.element.setAttribute('data-glass-nav', 'initialized')\n  }\n\n  destroy() {\n    this.element.removeAttribute('data-glass-nav')\n  }\n}\n", "/**\n * Theme Manager Utility\n * Manages light/dark theme switching and system preferences\n */\n\nexport class ThemeManager {\n  constructor(initialTheme = 'auto') {\n    this.currentTheme = initialTheme\n    this.systemTheme = this.getSystemTheme()\n    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    this.listeners = new Set()\n    \n    this.storageKey = 'liquid-glass-ui-theme'\n  }\n\n  /**\n   * Initialize theme manager\n   */\n  init() {\n    // Load saved theme preference\n    const savedTheme = this.loadTheme()\n    if (savedTheme) {\n      this.currentTheme = savedTheme\n    }\n\n    // Apply initial theme\n    this.applyTheme()\n\n    // Listen for system theme changes\n    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange.bind(this))\n\n    // Add theme toggle functionality\n    this.initThemeToggles()\n  }\n\n  /**\n   * Get system theme preference\n   */\n  getSystemTheme() {\n    return this.mediaQuery.matches ? 'dark' : 'light'\n  }\n\n  /**\n   * Get effective theme (resolves 'auto' to actual theme)\n   */\n  getEffectiveTheme() {\n    if (this.currentTheme === 'auto') {\n      return this.systemTheme\n    }\n    return this.currentTheme\n  }\n\n  /**\n   * Set theme\n   */\n  setTheme(theme) {\n    if (!['light', 'dark', 'auto'].includes(theme)) {\n      console.warn(`Invalid theme: ${theme}. Using 'auto' instead.`)\n      theme = 'auto'\n    }\n\n    this.currentTheme = theme\n    this.applyTheme()\n    this.saveTheme()\n    this.notifyListeners()\n  }\n\n  /**\n   * Toggle between light and dark themes\n   */\n  toggleTheme() {\n    const effectiveTheme = this.getEffectiveTheme()\n    const newTheme = effectiveTheme === 'light' ? 'dark' : 'light'\n    this.setTheme(newTheme)\n  }\n\n  /**\n   * Apply theme to document\n   */\n  applyTheme() {\n    const effectiveTheme = this.getEffectiveTheme()\n    \n    // Remove existing theme classes\n    document.documentElement.classList.remove('theme-light', 'theme-dark')\n    document.documentElement.removeAttribute('data-theme')\n    \n    // Add new theme class and attribute\n    document.documentElement.classList.add(`theme-${effectiveTheme}`)\n    document.documentElement.setAttribute('data-theme', effectiveTheme)\n\n    // Update meta theme-color for mobile browsers\n    this.updateMetaThemeColor(effectiveTheme)\n\n    // Dispatch theme change event\n    this.dispatchThemeEvent(effectiveTheme)\n  }\n\n  /**\n   * Update meta theme-color for mobile browsers\n   */\n  updateMetaThemeColor(theme) {\n    let metaThemeColor = document.querySelector('meta[name=\"theme-color\"]')\n    \n    if (!metaThemeColor) {\n      metaThemeColor = document.createElement('meta')\n      metaThemeColor.name = 'theme-color'\n      document.head.appendChild(metaThemeColor)\n    }\n\n    const colors = {\n      light: '#ffffff',\n      dark: '#000000'\n    }\n\n    metaThemeColor.content = colors[theme] || colors.light\n  }\n\n  /**\n   * Dispatch custom theme change event\n   */\n  dispatchThemeEvent(theme) {\n    const event = new CustomEvent('themechange', {\n      detail: {\n        theme,\n        previousTheme: this.previousTheme,\n        isSystemTheme: this.currentTheme === 'auto'\n      }\n    })\n    \n    this.previousTheme = theme\n    document.dispatchEvent(event)\n  }\n\n  /**\n   * Handle system theme changes\n   */\n  handleSystemThemeChange(event) {\n    this.systemTheme = event.matches ? 'dark' : 'light'\n    \n    if (this.currentTheme === 'auto') {\n      this.applyTheme()\n      this.notifyListeners()\n    }\n  }\n\n  /**\n   * Initialize theme toggle buttons\n   */\n  initThemeToggles() {\n    const toggles = document.querySelectorAll('[data-theme-toggle]')\n    \n    toggles.forEach(toggle => {\n      toggle.addEventListener('click', () => {\n        this.toggleTheme()\n      })\n    })\n\n    // Initialize theme selectors\n    const selectors = document.querySelectorAll('[data-theme-selector]')\n    \n    selectors.forEach(selector => {\n      selector.addEventListener('change', (event) => {\n        this.setTheme(event.target.value)\n      })\n      \n      // Set initial value\n      selector.value = this.currentTheme\n    })\n  }\n\n  /**\n   * Add theme change listener\n   */\n  addListener(callback) {\n    this.listeners.add(callback)\n    \n    return () => {\n      this.listeners.delete(callback)\n    }\n  }\n\n  /**\n   * Notify all listeners of theme change\n   */\n  notifyListeners() {\n    const effectiveTheme = this.getEffectiveTheme()\n    \n    this.listeners.forEach(callback => {\n      try {\n        callback(effectiveTheme, this.currentTheme)\n      } catch (error) {\n        console.error('Error in theme change listener:', error)\n      }\n    })\n  }\n\n  /**\n   * Save theme preference to localStorage\n   */\n  saveTheme() {\n    try {\n      localStorage.setItem(this.storageKey, this.currentTheme)\n    } catch (error) {\n      console.warn('Could not save theme preference:', error)\n    }\n  }\n\n  /**\n   * Load theme preference from localStorage\n   */\n  loadTheme() {\n    try {\n      return localStorage.getItem(this.storageKey)\n    } catch (error) {\n      console.warn('Could not load theme preference:', error)\n      return null\n    }\n  }\n\n  /**\n   * Get theme-specific CSS custom property value\n   */\n  getThemeValue(property) {\n    return getComputedStyle(document.documentElement).getPropertyValue(property)\n  }\n\n  /**\n   * Set theme-specific CSS custom property\n   */\n  setThemeValue(property, value) {\n    document.documentElement.style.setProperty(property, value)\n  }\n\n  /**\n   * Create theme-aware color scheme\n   */\n  createColorScheme(lightColor, darkColor) {\n    const effectiveTheme = this.getEffectiveTheme()\n    return effectiveTheme === 'dark' ? darkColor : lightColor\n  }\n\n  /**\n   * Destroy theme manager\n   */\n  destroy() {\n    this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange.bind(this))\n    this.listeners.clear()\n  }\n\n  /**\n   * Get current theme info\n   */\n  getThemeInfo() {\n    return {\n      current: this.currentTheme,\n      effective: this.getEffectiveTheme(),\n      system: this.systemTheme,\n      isAuto: this.currentTheme === 'auto'\n    }\n  }\n}\n", "/**\n * Device Detector Utility\n * Detects device capabilities and features for optimal glass effects\n */\n\nexport class DeviceDetector {\n  constructor() {\n    this.userAgent = navigator.userAgent.toLowerCase()\n    this.capabilities = this.detectCapabilities()\n  }\n\n  /**\n   * Check if device is mobile\n   */\n  isMobile() {\n    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is tablet\n   */\n  isTablet() {\n    return /ipad|android(?!.*mobile)/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is desktop\n   */\n  isDesktop() {\n    return !this.isMobile() && !this.isTablet()\n  }\n\n  /**\n   * Check if device is iOS\n   */\n  isIOS() {\n    return /iphone|ipad|ipod/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device is Android\n   */\n  isAndroid() {\n    return /android/i.test(this.userAgent)\n  }\n\n  /**\n   * Check if device supports touch\n   */\n  supportsTouch() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0\n  }\n\n  /**\n   * Check if device supports device orientation\n   */\n  supportsDeviceOrientation() {\n    return 'DeviceOrientationEvent' in window\n  }\n\n  /**\n   * Check if device supports device motion\n   */\n  supportsDeviceMotion() {\n    return 'DeviceMotionEvent' in window\n  }\n\n  /**\n   * Check if browser supports backdrop-filter\n   */\n  supportsBackdropFilter() {\n    return CSS.supports('backdrop-filter', 'blur(1px)') || \n           CSS.supports('-webkit-backdrop-filter', 'blur(1px)')\n  }\n\n  /**\n   * Check if user prefers reduced motion\n   */\n  prefersReducedMotion() {\n    return window.matchMedia('(prefers-reduced-motion: reduce)').matches\n  }\n\n  /**\n   * Get device pixel ratio\n   */\n  getPixelRatio() {\n    return window.devicePixelRatio || 1\n  }\n\n  /**\n   * Get viewport dimensions\n   */\n  getViewport() {\n    return {\n      width: window.innerWidth,\n      height: window.innerHeight,\n    }\n  }\n\n  /**\n   * Check if device has high refresh rate\n   */\n  hasHighRefreshRate() {\n    // Estimate based on requestAnimationFrame timing\n    return new Promise((resolve) => {\n      let frames = 0\n      const start = performance.now()\n      \n      const checkFrame = () => {\n        frames++\n        if (frames < 60) {\n          requestAnimationFrame(checkFrame)\n        } else {\n          const duration = performance.now() - start\n          const fps = Math.round(frames / (duration / 1000))\n          resolve(fps > 60)\n        }\n      }\n      \n      requestAnimationFrame(checkFrame)\n    })\n  }\n\n  /**\n   * Detect all device capabilities\n   */\n  detectCapabilities() {\n    return {\n      isMobile: this.isMobile(),\n      isTablet: this.isTablet(),\n      isDesktop: this.isDesktop(),\n      isIOS: this.isIOS(),\n      isAndroid: this.isAndroid(),\n      supportsTouch: this.supportsTouch(),\n      supportsDeviceOrientation: this.supportsDeviceOrientation(),\n      supportsDeviceMotion: this.supportsDeviceMotion(),\n      supportsBackdropFilter: this.supportsBackdropFilter(),\n      prefersReducedMotion: this.prefersReducedMotion(),\n      pixelRatio: this.getPixelRatio(),\n      viewport: this.getViewport(),\n    }\n  }\n\n  /**\n   * Get optimal glass effect settings based on device\n   */\n  getOptimalSettings() {\n    const settings = {\n      blurIntensity: 'medium',\n      animationDuration: 'normal',\n      enableCursorEffects: false,\n      enableMotionEffects: false,\n      enableParallax: false,\n      enableHighFrameRate: false,\n    }\n\n    // Desktop optimizations\n    if (this.isDesktop()) {\n      settings.enableCursorEffects = true\n      settings.blurIntensity = 'high'\n      settings.enableHighFrameRate = true\n    }\n\n    // Mobile optimizations\n    if (this.isMobile()) {\n      settings.enableMotionEffects = this.supportsDeviceOrientation()\n      settings.blurIntensity = 'low'\n      settings.animationDuration = 'fast'\n    }\n\n    // High-end device optimizations\n    if (this.getPixelRatio() > 2) {\n      settings.enableParallax = true\n    }\n\n    // Reduced motion preferences\n    if (this.prefersReducedMotion()) {\n      settings.animationDuration = 'none'\n      settings.enableMotionEffects = false\n      settings.enableParallax = false\n    }\n\n    // Backdrop filter fallback\n    if (!this.supportsBackdropFilter()) {\n      settings.blurIntensity = 'none'\n    }\n\n    return settings\n  }\n\n  /**\n   * Add device classes to document\n   */\n  addDeviceClasses() {\n    const classes = []\n    \n    if (this.isMobile()) classes.push('device-mobile')\n    if (this.isTablet()) classes.push('device-tablet')\n    if (this.isDesktop()) classes.push('device-desktop')\n    if (this.isIOS()) classes.push('device-ios')\n    if (this.isAndroid()) classes.push('device-android')\n    if (this.supportsTouch()) classes.push('supports-touch')\n    if (this.supportsBackdropFilter()) classes.push('supports-backdrop-filter')\n    if (this.prefersReducedMotion()) classes.push('prefers-reduced-motion')\n    \n    document.documentElement.classList.add(...classes)\n  }\n\n  /**\n   * Listen for viewport changes\n   */\n  onViewportChange(callback) {\n    let timeout\n    const handleResize = () => {\n      clearTimeout(timeout)\n      timeout = setTimeout(() => {\n        this.capabilities.viewport = this.getViewport()\n        callback(this.capabilities.viewport)\n      }, 100)\n    }\n\n    window.addEventListener('resize', handleResize)\n    window.addEventListener('orientationchange', handleResize)\n\n    return () => {\n      window.removeEventListener('resize', handleResize)\n      window.removeEventListener('orientationchange', handleResize)\n    }\n  }\n}\n", "/**\n * Liquid Glass UI - Main Entry Point\n * A comprehensive JavaScript/CSS UI framework that replicates Apple's liquid glass design aesthetic\n */\n\n// Import CSS\nimport './css/index.css'\n\n// Import core utilities\nimport { GlassEffects } from './js/effects/glass-effects.js'\nimport { CursorEffects } from './js/effects/cursor-effects.js'\nimport { MotionEffects } from './js/effects/motion-effects.js'\n\n// Import components\nimport { GlassButton } from './js/components/button.js'\nimport { GlassCard } from './js/components/card.js'\nimport { GlassModal } from './js/components/modal.js'\nimport { GlassNavigation } from './js/components/navigation.js'\n\n// Import utilities\nimport { ThemeManager } from './js/utils/theme-manager.js'\nimport { DeviceDetector } from './js/utils/device-detector.js'\n\n/**\n * Main LiquidGlassUI class\n */\nclass LiquidGlassUI {\n  constructor(options = {}) {\n    this.options = {\n      enableCursorEffects: true,\n      enableMotionEffects: true,\n      enableAnimations: true,\n      theme: 'auto', // 'light', 'dark', 'auto'\n      ...options,\n    }\n\n    this.effects = new GlassEffects(this.options)\n    this.themeManager = new ThemeManager(this.options.theme)\n    this.deviceDetector = new DeviceDetector()\n\n    this.init()\n  }\n\n  /**\n   * Initialize the framework\n   */\n  init() {\n    // Initialize theme\n    this.themeManager.init()\n\n    // Initialize effects based on device capabilities\n    if (this.deviceDetector.isDesktop() && this.options.enableCursorEffects) {\n      this.cursorEffects = new CursorEffects()\n      this.cursorEffects.init()\n    }\n\n    if (this.deviceDetector.isMobile() && this.options.enableMotionEffects) {\n      this.motionEffects = new MotionEffects()\n      this.motionEffects.init()\n    }\n\n    // Initialize components\n    this.initComponents()\n\n    // Mark as initialized\n    document.documentElement.setAttribute('data-liquid-glass-ui', 'initialized')\n  }\n\n  /**\n   * Initialize all components\n   */\n  initComponents() {\n    // Auto-initialize components with data attributes\n    this.initButtons()\n    this.initCards()\n    this.initModals()\n    this.initNavigation()\n  }\n\n  /**\n   * Initialize button components\n   */\n  initButtons() {\n    const buttons = document.querySelectorAll('[data-glass-button]')\n    buttons.forEach(button => new GlassButton(button))\n  }\n\n  /**\n   * Initialize card components\n   */\n  initCards() {\n    const cards = document.querySelectorAll('[data-glass-card]')\n    cards.forEach(card => new GlassCard(card))\n  }\n\n  /**\n   * Initialize modal components\n   */\n  initModals() {\n    const modals = document.querySelectorAll('[data-glass-modal]')\n    modals.forEach(modal => new GlassModal(modal))\n  }\n\n  /**\n   * Initialize navigation components\n   */\n  initNavigation() {\n    const navs = document.querySelectorAll('[data-glass-nav]')\n    navs.forEach(nav => new GlassNavigation(nav))\n  }\n\n  /**\n   * Update theme\n   */\n  setTheme(theme) {\n    this.themeManager.setTheme(theme)\n  }\n\n  /**\n   * Destroy the framework instance\n   */\n  destroy() {\n    if (this.cursorEffects) this.cursorEffects.destroy()\n    if (this.motionEffects) this.motionEffects.destroy()\n    this.themeManager.destroy()\n    \n    document.documentElement.removeAttribute('data-liquid-glass-ui')\n  }\n}\n\n// Export for module usage\nexport default LiquidGlassUI\n\n// Export individual components and utilities\nexport {\n  GlassEffects,\n  CursorEffects,\n  MotionEffects,\n  GlassButton,\n  GlassCard,\n  GlassModal,\n  GlassNavigation,\n  ThemeManager,\n  DeviceDetector,\n}\n\n// Auto-initialize if not in module environment\nif (typeof window !== 'undefined' && !window.LiquidGlassUI) {\n  window.LiquidGlassUI = LiquidGlassUI\n  \n  // Auto-initialize on DOM ready\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', () => {\n      if (!document.querySelector('[data-no-auto-init]')) {\n        new LiquidGlassUI()\n      }\n    })\n  } else {\n    if (!document.querySelector('[data-no-auto-init]')) {\n      new LiquidGlassUI()\n    }\n  }\n}\n"], "names": ["GlassEffects", "options", "deviceMemory", "hardwareConcurrency", "connection", "performanceScore", "entries", "entry", "element", "effectData", "resizeTimeout", "selector", "baseOptions", "rect", "event", "handler", "isHovering", "isFocused", "x", "y", "ripple", "animationType", "cleanup", "CursorEffects", "animate", "centerX", "centerY", "deltaX", "deltaY", "distance", "normalizedDistance", "intensity", "relativeX", "relativeY", "shadowIntensity", "shadowX", "shadowY", "<PERSON><PERSON><PERSON><PERSON>", "shadowOpacity", "shadow", "lightIntensity", "gradientX", "gradientY", "gradient", "handleClick", "newOptions", "MotionEffects", "permission", "error", "beta", "gamma", "normalizedX", "normalizedY", "tiltX", "tiltY", "parallaxX", "parallaxY", "shakeClass", "value", "min", "max", "GlassButton", "loading", "disabled", "variant", "variantClasses", "size", "sizeClasses", "state", "eventName", "detail", "duration", "animationClass", "GlassCard", "text", "badgeOptions", "badge", "existingBadge", "content", "body", "GlassModal", "GlassNavigation", "ThemeManager", "initialTheme", "savedTheme", "theme", "newTheme", "effectiveTheme", "metaThemeColor", "colors", "toggle", "callback", "property", "lightColor", "darkColor", "DeviceDetector", "resolve", "frames", "start", "checkFrame", "fps", "settings", "classes", "timeout", "handleResize", "LiquidGlassUI", "button", "card", "modal", "nav"], "mappings": "AAKO,MAAMA,EAAa;AAAA,EACxB,YAAYC,IAAU,IAAI;AACxB,SAAK,UAAU;AAAA,MACb,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,iBAAiB;AAAA;AAAA,MACjB,GAAGA;AAAA,IACT,GAEI,KAAK,WAAW,oBAAI,IAAG,GACvB,KAAK,YAAY,oBAAI,IAAG,GACxB,KAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,IAAI,KAAK,kBAET,KAAK,8BAA6B,GAClC,KAAK,0BAAyB,GAC9B,KAAK,iBAAgB,GACrB,KAAK,gBAAe,GAEpB,KAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC;AAC9B,QAAI,KAAK,QAAQ,oBAAoB,OAAQ;AAE7C,UAAMC,IAAe,UAAU,gBAAgB,GACzCC,IAAsB,UAAU,uBAAuB,GACvDC,IAAa,UAAU;AAE7B,QAAIC,IAAmB;AAGvB,IAAIH,KAAgB,IAAGG,KAAoB,IAClCH,KAAgB,IAAGG,KAAoB,IACvCH,KAAgB,MAAGG,KAAoB,IAG5CF,KAAuB,IAAGE,KAAoB,IACzCF,KAAuB,IAAGE,KAAoB,IAC9CF,KAAuB,MAAGE,KAAoB,IAGnDD,IACEA,EAAW,kBAAkB,OAAMC,KAAoB,IAClDD,EAAW,kBAAkB,SAAMC,KAAoB,KAEhEA,KAAoB,GAIlBA,KAAoB,IAAG,KAAK,QAAQ,kBAAkB,SACjDA,KAAoB,IAAG,KAAK,QAAQ,kBAAkB,WAC1D,KAAK,QAAQ,kBAAkB,OAGpC,SAAS,gBAAgB,UAAU,IAAI,eAAe,KAAK,QAAQ,eAAe,EAAE;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,IAAM,0BAA0B,WAEhC,KAAK,uBAAuB,IAAI;AAAA,MAC9B,CAACC,MAAY;AACX,QAAAA,EAAQ,QAAQ,CAAAC,MAAS;AACvB,gBAAMC,IAAUD,EAAM,QAChBE,IAAa,KAAK,SAAS,IAAID,CAAO;AAE5C,UAAKC,MAEDF,EAAM,iBACR,KAAK,gBAAgBC,GAASC,CAAU,IAExC,KAAK,kBAAkBD,GAASC,CAAU;AAAA,QAE9C,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,YAAY;AAAA,QACZ,WAAW;AAAA,MACnB;AAAA,IACA;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAEjB,aAAS,iBAAiB,oBAAoB,MAAM;AAClD,MAAI,SAAS,SACX,KAAK,gBAAe,IAEpB,KAAK,iBAAgB;AAAA,IAEzB,CAAC;AAGD,QAAIC;AACJ,WAAO,iBAAiB,UAAU,MAAM;AACtC,mBAAaA,CAAa,GAC1BA,IAAgB,WAAW,MAAM;AAC/B,aAAK,kBAAiB;AAAA,MACxB,GAAG,GAAG;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAWhB,IAVkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,EAEc,QAAQ,CAAAC,MAAY;AAC5B,eAAS,iBAAiBA,CAAQ,EAAE,QAAQ,CAAAH,MAAW;AACrD,aAAK,WAAWA,CAAO;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWA,GAASP,IAAU,IAAI;AAChC,QAAI,KAAK,SAAS,IAAIO,CAAO,EAAG;AAEhC,UAAMC,IAAa;AAAA,MACjB,SAAAD;AAAA,MACA,SAAS,EAAE,GAAG,KAAK,kBAAiB,GAAI,GAAGP,EAAO;AAAA,MAClD,UAAU;AAAA,MACV,YAAY,oBAAI,IAAG;AAAA,MACnB,WAAW,oBAAI,IAAG;AAAA,IACxB;AAEI,SAAK,SAAS,IAAIO,GAASC,CAAU,GAGjC,KAAK,uBACP,KAAK,qBAAqB,QAAQD,CAAO,IAEzC,KAAK,gBAAgBA,GAASC,CAAU,GAI1C,KAAK,kBAAkBD,GAASC,CAAU;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,UAAMG,IAAc;AAAA,MAClB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB,KAAK,QAAQ;AAAA,MAC/B,oBAAoB,KAAK,QAAQ;AAAA,IACvC;AAEI,YAAQ,KAAK,QAAQ,iBAAe;AAAA,MAClC,KAAK;AACH,eAAO;AAAA,UACL,GAAGA;AAAA,UACH,YAAY;AAAA,UACZ,kBAAkB;AAAA,UAClB,SAAS;AAAA,QACnB;AAAA,MACM,KAAK;AACH,eAAO;AAAA,UACL,GAAGA;AAAA,UACH,SAAS;AAAA,QACnB;AAAA,MACM,KAAK;AACH,eAAO;AAAA,UACL,GAAGA;AAAA,UACH,SAAS;AAAA,QACnB;AAAA,MACM;AACE,eAAOA;AAAA,IACf;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBJ,GAASC,GAAY;AACrC,UAAM,EAAE,SAAAR,EAAO,IAAKQ;AAGpB,IAAAD,EAAQ,UAAU,IAAI,eAAe,GAGrCA,EAAQ,UAAU,IAAI,qBAAqB,KAAK,QAAQ,eAAe,EAAE,GAGzE,KAAK,wBAAwBA,GAASC,CAAU,GAG5CR,EAAQ,sBACV,KAAK,wBAAwBO,GAASC,CAAU;AAAA,EAEpD;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwBD,GAASC,GAAY;AAC3C,UAAM,EAAE,SAAAR,EAAO,IAAKQ,GACdI,IAAOL,EAAQ,sBAAqB;AAG1C,IAAAA,EAAQ,MAAM,YAAY,mBAAmB,GAAGK,EAAK,KAAK,IAAI,GAC9DL,EAAQ,MAAM,YAAY,oBAAoB,GAAGK,EAAK,MAAM,IAAI,GAGhEL,EAAQ,MAAM,YAAY,cAAc,GAAGP,EAAQ,WAAW,CAAC,IAAI,GACnEO,EAAQ,MAAM,YAAY,uBAAuBP,EAAQ,mBAAmB,MAAM,GAAG;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwBO,GAASC,GAAY;AAS3C,WAAO,QARW;AAAA,MAChB,YAAY,MAAM,KAAK,mBAAmBD,GAASC,GAAY,EAAI;AAAA,MACnE,YAAY,MAAM,KAAK,mBAAmBD,GAASC,GAAY,EAAK;AAAA,MACpE,OAAO,MAAM,KAAK,mBAAmBD,GAASC,GAAY,EAAI;AAAA,MAC9D,MAAM,MAAM,KAAK,mBAAmBD,GAASC,GAAY,EAAK;AAAA,MAC9D,OAAO,CAACK,MAAU,KAAK,mBAAmBN,GAASC,GAAYK,CAAK;AAAA,IAC1E,CAE4B,EAAE,QAAQ,CAAC,CAACA,GAAOC,CAAO,MAAM;AACtD,MAAAP,EAAQ,iBAAiBM,GAAOC,CAAO,GACvCN,EAAW,UAAU,IAAI,MAAM;AAC7B,QAAAD,EAAQ,oBAAoBM,GAAOC,CAAO;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmBP,GAASC,GAAYO,GAAY;AAClD,IAAKP,EAAW,QAAQ,uBAExBD,EAAQ,UAAU,OAAO,eAAeQ,CAAU,GAE9CA,IACF,KAAK,aAAaR,GAAS,UAAU,IAErC,KAAK,aAAaA,GAAS,WAAW;AAAA,EAE1C;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmBA,GAASC,GAAYQ,GAAW;AACjD,IAAKR,EAAW,QAAQ,sBAExBD,EAAQ,UAAU,OAAO,eAAeS,CAAS;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmBT,GAASC,GAAYK,GAAO;AAC7C,IAAKL,EAAW,QAAQ,uBAExB,KAAK,mBAAmBD,GAASM,CAAK,GACtC,KAAK,aAAaN,GAAS,OAAO;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmBA,GAASM,GAAO;AACjC,UAAMD,IAAOL,EAAQ,sBAAqB,GACpCU,IAAIJ,EAAM,UAAUD,EAAK,MACzBM,IAAIL,EAAM,UAAUD,EAAK,KAEzBO,IAAS,SAAS,cAAc,KAAK;AAC3C,IAAAA,EAAO,YAAY,gBACnBA,EAAO,MAAM,UAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAWVX,EAAQ,MAAM,WAAW,YACzBA,EAAQ,YAAYY,CAAM,GAE1B,WAAW,MAAM;AACf,MAAIA,EAAO,cACTA,EAAO,WAAW,YAAYA,CAAM;AAAA,IAExC,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,aAAaZ,GAASa,GAAe;AACnC,UAAMZ,IAAa,KAAK,SAAS,IAAID,CAAO;AAC5C,IAAI,CAACC,KAAc,CAACA,EAAW,QAAQ,qBAEvCA,EAAW,WAAW,IAAIY,CAAa,GACvCb,EAAQ,UAAU,IAAI,iBAAiBa,CAAa,EAAE,GAEtD,WAAW,MAAM;AACf,MAAAZ,EAAW,WAAW,OAAOY,CAAa,GAC1Cb,EAAQ,UAAU,OAAO,iBAAiBa,CAAa,EAAE;AAAA,IAC3D,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgBb,GAASC,GAAY;AACnC,IAAIA,EAAW,aAEfA,EAAW,WAAW,IACtBD,EAAQ,UAAU,IAAI,cAAc;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBA,GAASC,GAAY;AACrC,IAAKA,EAAW,aAEhBA,EAAW,WAAW,IACtBD,EAAQ,UAAU,OAAO,cAAc;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAS;AACrB,UAAMC,IAAa,KAAK,SAAS,IAAID,CAAO;AAC5C,IAAKC,MAGD,KAAK,wBACP,KAAK,qBAAqB,UAAUD,CAAO,GAI7CC,EAAW,UAAU,QAAQ,CAAAa,MAAWA,EAAO,CAAE,GAGjDd,EAAQ,UAAU,OAAO,iBAAiB,gBAAgB,eAAe,aAAa,GAGtF,KAAK,SAAS,OAAOA,CAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,SAAS,QAAQ,CAACC,GAAYD,MAAY;AAC7C,WAAK,wBAAwBA,GAASC,CAAU;AAAA,IAClD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,SAAS,QAAQ,CAACA,GAAYD,MAAY;AAC7C,MAAAA,EAAQ,UAAU,IAAI,cAAc;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,SAAS,QAAQ,CAACC,GAAYD,MAAY;AAC7C,MAAAA,EAAQ,UAAU,OAAO,cAAc;AAAA,IACzC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,SAAK,SAAS,QAAQ,CAACC,GAAYD,MAAY;AAC7C,WAAK,cAAcA,CAAO;AAAA,IAC5B,CAAC,GAGG,KAAK,wBACP,KAAK,qBAAqB,WAAU,GAGtC,KAAK,SAAS,MAAK,GACnB,KAAK,UAAU,MAAK,GACpB,KAAK,gBAAgB;AAAA,EACvB;AACF;AC5aO,MAAMe,EAAc;AAAA,EACzB,YAAYtB,IAAU,IAAI;AACxB,SAAK,UAAU;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,GAAGA;AAAA,IACT,GAEI,KAAK,SAAS,EAAE,GAAG,GAAG,GAAG,EAAC,GAC1B,KAAK,eAAe,EAAE,GAAG,GAAG,GAAG,EAAC,GAChC,KAAK,WAAW,oBAAI,IAAG,GACvB,KAAK,WAAW,IAChB,KAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,IAAI,KAAK,aAET,KAAK,WAAW,IAChB,KAAK,WAAU,GACf,KAAK,aAAY,GACjB,KAAK,eAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI,GACrD,KAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI,GAEvD,SAAS,iBAAiB,aAAa,KAAK,eAAe,GAC3D,SAAS,iBAAiB,cAAc,KAAK,gBAAgB;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AASb,IARkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,EAEc,QAAQ,CAAAU,MAAY;AAC5B,eAAS,iBAAiBA,CAAQ,EAAE,QAAQ,CAAAH,MAAW;AACrD,aAAK,WAAWA,CAAO;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWA,GAAS;AAClB,IAAI,KAAK,SAAS,IAAIA,CAAO,MAE7B,KAAK,SAAS,IAAIA,CAAO,GAGzBA,EAAQ,MAAM,YAAY,cAAc,GAAG,GAC3CA,EAAQ,MAAM,YAAY,cAAc,GAAG,GAC3CA,EAAQ,MAAM,YAAY,qBAAqB,GAAG,GAClDA,EAAQ,MAAM,YAAY,sBAAsB,GAAG,GAGnDA,EAAQ,UAAU,IAAI,mBAAmB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAS;AACrB,IAAK,KAAK,SAAS,IAAIA,CAAO,MAE9B,KAAK,SAAS,OAAOA,CAAO,GAC5BA,EAAQ,UAAU,OAAO,mBAAmB,GAG5CA,EAAQ,MAAM,eAAe,YAAY,GACzCA,EAAQ,MAAM,eAAe,YAAY,GACzCA,EAAQ,MAAM,eAAe,mBAAmB,GAChDA,EAAQ,MAAM,eAAe,oBAAoB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgBM,GAAO;AACrB,SAAK,OAAO,IAAIA,EAAM,SACtB,KAAK,OAAO,IAAIA,EAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,OAAO,IAAI,MAChB,KAAK,OAAO,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAMU,IAAU,MAAM;AACpB,MAAK,KAAK,aAEV,KAAK,aAAY,GACjB,KAAK,eAAc,GAEnB,KAAK,iBAAiB,sBAAsBA,CAAO;AAAA,IACrD;AAEA,IAAAA,EAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,aAAa,MAAM,KAAK,OAAO,IAAI,KAAK,aAAa,KAAK,KAAK,QAAQ,WAC5E,KAAK,aAAa,MAAM,KAAK,OAAO,IAAI,KAAK,aAAa,KAAK,KAAK,QAAQ;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,SAAK,SAAS,QAAQ,CAAAhB,MAAW;AAC/B,WAAK,cAAcA,CAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAS;AACrB,UAAMK,IAAOL,EAAQ,sBAAqB,GACpCiB,IAAUZ,EAAK,OAAOA,EAAK,QAAQ,GACnCa,IAAUb,EAAK,MAAMA,EAAK,SAAS,GAGnCc,IAAS,KAAK,aAAa,IAAIF,GAC/BG,IAAS,KAAK,aAAa,IAAIF,GAC/BG,IAAW,KAAK,KAAKF,IAASA,IAASC,IAASA,CAAM,GAGtDE,IAAqB,KAAK,IAAID,IAAW,KAAK,QAAQ,aAAa,CAAC,GACpEE,IAAY,IAAID,GAGhBE,KAAa,KAAK,aAAa,IAAInB,EAAK,QAAQA,EAAK,OACrDoB,KAAa,KAAK,aAAa,IAAIpB,EAAK,OAAOA,EAAK;AAG1D,IAAAL,EAAQ,MAAM,YAAY,cAAcwB,EAAU,QAAQ,CAAC,CAAC,GAC5DxB,EAAQ,MAAM,YAAY,cAAcyB,EAAU,QAAQ,CAAC,CAAC,GAC5DzB,EAAQ,MAAM,YAAY,qBAAqBsB,EAAmB,QAAQ,CAAC,CAAC,GAC5EtB,EAAQ,MAAM,YAAY,sBAAsBuB,EAAU,QAAQ,CAAC,CAAC,GAGhE,KAAK,QAAQ,iBACf,KAAK,kBAAkBvB,GAASmB,GAAQC,GAAQG,CAAS,GAIvD,KAAK,QAAQ,kBACf,KAAK,oBAAoBvB,GAASwB,GAAWC,GAAWF,CAAS;AAAA,EAErE;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBvB,GAASmB,GAAQC,GAAQG,GAAW;AACpD,UAAMG,IAAkBH,IAAY,KAAK,QAAQ,iBAC3CI,IAAU,CAACR,IAAS,MAAMO,GAC1BE,IAAU,CAACR,IAAS,MAAMM,GAC1BG,IAAa,KAAKH,GAClBI,IAAgB,MAAMJ,GAEtBK,IAAS,GAAGJ,CAAO,MAAMC,CAAO,MAAMC,CAAU,oBAAoBC,CAAa;AACvF,IAAA9B,EAAQ,MAAM,YAAY,mBAAmB+B,CAAM;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB/B,GAASwB,GAAWC,GAAWF,GAAW;AAC5D,UAAMS,IAAiBT,IAAY,KAAK,QAAQ,mBAG1CU,IAAYT,IAAY,KACxBU,IAAYT,IAAY,KAGxBU,IAAW,mBAFI,MAAMZ,CAEqB,gBAAgBU,CAAS,KAAKC,CAAS,0BAA0BF,CAAc;AAC/H,IAAAhC,EAAQ,MAAM,YAAY,kBAAkBmC,CAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,aAAanC,GAASM,GAAO;AAC3B,UAAMD,IAAOL,EAAQ,sBAAqB,GACpCU,IAAIJ,EAAM,UAAUD,EAAK,MACzBM,IAAIL,EAAM,UAAUD,EAAK,KAEzBO,IAAS,SAAS,cAAc,KAAK;AAC3C,IAAAA,EAAO,YAAY,iBACnBA,EAAO,MAAM,UAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAWVX,EAAQ,MAAM,WAAW,YACzBA,EAAQ,YAAYY,CAAM,GAG1B,WAAW,MAAM;AACf,MAAIA,EAAO,cACTA,EAAO,WAAW,YAAYA,CAAM;AAAA,IAExC,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgBZ,GAAS;AACvB,UAAMoC,IAAc,CAAC9B,MAAU;AAC7B,WAAK,aAAaN,GAASM,CAAK;AAAA,IAClC;AAEA,WAAAN,EAAQ,iBAAiB,SAASoC,CAAW,GAEtC,MAAM;AACX,MAAApC,EAAQ,oBAAoB,SAASoC,CAAW;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcC,GAAY;AACxB,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAGA,EAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,WAAW,IACZ,KAAK,mBACP,qBAAqB,KAAK,cAAc,GACxC,KAAK,iBAAiB;AAAA,EAE1B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,IAAK,KAAK,aACR,KAAK,WAAW,IAChB,KAAK,eAAc;AAAA,EAEvB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,MAAK,GAGV,SAAS,oBAAoB,aAAa,KAAK,eAAe,GAC9D,SAAS,oBAAoB,cAAc,KAAK,gBAAgB,GAGhE,KAAK,SAAS,QAAQ,CAAArC,MAAW;AAC/B,WAAK,cAAcA,CAAO;AAAA,IAC5B,CAAC,GAED,KAAK,SAAS,MAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,EAAE,GAAG,KAAK,aAAY;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AACF;AChUO,MAAMsC,EAAc;AAAA,EACzB,YAAY7C,IAAU,IAAI;AACxB,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA,MACX,GAAGA;AAAA,IACT,GAEI,KAAK,cAAc,EAAE,OAAO,GAAG,MAAM,GAAG,OAAO,EAAC,GAChD,KAAK,oBAAoB,EAAE,OAAO,GAAG,MAAM,GAAG,OAAO,EAAC,GACtD,KAAK,WAAW,oBAAI,IAAG,GACvB,KAAK,WAAW,IAChB,KAAK,iBAAiB,MACtB,KAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI,MAAK,UAGT;AAAA,UAAI,CAAC,KAAK;AACR,uBAAQ,KAAK,kCAAkC,GACxC;AAIT,UAAI,OAAO,uBAAuB,qBAAsB;AACtD,YAAI;AACF,gBAAM8C,IAAa,MAAM,uBAAuB,kBAAiB;AACjE,eAAK,oBAAoBA,MAAe;AAAA,QAC1C,SAASC,GAAO;AACd,yBAAQ,KAAK,yCAAyCA,CAAK,GACpD;AAAA,QACT;AAAA;AAEA,aAAK,oBAAoB;AAG3B,aAAK,KAAK,qBAIV,KAAK,WAAW,IAChB,KAAK,WAAU,GACf,KAAK,aAAY,GACjB,KAAK,eAAc,GAEZ,MARE;AAAA;AAAA,EASX;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,4BAA4B;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,0BAA0B,KAAK,wBAAwB,KAAK,IAAI,GACrE,OAAO,iBAAiB,qBAAqB,KAAK,uBAAuB;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAQb,IAPkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,EAEc,QAAQ,CAAArC,MAAY;AAC5B,eAAS,iBAAiBA,CAAQ,EAAE,QAAQ,CAAAH,MAAW;AACrD,aAAK,WAAWA,CAAO;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWA,GAAS;AAClB,IAAI,KAAK,SAAS,IAAIA,CAAO,MAE7B,KAAK,SAAS,IAAIA,CAAO,GAGzBA,EAAQ,MAAM,YAAY,cAAc,GAAG,GAC3CA,EAAQ,MAAM,YAAY,cAAc,GAAG,GAC3CA,EAAQ,MAAM,YAAY,cAAc,GAAG,GAC3CA,EAAQ,MAAM,YAAY,sBAAsB,GAAG,GAGnDA,EAAQ,UAAU,IAAI,mBAAmB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAS;AACrB,IAAK,KAAK,SAAS,IAAIA,CAAO,MAE9B,KAAK,SAAS,OAAOA,CAAO,GAC5BA,EAAQ,UAAU,OAAO,mBAAmB,GAG5CA,EAAQ,MAAM,eAAe,YAAY,GACzCA,EAAQ,MAAM,eAAe,YAAY,GACzCA,EAAQ,MAAM,eAAe,YAAY,GACzCA,EAAQ,MAAM,eAAe,oBAAoB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwBM,GAAO;AAC7B,SAAK,YAAY,QAAQA,EAAM,SAAS,GACxC,KAAK,YAAY,OAAOA,EAAM,QAAQ,GACtC,KAAK,YAAY,QAAQA,EAAM,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAMU,IAAU,MAAM;AACpB,MAAK,KAAK,aAEV,KAAK,kBAAiB,GACtB,KAAK,eAAc,GAEnB,KAAK,iBAAiB,sBAAsBA,CAAO;AAAA,IACrD;AAEA,IAAAA,EAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,kBAAkB,UAAU,KAAK,YAAY,QAAQ,KAAK,kBAAkB,SAAS,KAAK,QAAQ,WACvG,KAAK,kBAAkB,SAAS,KAAK,YAAY,OAAO,KAAK,kBAAkB,QAAQ,KAAK,QAAQ,WACpG,KAAK,kBAAkB,UAAU,KAAK,YAAY,QAAQ,KAAK,kBAAkB,SAAS,KAAK,QAAQ;AAAA,EACzG;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,SAAK,SAAS,QAAQ,CAAAhB,MAAW;AAC/B,WAAK,cAAcA,CAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAS;AACrB,UAAM,EAAE,MAAAyC,GAAM,OAAAC,EAAK,IAAK,KAAK,mBAGvBC,IAAc,KAAK,MAAMD,IAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,aAC3DE,IAAc,KAAK,MAAMH,IAAO,KAAK,IAAI,CAAC,IAAI,KAAK,QAAQ,aAC3DlB,IAAY,KAAK,KAAKoB,IAAcA,IAAcC,IAAcA,CAAW;AAGjF,IAAA5C,EAAQ,MAAM,YAAY,cAAc2C,EAAY,QAAQ,CAAC,CAAC,GAC9D3C,EAAQ,MAAM,YAAY,cAAc4C,EAAY,QAAQ,CAAC,CAAC,GAC9D5C,EAAQ,MAAM,YAAY,sBAAsB,KAAK,IAAIuB,GAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,GAG7E,KAAK,QAAQ,cACf,KAAK,gBAAgBvB,GAAS2C,GAAaC,CAAW,GAGpD,KAAK,QAAQ,kBACf,KAAK,oBAAoB5C,GAAS2C,GAAaC,CAAW,GAGxD,KAAK,QAAQ,kBACf,KAAK,oBAAoB5C,GAAS2C,GAAaC,GAAarB,CAAS;AAAA,EAEzE;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgBvB,GAASU,GAAGC,GAAG;AAC7B,UAAMkC,IAAQlC,IAAI,KAAK,QAAQ,SACzBmC,IAAQ,CAACpC,IAAI,KAAK,QAAQ;AAEhC,IAAAV,EAAQ,MAAM,YAAY,mBAAmB,GAAG6C,CAAK,KAAK,GAC1D7C,EAAQ,MAAM,YAAY,mBAAmB,GAAG8C,CAAK,KAAK;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB9C,GAASU,GAAGC,GAAG;AACjC,UAAMoC,IAAYrC,IAAI,IAChBsC,IAAYrC,IAAI;AAEtB,IAAAX,EAAQ,MAAM,YAAY,uBAAuB,GAAG+C,CAAS,IAAI,GACjE/C,EAAQ,MAAM,YAAY,uBAAuB,GAAGgD,CAAS,IAAI;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoBhD,GAASU,GAAGC,GAAGY,GAAW;AAE5C,UAAMU,KAAavB,IAAI,KAAK,IACtBwB,KAAavB,IAAI,KAAK,IACtBqB,IAAiBT,IAAY,KAE7BY,IAAW,6BAA6BF,CAAS,KAAKC,CAAS,0BAA0BF,CAAc;AAC7G,IAAAhC,EAAQ,MAAM,YAAY,kBAAkBmC,CAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYnC,GAASuB,IAAY,GAAG;AAClC,UAAM0B,IAAa,gBAAgB,KAAK,MAAM1B,IAAY,CAAC,IAAI,CAAC;AAChE,IAAAvB,EAAQ,UAAU,IAAIiD,CAAU,GAEhC,WAAW,MAAM;AACf,MAAAjD,EAAQ,UAAU,OAAOiD,CAAU;AAAA,IACrC,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,MAAMC,GAAOC,GAAKC,GAAK;AACrB,WAAO,KAAK,IAAI,KAAK,IAAIF,GAAOC,CAAG,GAAGC,CAAG;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB;AACxB,QAAI,OAAO,uBAAuB,qBAAsB;AACtD,UAAI;AACF,cAAMb,IAAa,MAAM,uBAAuB,kBAAiB;AACjE,oBAAK,oBAAoBA,MAAe,WACjC,KAAK;AAAA,MACd,SAASC,GAAO;AACd,uBAAQ,MAAM,mDAAmDA,CAAK,GAC/D;AAAA,MACT;AAGF,gBAAK,oBAAoB,IAClB;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAEV,SAAK,cAAc,EAAE,OAAO,GAAG,MAAM,GAAG,OAAO,EAAC,GAChD,KAAK,oBAAoB,EAAE,OAAO,GAAG,MAAM,GAAG,OAAO,EAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcH,GAAY;AACxB,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAGA,EAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,WAAW,IACZ,KAAK,mBACP,qBAAqB,KAAK,cAAc,GACxC,KAAK,iBAAiB;AAAA,EAE1B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,IAAI,CAAC,KAAK,YAAY,KAAK,sBACzB,KAAK,WAAW,IAChB,KAAK,eAAc;AAAA,EAEvB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,MAAK,GAGV,OAAO,oBAAoB,qBAAqB,KAAK,uBAAuB,GAG5E,KAAK,SAAS,QAAQ,CAAArC,MAAW;AAC/B,WAAK,cAAcA,CAAO;AAAA,IAC5B,CAAC,GAED,KAAK,SAAS,MAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,EAAE,GAAG,KAAK,kBAAiB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AACF;ACjVO,MAAMqD,EAAY;AAAA,EACvB,YAAYrD,GAASP,IAAU,IAAI;AACjC,SAAK,UAAUO,GACf,KAAK,UAAU;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,GAAGP;AAAA,IACT,GAEI,KAAK,YAAY,IACjB,KAAK,UAAU,oBAAI,IAAG,GAEtB,KAAK,KAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,aAAY,GACjB,KAAK,WAAU,GACf,KAAK,mBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAEb,IAAK,KAAK,QAAQ,UAAU,SAAS,WAAW,KAC9C,KAAK,QAAQ,UAAU,IAAI,WAAW,GAIxC,KAAK,QAAQ,aAAa,qBAAqB,aAAa,GAG5D,KAAK,YAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,SAAK,QAAQ,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC,GAG9D,KAAK,QAAQ,gBACf,KAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,GAC5E,KAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,IAI1E,KAAK,QAAQ,gBACf,KAAK,QAAQ,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC,GAClE,KAAK,QAAQ,iBAAiB,QAAQ,KAAK,WAAW,KAAK,IAAI,CAAC,IAIlE,KAAK,QAAQ,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAEnB,IAAI,CAAC,KAAK,QAAQ,aAAa,MAAM,KAAK,KAAK,QAAQ,YAAY,YACjE,KAAK,QAAQ,aAAa,QAAQ,QAAQ,GAIxC,CAAC,KAAK,QAAQ,aAAa,UAAU,KAAK,KAAK,QAAQ,YAAY,YACrE,KAAK,QAAQ,aAAa,YAAY,GAAG,GAIvC,KAAK,QAAQ,iBACf,KAAK,QAAQ,aAAa,aAAa,OAAO;AAAA,EAElD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYa,GAAO;AACjB,QAAI,KAAK,aAAa,KAAK,QAAQ,UAAU;AAC3C,MAAAA,EAAM,eAAc;AACpB;AAAA,IACF;AAGA,IAAI,KAAK,QAAQ,gBACf,KAAK,aAAaA,CAAK,GAIzB,KAAK,cAAc,sBAAsB,EAAE,eAAeA,EAAK,CAAE;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBA,GAAO;AACtB,SAAK,QAAQ,UAAU,IAAI,iBAAiB,GAC5C,KAAK,cAAc,sBAAsB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBA,GAAO;AACtB,SAAK,QAAQ,UAAU,OAAO,iBAAiB,GAC/C,KAAK,cAAc,sBAAsB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYA,GAAO;AACjB,SAAK,QAAQ,UAAU,IAAI,iBAAiB,GAC5C,KAAK,cAAc,sBAAsB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWA,GAAO;AAChB,SAAK,QAAQ,UAAU,OAAO,iBAAiB,GAC/C,KAAK,cAAc,sBAAsB,EAAE,OAAO,QAAQ,eAAeA,EAAK,CAAE;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAO;AAEnB,KAAIA,EAAM,QAAQ,WAAWA,EAAM,QAAQ,SACzCA,EAAM,eAAc,GACpB,KAAK,QAAQ,MAAK;AAAA,EAEtB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAaA,GAAO;AAClB,UAAMD,IAAO,KAAK,QAAQ,sBAAqB,GACzCK,IAAIJ,EAAM,UAAUD,EAAK,MACzBM,IAAIL,EAAM,UAAUD,EAAK,KAEzBO,IAAS,SAAS,cAAc,KAAK;AAC3C,IAAAA,EAAO,YAAY,oBACnBA,EAAO,MAAM,UAAU;AAAA;AAAA,cAEbF,CAAC;AAAA,aACFC,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIM,KAAK,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,OAQpC,iBAAiB,KAAK,OAAO,EAAE,aAAa,aAC9C,KAAK,QAAQ,MAAM,WAAW,aAGhC,KAAK,QAAQ,YAAYC,CAAM,GAC/B,KAAK,QAAQ,IAAIA,CAAM,GAGvB,WAAW,MAAM;AACf,MAAIA,EAAO,cACTA,EAAO,WAAW,YAAYA,CAAM,GAEtC,KAAK,QAAQ,OAAOA,CAAM;AAAA,IAC5B,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW0C,IAAU,IAAM;AACzB,SAAK,YAAYA,GAEbA,KACF,KAAK,QAAQ,UAAU,IAAI,mBAAmB,GAC9C,KAAK,QAAQ,aAAa,aAAa,MAAM,GAC7C,KAAK,QAAQ,WAAW,OAExB,KAAK,QAAQ,UAAU,OAAO,mBAAmB,GACjD,KAAK,QAAQ,aAAa,aAAa,OAAO,GAC9C,KAAK,QAAQ,WAAW,KAG1B,KAAK,YAAW,GAChB,KAAK,cAAc,wBAAwB,EAAE,SAAAA,EAAO,CAAE;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYC,IAAW,IAAM;AAC3B,SAAK,QAAQ,WAAWA,GACxB,KAAK,QAAQ,UAAU,OAAO,sBAAsBA,CAAQ,GAC5D,KAAK,YAAW,GAChB,KAAK,cAAc,yBAAyB,EAAE,UAAAA,EAAQ,CAAE;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWC,GAAS;AAElB,UAAMC,IAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAEI,SAAK,QAAQ,UAAU,OAAO,GAAGA,CAAc,GAG3CD,KAAWA,MAAY,aACzB,KAAK,QAAQ,UAAU,IAAI,aAAaA,CAAO,EAAE,GAGnD,KAAK,cAAc,wBAAwB,EAAE,SAAAA,EAAO,CAAE;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQE,GAAM;AAEZ,UAAMC,IAAc,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACnF,SAAK,QAAQ,UAAU,OAAO,GAAGA,CAAW,GAGxCD,KAAQA,MAAS,aACnB,KAAK,QAAQ,UAAU,IAAI,aAAaA,CAAI,EAAE,GAGhD,KAAK,cAAc,qBAAqB,EAAE,MAAAA,EAAI,CAAE;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAME,IAAQ;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,UAAU,KAAK,QAAQ;AAAA,MACvB,SAAS,KAAK,QAAQ,UAAU,SAAS,iBAAiB;AAAA,MAC1D,SAAS,KAAK,QAAQ,UAAU,SAAS,iBAAiB;AAAA,IAChE;AAEI,SAAK,QAAQ,aAAa,cAAc,KAAK,UAAUA,CAAK,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcC,GAAWC,IAAS,IAAI;AACpC,UAAMxD,IAAQ,IAAI,YAAYuD,GAAW;AAAA,MACvC,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,GAAGC;AAAA,MACX;AAAA,MACM,SAAS;AAAA,MACT,YAAY;AAAA,IAClB,CAAK;AAED,SAAK,QAAQ,cAAcxD,CAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,aAAaO,GAAekD,IAAW,KAAM;AAC3C,UAAMC,IAAiB,qBAAqBnD,CAAa;AACzD,SAAK,QAAQ,UAAU,IAAImD,CAAc,GAEzC,WAAW,MAAM;AACf,WAAK,QAAQ,UAAU,OAAOA,CAAc;AAAA,IAC9C,GAAGD,CAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc1B,GAAY;AACxB,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAGA,EAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,UAAU,KAAK,QAAQ;AAAA,MACvB,SAAS,KAAK,WAAU;AAAA,MACxB,MAAM,KAAK,QAAO;AAAA,IACxB;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,WADiB,CAAC,WAAW,aAAa,WAAW,WAAW,UAAU,OAAO,EACjE,KAAK,CAAAmB,MAAW,KAAK,QAAQ,UAAU,SAAS,aAAaA,CAAO,EAAE,CAAC,KAAK;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,WADc,CAAC,MAAM,MAAM,MAAM,IAAI,EACxB,KAAK,CAAAE,MAAQ,KAAK,QAAQ,UAAU,SAAS,aAAaA,CAAI,EAAE,CAAC,KAAK;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,SAAK,QAAQ,QAAQ,CAAA9C,MAAU;AAC7B,MAAIA,EAAO,cACTA,EAAO,WAAW,YAAYA,CAAM;AAAA,IAExC,CAAC,GACD,KAAK,QAAQ,MAAK,GAKlB,KAAK,QAAQ,gBAAgB,mBAAmB,GAGhD,KAAK,QAAQ,UAAU,OAAO,mBAAmB,mBAAmB,qBAAqB,oBAAoB;AAAA,EAC/G;AACF;ACpWO,MAAMqD,EAAU;AAAA,EACrB,YAAYjE,GAASP,IAAU,IAAI;AACjC,SAAK,UAAUO,GACf,KAAK,UAAU;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA;AAAA,MACb,GAAGP;AAAA,IACT,GAEI,KAAK,YAAY,IAEjB,KAAK,KAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,aAAY,GACjB,KAAK,WAAU,GACf,KAAK,mBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAEb,IAAK,KAAK,QAAQ,UAAU,SAAS,YAAY,KAC/C,KAAK,QAAQ,UAAU,IAAI,YAAY,GAIzC,KAAK,QAAQ,aAAa,mBAAmB,aAAa,GAG1D,KAAK,YAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,IAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,UAAU,SAAS,wBAAwB,KACtF,KAAK,QAAQ,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC,GAIhE,KAAK,QAAQ,gBACf,KAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,GAC5E,KAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB,KAAK,IAAI,CAAC,IAI9E,KAAK,QAAQ,iBAAiB,SAAS,KAAK,YAAY,KAAK,IAAI,CAAC,GAClE,KAAK,QAAQ,iBAAiB,QAAQ,KAAK,WAAW,KAAK,IAAI,CAAC,GAG5D,KAAK,QAAQ,UAAU,SAAS,wBAAwB,KAC1D,KAAK,QAAQ,iBAAiB,WAAW,KAAK,cAAc,KAAK,IAAI,CAAC;AAAA,EAE1E;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAEnB,IAAI,KAAK,QAAQ,UAAU,SAAS,wBAAwB,MACrD,KAAK,QAAQ,aAAa,UAAU,KACvC,KAAK,QAAQ,aAAa,YAAY,GAAG,GAEtC,KAAK,QAAQ,aAAa,MAAM,KACnC,KAAK,QAAQ,aAAa,QAAQ,QAAQ,IAK1C,KAAK,QAAQ,iBACf,KAAK,QAAQ,aAAa,aAAa,OAAO;AAAA,EAElD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYa,GAAO;AACjB,QAAI,KAAK,WAAW;AAClB,MAAAA,EAAM,eAAc;AACpB;AAAA,IACF;AAGA,SAAK,cAAc,oBAAoB,EAAE,eAAeA,EAAK,CAAE;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBA,GAAO;AAItB,YAHA,KAAK,QAAQ,UAAU,IAAI,kBAAkB,GAGrC,KAAK,QAAQ,aAAW;AAAA,MAC9B,KAAK;AACH,aAAK,QAAQ,UAAU,IAAI,iBAAiB;AAC5C;AAAA,MACF,KAAK;AACH,aAAK,QAAQ,UAAU,IAAI,kBAAkB;AAC7C;AAAA,IAIR;AAEI,SAAK,cAAc,oBAAoB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBA,GAAO;AACtB,SAAK,QAAQ,UAAU,OAAO,oBAAoB,mBAAmB,kBAAkB,GACvF,KAAK,cAAc,oBAAoB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYA,GAAO;AACjB,SAAK,QAAQ,UAAU,IAAI,kBAAkB,GAC7C,KAAK,cAAc,oBAAoB,EAAE,OAAO,SAAS,eAAeA,EAAK,CAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWA,GAAO;AAChB,SAAK,QAAQ,UAAU,OAAO,kBAAkB,GAChD,KAAK,cAAc,oBAAoB,EAAE,OAAO,QAAQ,eAAeA,EAAK,CAAE;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAO;AAEnB,KAAIA,EAAM,QAAQ,WAAWA,EAAM,QAAQ,SACzCA,EAAM,eAAc,GACpB,KAAK,QAAQ,MAAK;AAAA,EAEtB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWgD,IAAU,IAAM;AACzB,SAAK,YAAYA,GAEbA,KACF,KAAK,QAAQ,UAAU,IAAI,oBAAoB,GAC/C,KAAK,QAAQ,aAAa,aAAa,MAAM,MAE7C,KAAK,QAAQ,UAAU,OAAO,oBAAoB,GAClD,KAAK,QAAQ,aAAa,aAAa,OAAO,IAGhD,KAAK,YAAW,GAChB,KAAK,cAAc,sBAAsB,EAAE,SAAAA,EAAO,CAAE;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,WAAWE,GAAS;AAElB,UAAMC,IAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAEI,SAAK,QAAQ,UAAU,OAAO,GAAGA,CAAc,GAG3CD,KAAWA,MAAY,aACzB,KAAK,QAAQ,UAAU,IAAI,cAAcA,CAAO,EAAE,GAGpD,KAAK,cAAc,sBAAsB,EAAE,SAAAA,EAAO,CAAE;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQE,GAAM;AAEZ,UAAMC,IAAc,CAAC,iBAAiB,eAAe;AACrD,SAAK,QAAQ,UAAU,OAAO,GAAGA,CAAW,GAGxCD,KAAQA,MAAS,aACnB,KAAK,QAAQ,UAAU,IAAI,cAAcA,CAAI,EAAE,GAGjD,KAAK,cAAc,mBAAmB,EAAE,MAAAA,EAAI,CAAE;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,SAASQ,GAAMzE,IAAU,IAAI;AAC3B,UAAM0E,IAAe;AAAA,MACnB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,GAAG1E;AAAA,IACT;AAGI,SAAK,YAAW;AAEhB,UAAM2E,IAAQ,SAAS,cAAc,KAAK;AAC1C,IAAAA,EAAM,YAAY,qCAAqCD,EAAa,QAAQ,IAC5EC,EAAM,cAAcF,GAEhBC,EAAa,YAAY,aAC3BC,EAAM,UAAU,IAAI,oBAAoBD,EAAa,OAAO,EAAE,GAGhE,KAAK,QAAQ,YAAYC,CAAK,GAC9B,KAAK,cAAc,0BAA0B,EAAE,MAAAF,GAAM,SAASC,EAAY,CAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAME,IAAgB,KAAK,QAAQ,cAAc,mBAAmB;AACpE,IAAIA,MACFA,EAAc,OAAM,GACpB,KAAK,cAAc,0BAA0B;AAAA,EAEjD;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcC,GAAS;AACrB,UAAMC,IAAO,KAAK,QAAQ,cAAc,kBAAkB;AAC1D,IAAIA,MACE,OAAOD,KAAY,WACrBC,EAAK,YAAYD,IACRA,aAAmB,gBAC5BC,EAAK,YAAY,IACjBA,EAAK,YAAYD,CAAO,IAE1B,KAAK,cAAc,8BAA8B,EAAE,SAAAA,EAAO,CAAE;AAAA,EAEhE;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAMV,IAAQ;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,aAAa,KAAK,QAAQ,UAAU,SAAS,wBAAwB;AAAA,MACrE,SAAS,KAAK,QAAQ,UAAU,SAAS,kBAAkB;AAAA,MAC3D,SAAS,KAAK,QAAQ,UAAU,SAAS,kBAAkB;AAAA,IACjE;AAEI,SAAK,QAAQ,aAAa,cAAc,KAAK,UAAUA,CAAK,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcC,GAAWC,IAAS,IAAI;AACpC,UAAMxD,IAAQ,IAAI,YAAYuD,GAAW;AAAA,MACvC,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS,KAAK;AAAA,QACd,GAAGC;AAAA,MACX;AAAA,MACM,SAAS;AAAA,MACT,YAAY;AAAA,IAClB,CAAK;AAED,SAAK,QAAQ,cAAcxD,CAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,aAAaO,GAAekD,IAAW,KAAM;AAC3C,UAAMC,IAAiB,sBAAsBnD,CAAa;AAC1D,SAAK,QAAQ,UAAU,IAAImD,CAAc,GAEzC,WAAW,MAAM;AACf,WAAK,QAAQ,UAAU,OAAOA,CAAc;AAAA,IAC9C,GAAGD,CAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc1B,GAAY;AACxB,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAGA,EAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,aAAa,KAAK,QAAQ,UAAU,SAAS,wBAAwB;AAAA,MACrE,SAAS,KAAK,WAAU;AAAA,MACxB,MAAM,KAAK,QAAO;AAAA,IACxB;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,WADiB,CAAC,YAAY,UAAU,YAAY,QAAQ,EAC5C,KAAK,CAAAmB,MAAW,KAAK,QAAQ,UAAU,SAAS,cAAcA,CAAO,EAAE,CAAC,KAAK;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,WADc,CAAC,MAAM,IAAI,EACZ,KAAK,CAAAE,MAAQ,KAAK,QAAQ,UAAU,SAAS,cAAcA,CAAI,EAAE,CAAC,KAAK;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,SAAK,QAAQ,gBAAgB,iBAAiB,GAG9C,KAAK,QAAQ,UAAU,OAAO,oBAAoB,oBAAoB,sBAAsB,mBAAmB,kBAAkB,GAGjI,KAAK,YAAW;AAAA,EAClB;AACF;ACnWO,MAAMc,EAAW;AAAA,EACtB,YAAYxE,GAASP,IAAU,IAAI;AACjC,SAAK,UAAUO,GACf,KAAK,UAAU;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,GAAGP;AAAA,IACT,GAEI,KAAK,SAAS,IACd,KAAK,KAAI;AAAA,EACX;AAAA,EAEA,OAAO;AACL,SAAK,QAAQ,aAAa,oBAAoB,aAAa;AAAA,EAC7D;AAAA,EAEA,OAAO;AACL,SAAK,SAAS,IACd,KAAK,QAAQ,UAAU,IAAI,kBAAkB;AAAA,EAC/C;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,IACd,KAAK,QAAQ,UAAU,OAAO,kBAAkB;AAAA,EAClD;AAAA,EAEA,UAAU;AACR,SAAK,QAAQ,gBAAgB,kBAAkB;AAAA,EACjD;AACF;AC/BO,MAAMgF,EAAgB;AAAA,EAC3B,YAAYzE,GAASP,IAAU,IAAI;AACjC,SAAK,UAAUO,GACf,KAAK,UAAU;AAAA,MACb,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,GAAGP;AAAA,IACT,GAEI,KAAK,KAAI;AAAA,EACX;AAAA,EAEA,OAAO;AACL,SAAK,QAAQ,aAAa,kBAAkB,aAAa;AAAA,EAC3D;AAAA,EAEA,UAAU;AACR,SAAK,QAAQ,gBAAgB,gBAAgB;AAAA,EAC/C;AACF;ACnBO,MAAMiF,EAAa;AAAA,EACxB,YAAYC,IAAe,QAAQ;AACjC,SAAK,eAAeA,GACpB,KAAK,cAAc,KAAK,eAAc,GACtC,KAAK,aAAa,OAAO,WAAW,8BAA8B,GAClE,KAAK,YAAY,oBAAI,IAAG,GAExB,KAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAEL,UAAMC,IAAa,KAAK,UAAS;AACjC,IAAIA,MACF,KAAK,eAAeA,IAItB,KAAK,WAAU,GAGf,KAAK,WAAW,iBAAiB,UAAU,KAAK,wBAAwB,KAAK,IAAI,CAAC,GAGlF,KAAK,iBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK,WAAW,UAAU,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAI,KAAK,iBAAiB,SACjB,KAAK,cAEP,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,SAASC,GAAO;AACd,IAAK,CAAC,SAAS,QAAQ,MAAM,EAAE,SAASA,CAAK,MAC3C,QAAQ,KAAK,kBAAkBA,CAAK,yBAAyB,GAC7DA,IAAQ,SAGV,KAAK,eAAeA,GACpB,KAAK,WAAU,GACf,KAAK,UAAS,GACd,KAAK,gBAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAEZ,UAAMC,IADiB,KAAK,kBAAiB,MACT,UAAU,SAAS;AACvD,SAAK,SAASA,CAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAMC,IAAiB,KAAK,kBAAiB;AAG7C,aAAS,gBAAgB,UAAU,OAAO,eAAe,YAAY,GACrE,SAAS,gBAAgB,gBAAgB,YAAY,GAGrD,SAAS,gBAAgB,UAAU,IAAI,SAASA,CAAc,EAAE,GAChE,SAAS,gBAAgB,aAAa,cAAcA,CAAc,GAGlE,KAAK,qBAAqBA,CAAc,GAGxC,KAAK,mBAAmBA,CAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqBF,GAAO;AAC1B,QAAIG,IAAiB,SAAS,cAAc,0BAA0B;AAEtE,IAAKA,MACHA,IAAiB,SAAS,cAAc,MAAM,GAC9CA,EAAe,OAAO,eACtB,SAAS,KAAK,YAAYA,CAAc;AAG1C,UAAMC,IAAS;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,IACZ;AAEI,IAAAD,EAAe,UAAUC,EAAOJ,CAAK,KAAKI,EAAO;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmBJ,GAAO;AACxB,UAAMvE,IAAQ,IAAI,YAAY,eAAe;AAAA,MAC3C,QAAQ;AAAA,QACN,OAAAuE;AAAA,QACA,eAAe,KAAK;AAAA,QACpB,eAAe,KAAK,iBAAiB;AAAA,MAC7C;AAAA,IACA,CAAK;AAED,SAAK,gBAAgBA,GACrB,SAAS,cAAcvE,CAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwBA,GAAO;AAC7B,SAAK,cAAcA,EAAM,UAAU,SAAS,SAExC,KAAK,iBAAiB,WACxB,KAAK,WAAU,GACf,KAAK,gBAAe;AAAA,EAExB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAGjB,IAFgB,SAAS,iBAAiB,qBAAqB,EAEvD,QAAQ,CAAA4E,MAAU;AACxB,MAAAA,EAAO,iBAAiB,SAAS,MAAM;AACrC,aAAK,YAAW;AAAA,MAClB,CAAC;AAAA,IACH,CAAC,GAGiB,SAAS,iBAAiB,uBAAuB,EAEzD,QAAQ,CAAA/E,MAAY;AAC5B,MAAAA,EAAS,iBAAiB,UAAU,CAACG,MAAU;AAC7C,aAAK,SAASA,EAAM,OAAO,KAAK;AAAA,MAClC,CAAC,GAGDH,EAAS,QAAQ,KAAK;AAAA,IACxB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,YAAYgF,GAAU;AACpB,gBAAK,UAAU,IAAIA,CAAQ,GAEpB,MAAM;AACX,WAAK,UAAU,OAAOA,CAAQ;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,UAAMJ,IAAiB,KAAK,kBAAiB;AAE7C,SAAK,UAAU,QAAQ,CAAAI,MAAY;AACjC,UAAI;AACF,QAAAA,EAASJ,GAAgB,KAAK,YAAY;AAAA,MAC5C,SAASvC,GAAO;AACd,gBAAQ,MAAM,mCAAmCA,CAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI;AACF,mBAAa,QAAQ,KAAK,YAAY,KAAK,YAAY;AAAA,IACzD,SAASA,GAAO;AACd,cAAQ,KAAK,oCAAoCA,CAAK;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI;AACF,aAAO,aAAa,QAAQ,KAAK,UAAU;AAAA,IAC7C,SAASA,GAAO;AACd,qBAAQ,KAAK,oCAAoCA,CAAK,GAC/C;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc4C,GAAU;AACtB,WAAO,iBAAiB,SAAS,eAAe,EAAE,iBAAiBA,CAAQ;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAKA,cAAcA,GAAUlC,GAAO;AAC7B,aAAS,gBAAgB,MAAM,YAAYkC,GAAUlC,CAAK;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBmC,GAAYC,GAAW;AAEvC,WADuB,KAAK,kBAAiB,MACnB,SAASA,IAAYD;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,WAAW,oBAAoB,UAAU,KAAK,wBAAwB,KAAK,IAAI,CAAC,GACrF,KAAK,UAAU,MAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,WAAW,KAAK,kBAAiB;AAAA,MACjC,QAAQ,KAAK;AAAA,MACb,QAAQ,KAAK,iBAAiB;AAAA,IACpC;AAAA,EACE;AACF;AC/PO,MAAME,EAAe;AAAA,EAC1B,cAAc;AACZ,SAAK,YAAY,UAAU,UAAU,YAAW,GAChD,KAAK,eAAe,KAAK,mBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,iEAAiE,KAAK,KAAK,SAAS;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,4BAA4B,KAAK,KAAK,SAAS;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,CAAC,KAAK,cAAc,CAAC,KAAK,SAAQ;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,WAAO,oBAAoB,KAAK,KAAK,SAAS;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,WAAW,KAAK,KAAK,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,kBAAkB,UAAU,UAAU,iBAAiB;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,WAAO,4BAA4B;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,WAAO,uBAAuB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,IAAI,SAAS,mBAAmB,WAAW,KAC3C,IAAI,SAAS,2BAA2B,WAAW;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,WAAO,OAAO,WAAW,kCAAkC,EAAE;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,OAAO,oBAAoB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO;AAAA,MACL,OAAO,OAAO;AAAA,MACd,QAAQ,OAAO;AAAA,IACrB;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAEnB,WAAO,IAAI,QAAQ,CAACC,MAAY;AAC9B,UAAIC,IAAS;AACb,YAAMC,IAAQ,YAAY,IAAG,GAEvBC,IAAa,MAAM;AAEvB,YADAF,KACIA,IAAS;AACX,gCAAsBE,CAAU;AAAA,aAC3B;AACL,gBAAM5B,IAAW,YAAY,QAAQ2B,GAC/BE,IAAM,KAAK,MAAMH,KAAU1B,IAAW,IAAK;AACjD,UAAAyB,EAAQI,IAAM,EAAE;AAAA,QAClB;AAAA,MACF;AAEA,4BAAsBD,CAAU;AAAA,IAClC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO;AAAA,MACL,UAAU,KAAK,SAAQ;AAAA,MACvB,UAAU,KAAK,SAAQ;AAAA,MACvB,WAAW,KAAK,UAAS;AAAA,MACzB,OAAO,KAAK,MAAK;AAAA,MACjB,WAAW,KAAK,UAAS;AAAA,MACzB,eAAe,KAAK,cAAa;AAAA,MACjC,2BAA2B,KAAK,0BAAyB;AAAA,MACzD,sBAAsB,KAAK,qBAAoB;AAAA,MAC/C,wBAAwB,KAAK,uBAAsB;AAAA,MACnD,sBAAsB,KAAK,qBAAoB;AAAA,MAC/C,YAAY,KAAK,cAAa;AAAA,MAC9B,UAAU,KAAK,YAAW;AAAA,IAChC;AAAA,EACE;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAME,IAAW;AAAA,MACf,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,IAC3B;AAGI,WAAI,KAAK,gBACPA,EAAS,sBAAsB,IAC/BA,EAAS,gBAAgB,QACzBA,EAAS,sBAAsB,KAI7B,KAAK,eACPA,EAAS,sBAAsB,KAAK,0BAAyB,GAC7DA,EAAS,gBAAgB,OACzBA,EAAS,oBAAoB,SAI3B,KAAK,cAAa,IAAK,MACzBA,EAAS,iBAAiB,KAIxB,KAAK,2BACPA,EAAS,oBAAoB,QAC7BA,EAAS,sBAAsB,IAC/BA,EAAS,iBAAiB,KAIvB,KAAK,6BACRA,EAAS,gBAAgB,SAGpBA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAMC,IAAU,CAAA;AAEhB,IAAI,KAAK,SAAQ,KAAIA,EAAQ,KAAK,eAAe,GAC7C,KAAK,SAAQ,KAAIA,EAAQ,KAAK,eAAe,GAC7C,KAAK,UAAS,KAAIA,EAAQ,KAAK,gBAAgB,GAC/C,KAAK,MAAK,KAAIA,EAAQ,KAAK,YAAY,GACvC,KAAK,UAAS,KAAIA,EAAQ,KAAK,gBAAgB,GAC/C,KAAK,cAAa,KAAIA,EAAQ,KAAK,gBAAgB,GACnD,KAAK,uBAAsB,KAAIA,EAAQ,KAAK,0BAA0B,GACtE,KAAK,qBAAoB,KAAIA,EAAQ,KAAK,wBAAwB,GAEtE,SAAS,gBAAgB,UAAU,IAAI,GAAGA,CAAO;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiBX,GAAU;AACzB,QAAIY;AACJ,UAAMC,IAAe,MAAM;AACzB,mBAAaD,CAAO,GACpBA,IAAU,WAAW,MAAM;AACzB,aAAK,aAAa,WAAW,KAAK,YAAW,GAC7CZ,EAAS,KAAK,aAAa,QAAQ;AAAA,MACrC,GAAG,GAAG;AAAA,IACR;AAEA,kBAAO,iBAAiB,UAAUa,CAAY,GAC9C,OAAO,iBAAiB,qBAAqBA,CAAY,GAElD,MAAM;AACX,aAAO,oBAAoB,UAAUA,CAAY,GACjD,OAAO,oBAAoB,qBAAqBA,CAAY;AAAA,IAC9D;AAAA,EACF;AACF;AC3MA,MAAMC,EAAc;AAAA,EAClB,YAAYxG,IAAU,IAAI;AACxB,SAAK,UAAU;AAAA,MACb,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,OAAO;AAAA;AAAA,MACP,GAAGA;AAAA,IACT,GAEI,KAAK,UAAU,IAAID,EAAa,KAAK,OAAO,GAC5C,KAAK,eAAe,IAAIkF,EAAa,KAAK,QAAQ,KAAK,GACvD,KAAK,iBAAiB,IAAIa,EAAc,GAExC,KAAK,KAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAEL,SAAK,aAAa,KAAI,GAGlB,KAAK,eAAe,UAAS,KAAM,KAAK,QAAQ,wBAClD,KAAK,gBAAgB,IAAIxE,EAAa,GACtC,KAAK,cAAc,KAAI,IAGrB,KAAK,eAAe,SAAQ,KAAM,KAAK,QAAQ,wBACjD,KAAK,gBAAgB,IAAIuB,EAAa,GACtC,KAAK,cAAc,KAAI,IAIzB,KAAK,eAAc,GAGnB,SAAS,gBAAgB,aAAa,wBAAwB,aAAa;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAEf,SAAK,YAAW,GAChB,KAAK,UAAS,GACd,KAAK,WAAU,GACf,KAAK,eAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAEZ,IADgB,SAAS,iBAAiB,qBAAqB,EACvD,QAAQ,CAAA4D,MAAU,IAAI7C,EAAY6C,CAAM,CAAC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAEV,IADc,SAAS,iBAAiB,mBAAmB,EACrD,QAAQ,CAAAC,MAAQ,IAAIlC,EAAUkC,CAAI,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,IADe,SAAS,iBAAiB,oBAAoB,EACtD,QAAQ,CAAAC,MAAS,IAAI5B,EAAW4B,CAAK,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAEf,IADa,SAAS,iBAAiB,kBAAkB,EACpD,QAAQ,CAAAC,MAAO,IAAI5B,EAAgB4B,CAAG,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,SAASxB,GAAO;AACd,SAAK,aAAa,SAASA,CAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,IAAI,KAAK,iBAAe,KAAK,cAAc,QAAO,GAC9C,KAAK,iBAAe,KAAK,cAAc,QAAO,GAClD,KAAK,aAAa,QAAO,GAEzB,SAAS,gBAAgB,gBAAgB,sBAAsB;AAAA,EACjE;AACF;AAmBI,OAAO,SAAW,OAAe,CAAC,OAAO,kBAC3C,OAAO,gBAAgBoB,GAGnB,SAAS,eAAe,YAC1B,SAAS,iBAAiB,oBAAoB,MAAM;AAClD,EAAK,SAAS,cAAc,qBAAqB,KAC/C,IAAIA,EAAa;AAErB,CAAC,IAEI,SAAS,cAAc,qBAAqB,KAC/C,IAAIA,EAAa;"}