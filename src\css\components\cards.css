/* Liquid Glass UI - Card Components */

/* Base Card Styles */
.glass-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background: var(--glass-white-800);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  border: 1px solid var(--glass-border-light);
  border-radius: var(--glass-radius-lg);
  box-shadow: var(--glass-shadow-md);
  overflow: hidden;
  transition: all var(--glass-duration-normal) var(--glass-ease-out);
}

.glass-card:hover {
  background: var(--glass-white-700);
  border-color: var(--glass-border-medium);
  box-shadow: var(--glass-shadow-lg);
  transform: translateY(-2px);
}

/* Dark Theme */
[data-theme="dark"] .glass-card {
  background: var(--glass-black-800);
  border-color: var(--glass-border-dark);
}

[data-theme="dark"] .glass-card:hover {
  background: var(--glass-black-700);
  border-color: var(--glass-border-dark-medium);
}

/* Card Header */
.glass-card-header {
  padding: var(--glass-space-lg);
  border-bottom: 1px solid var(--glass-border-light);
  background: var(--glass-white-900);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
}

[data-theme="dark"] .glass-card-header {
  background: var(--glass-black-900);
  border-bottom-color: var(--glass-border-dark);
}

.glass-card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

.glass-card-subtitle {
  margin: var(--glass-space-xs) 0 0 0;
  font-size: 0.875rem;
  opacity: 0.7;
  line-height: 1.4;
}

/* Card Body */
.glass-card-body {
  padding: var(--glass-space-lg);
  flex: 1;
}

.glass-card-text {
  margin: 0;
  line-height: 1.6;
}

/* Card Footer */
.glass-card-footer {
  padding: var(--glass-space-lg);
  border-top: 1px solid var(--glass-border-light);
  background: var(--glass-white-900);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
}

[data-theme="dark"] .glass-card-footer {
  background: var(--glass-black-900);
  border-top-color: var(--glass-border-dark);
}

/* Card Image */
.glass-card-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0;
}

.glass-card-image-top {
  border-top-left-radius: var(--glass-radius-lg);
  border-top-right-radius: var(--glass-radius-lg);
}

.glass-card-image-bottom {
  border-bottom-left-radius: var(--glass-radius-lg);
  border-bottom-right-radius: var(--glass-radius-lg);
}

/* Card Variants */
.glass-card-elevated {
  box-shadow: var(--glass-shadow-xl);
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
}

.glass-card-elevated:hover {
  box-shadow: var(--glass-shadow-2xl);
  transform: translateY(-4px);
}

.glass-card-subtle {
  background: var(--glass-white-900);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
  box-shadow: var(--glass-shadow-sm);
}

.glass-card-outlined {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border: 2px solid var(--glass-border-medium);
  box-shadow: none;
}

.glass-card-outlined:hover {
  background: var(--glass-white-900);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
  box-shadow: var(--glass-shadow-sm);
}

.glass-card-filled {
  background: var(--glass-white-600);
  backdrop-filter: blur(var(--glass-blur-xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-xl));
  border-color: var(--glass-border-strong);
}

/* Card Sizes */
.glass-card-sm {
  border-radius: var(--glass-radius-md);
}

.glass-card-sm .glass-card-header,
.glass-card-sm .glass-card-body,
.glass-card-sm .glass-card-footer {
  padding: var(--glass-space-md);
}

.glass-card-lg {
  border-radius: var(--glass-radius-xl);
}

.glass-card-lg .glass-card-header,
.glass-card-lg .glass-card-body,
.glass-card-lg .glass-card-footer {
  padding: var(--glass-space-xl);
}

/* Interactive Cards */
.glass-card-interactive {
  cursor: pointer;
  user-select: none;
}

.glass-card-interactive:active {
  transform: translateY(0);
  box-shadow: var(--glass-shadow-md);
}

/* Card with Overlay */
.glass-card-overlay {
  position: relative;
}

.glass-card-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-card-overlay > * {
  position: relative;
  z-index: 2;
}

/* Card Grid */
.glass-card-grid {
  display: grid;
  gap: var(--glass-space-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.glass-card-grid-sm {
  gap: var(--glass-space-md);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.glass-card-grid-lg {
  gap: var(--glass-space-xl);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Card Actions */
.glass-card-actions {
  display: flex;
  gap: var(--glass-space-sm);
  align-items: center;
  justify-content: flex-end;
  padding: var(--glass-space-md) var(--glass-space-lg);
}

.glass-card-actions-start {
  justify-content: flex-start;
}

.glass-card-actions-center {
  justify-content: center;
}

.glass-card-actions-between {
  justify-content: space-between;
}

/* Card Media */
.glass-card-media {
  position: relative;
  overflow: hidden;
}

.glass-card-media img,
.glass-card-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--glass-duration-slow) var(--glass-ease-out);
}

.glass-card:hover .glass-card-media img,
.glass-card:hover .glass-card-media video {
  transform: scale(1.05);
}

/* Card Badge */
.glass-card-badge {
  position: absolute;
  top: var(--glass-space-md);
  right: var(--glass-space-md);
  padding: var(--glass-space-xs) var(--glass-space-sm);
  background: var(--glass-white-700);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  border: 1px solid var(--glass-border-light);
  border-radius: var(--glass-radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 10;
}

/* Card Loading State */
.glass-card-loading {
  position: relative;
  pointer-events: none;
}

.glass-card-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-white-800);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.glass-card-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  animation: glass-card-spin 1s linear infinite;
  z-index: 101;
}

@keyframes glass-card-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Card Animations */
.glass-card-animate-fade-in {
  animation: glass-card-fade-in var(--glass-duration-slow) var(--glass-ease-out);
}

@keyframes glass-card-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.glass-card-animate-slide-up {
  animation: glass-card-slide-up var(--glass-duration-slow) var(--glass-ease-out);
}

@keyframes glass-card-slide-up {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

/* Responsive Cards */
@media (max-width: 768px) {
  .glass-card-grid {
    grid-template-columns: 1fr;
    gap: var(--glass-space-md);
  }
  
  .glass-card-header,
  .glass-card-body,
  .glass-card-footer {
    padding: var(--glass-space-md);
  }
  
  .glass-card-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .glass-card-actions .glass-btn {
    width: 100%;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .glass-card-media img,
  .glass-card-media video {
    transition: none;
    animation: none;
  }
  
  .glass-card:hover {
    transform: none;
  }
  
  .glass-card:hover .glass-card-media img,
  .glass-card:hover .glass-card-media video {
    transform: none;
  }
}
