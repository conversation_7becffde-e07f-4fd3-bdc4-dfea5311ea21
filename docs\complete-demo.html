<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Demo - Apple Liquid Glass UI</title>
    <link rel="stylesheet" href="../src/css/liquid-glass-complete.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1551384963-cccb0b7ed94b?q=80&w=3247&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') center/cover;
            min-height: 100vh;
            margin: 0;
            padding-top: 80px;
            padding-bottom: 80px;
            animation: bg-move 8s ease-in-out infinite alternate;
        }

        @keyframes bg-move {
            from {
                background-position: center center;
            }
            to {
                background-position: center top;
            }
        }
        
        .demo-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--glass-space-large);
            display: grid;
            gap: var(--glass-space-extra-large);
        }
        
        .demo-section {
            background: var(--glass-material-thin);
            border-radius: var(--glass-radius-extra-large);
            padding: var(--glass-space-extra-large);
            border: var(--glass-border-ultra-thin);
            box-shadow: var(--glass-shadow-regular);
        }
        
        .demo-section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.9);
            margin: 0 0 var(--glass-space-large) 0;
            letter-spacing: -0.02em;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--glass-space-large);
        }
        
        .demo-item {
            background: var(--glass-material-regular);
            border-radius: var(--glass-radius-large);
            padding: var(--glass-space-large);
            border: var(--glass-border-ultra-thin);
        }
        
        .demo-item h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.9);
            margin: 0 0 var(--glass-space-medium) 0;
        }
        
        .demo-item p {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.7);
            margin: 0 0 var(--glass-space-large) 0;
            line-height: 1.5;
        }
        
        .component-showcase {
            display: flex;
            flex-direction: column;
            gap: var(--glass-space-medium);
        }
        
        .button-group {
            display: flex;
            gap: var(--glass-space-medium);
            flex-wrap: wrap;
        }
        
        .form-demo {
            display: grid;
            gap: var(--glass-space-large);
        }
        
        .hero-section {
            text-align: center;
            padding: var(--glass-space-ultra-large) var(--glass-space-large);
            background: var(--glass-material-regular);
            border-radius: var(--glass-radius-ultra-large);
            margin-bottom: var(--glass-space-extra-large);
        }
        
        .hero-title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            color: rgba(0, 0, 0, 0.9);
            margin: 0 0 var(--glass-space-medium) 0;
            letter-spacing: -0.03em;
        }
        
        .hero-subtitle {
            font-size: 1.125rem;
            color: rgba(0, 0, 0, 0.7);
            margin: 0 0 var(--glass-space-extra-large) 0;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        @media (max-width: 768px) {
            .demo-content {
                padding: var(--glass-space-medium);
            }
            
            .demo-section {
                padding: var(--glass-space-large);
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="glass-navbar">
        <div class="glass-navbar-content">
            <a href="#" class="glass-navbar-brand">
                <div class="glass-navbar-logo">LG</div>
                Liquid Glass UI
            </a>
            
            <button class="glass-navbar-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
            
            <div class="glass-navbar-menu">
                <ul class="glass-navbar-nav">
                    <li><a href="#components" class="glass-navbar-link">Components</a></li>
                    <li><a href="#forms" class="glass-navbar-link">Forms</a></li>
                    <li><a href="#navigation" class="glass-navbar-link">Navigation</a></li>
                    <li><a href="index.html" class="glass-navbar-link">Docs</a></li>
                </ul>
                <button class="glass-btn glass-btn-primary">Get Started</button>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div class="glass-navbar-overlay" id="navOverlay"></div>
    <div class="glass-navbar-mobile-menu" id="navMobileMenu">
        <nav class="glass-navbar-mobile-nav">
            <a href="#components" class="glass-navbar-mobile-link">Components</a>
            <a href="#forms" class="glass-navbar-mobile-link">Forms</a>
            <a href="#navigation" class="glass-navbar-mobile-link">Navigation</a>
            <a href="index.html" class="glass-navbar-mobile-link">Documentation</a>
            <div style="margin-top: var(--glass-space-large);">
                <button class="glass-btn glass-btn-primary" style="width: 100%;">Get Started</button>
            </div>
        </nav>
    </div>

    <!-- SVG Filters for Liquid Glass Effects -->
    <svg style="position: absolute; width: 0; height: 0; pointer-events: none;">
        <defs>
            <filter id="liquid-glass-distortion" x="0%" y="0%" width="100%" height="100%">
                <feTurbulence type="fractalNoise" baseFrequency="0.008 0.008" numOctaves="2" seed="92" result="noise" />
                <feGaussianBlur in="noise" stdDeviation="2" result="blurred" />
                <feDisplacementMap in="SourceGraphic" in2="blurred" scale="70" xChannelSelector="R" yChannelSelector="G" />
            </filter>

            <filter id="liquid-glass-light" x="0%" y="0%" width="100%" height="100%">
                <feTurbulence type="fractalNoise" baseFrequency="0.012 0.012" numOctaves="3" seed="42" result="noise" />
                <feGaussianBlur in="noise" stdDeviation="1.5" result="blurred" />
                <feDisplacementMap in="SourceGraphic" in2="blurred" scale="40" xChannelSelector="R" yChannelSelector="G" />
            </filter>

            <filter id="liquid-glass-heavy" x="0%" y="0%" width="100%" height="100%">
                <feTurbulence type="fractalNoise" baseFrequency="0.005 0.005" numOctaves="4" seed="123" result="noise" />
                <feGaussianBlur in="noise" stdDeviation="3" result="blurred" />
                <feDisplacementMap in="SourceGraphic" in2="blurred" scale="100" xChannelSelector="R" yChannelSelector="G" />
            </filter>
        </defs>
    </svg>

    <!-- Main Content -->
    <main class="demo-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <h1 class="hero-title">Apple Liquid Glass UI</h1>
            <p class="hero-subtitle">Experience the complete component library with authentic Apple liquid glass design, mobile-first responsive layout, and fluid interactions.</p>
            <div class="button-group" style="justify-content: center;">
                <button class="glass-btn glass-btn-primary">Explore Components</button>
                <button class="glass-btn glass-btn-secondary">View Documentation</button>
            </div>
        </section>

        <!-- Advanced Liquid Glass Section -->
        <section class="demo-section">
            <h2>Advanced Liquid Glass Effects</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Layered Glass System</h3>
                    <p>Multi-layer glass effect with SVG distortion filters for authentic liquid appearance.</p>
                    <div class="glass-container" style="min-height: 120px;">
                        <div class="glass-filter"></div>
                        <div class="glass-overlay"></div>
                        <div class="glass-specular"></div>
                        <div class="glass-content">
                            <div style="text-align: center; width: 100%;">
                                <h4 style="margin: 0; color: white; text-shadow: 0 0 3px rgba(0,0,0,0.5);">Layered Glass</h4>
                                <p style="margin: 8px 0 0; color: rgba(255,255,255,0.8); font-size: 0.875rem;">With SVG distortion</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Music Player Glass</h3>
                    <p>Apple-style music player with liquid glass background and controls.</p>
                    <div class="glass-container" style="min-height: 120px;">
                        <div class="glass-filter" style="filter: url(#liquid-glass-light);"></div>
                        <div class="glass-overlay"></div>
                        <div class="glass-specular"></div>
                        <div class="glass-content" style="justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 8px;"></div>
                                <div>
                                    <h4 style="margin: 0; color: white; text-shadow: 0 0 3px rgba(0,0,0,0.5); font-size: 1rem;">Liquid Dreams</h4>
                                    <p style="margin: 4px 0 0; color: rgba(255,255,255,0.7); font-size: 0.875rem;">Glass UI</p>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 16px; color: white;">
                                <span style="font-size: 20px;">⏮</span>
                                <span style="font-size: 24px;">⏸</span>
                                <span style="font-size: 20px;">⏭</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Heavy Distortion Glass</h3>
                    <p>Intense liquid glass effect with heavy SVG distortion for dramatic visuals.</p>
                    <div class="glass-container" style="min-height: 120px;">
                        <div class="glass-filter" style="filter: url(#liquid-glass-heavy);"></div>
                        <div class="glass-overlay"></div>
                        <div class="glass-specular"></div>
                        <div class="glass-content">
                            <div style="text-align: center; width: 100%;">
                                <h4 style="margin: 0; color: white; text-shadow: 0 0 3px rgba(0,0,0,0.5);">Heavy Distortion</h4>
                                <p style="margin: 8px 0 0; color: rgba(255,255,255,0.8); font-size: 0.875rem;">Dramatic liquid effect</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Interactive Liquid Glass</h3>
                    <p>Hover to see the liquid glass effect respond to mouse movement.</p>
                    <div class="glass-liquid glass-parallax" style="min-height: 120px; padding: var(--glass-space-large); display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <h4 style="margin: 0; color: white; text-shadow: 0 0 3px rgba(0,0,0,0.5);">Interactive Glass</h4>
                            <p style="margin: 8px 0 0; color: rgba(255,255,255,0.8); font-size: 0.875rem;">Hover for parallax effect</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Buttons Section -->
        <section id="components" class="demo-section">
            <h2>Button Components</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Primary Buttons</h3>
                    <p>Main action buttons with Apple's signature glass material and fluid interactions.</p>
                    <div class="component-showcase">
                        <div class="button-group">
                            <button class="glass-btn glass-btn-primary">Primary</button>
                            <button class="glass-btn glass-btn-primary glass-btn-sm">Small</button>
                            <button class="glass-btn glass-btn-primary glass-btn-lg">Large</button>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Button Variants</h3>
                    <p>Different button styles for various use cases and contexts.</p>
                    <div class="component-showcase">
                        <div class="button-group">
                            <button class="glass-btn glass-btn-secondary">Secondary</button>
                            <button class="glass-btn glass-btn-success">Success</button>
                            <button class="glass-btn glass-btn-warning">Warning</button>
                            <button class="glass-btn glass-btn-danger">Danger</button>
                            <button class="glass-btn glass-btn-ghost">Ghost</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Forms Section -->
        <section id="forms" class="demo-section">
            <h2>Form Components</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Input Fields</h3>
                    <p>Glass-styled form inputs with Apple's material design and focus states.</p>
                    <div class="form-demo">
                        <div class="glass-form-group">
                            <label class="glass-label">Email Address</label>
                            <input type="email" class="glass-input" placeholder="Enter your email">
                        </div>
                        <div class="glass-form-group">
                            <label class="glass-label">Password</label>
                            <input type="password" class="glass-input" placeholder="Enter your password">
                        </div>
                        <div class="glass-form-group">
                            <label class="glass-label">Message</label>
                            <textarea class="glass-input glass-textarea" placeholder="Enter your message"></textarea>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Form Controls</h3>
                    <p>Toggles, checkboxes, and other interactive form elements.</p>
                    <div class="form-demo">
                        <div class="glass-toggle-group">
                            <span class="glass-toggle-label">Enable Notifications</span>
                            <input type="checkbox" class="glass-toggle" checked>
                        </div>
                        
                        <div class="glass-checkbox-group">
                            <input type="checkbox" class="glass-checkbox" id="terms" checked>
                            <label for="terms" class="glass-checkbox-label">I agree to the terms and conditions</label>
                        </div>
                        
                        <div class="glass-radio-group">
                            <div class="glass-radio-item">
                                <input type="radio" class="glass-radio" name="plan" id="basic" checked>
                                <label for="basic" class="glass-radio-label">Basic Plan</label>
                            </div>
                            <div class="glass-radio-item">
                                <input type="radio" class="glass-radio" name="plan" id="pro">
                                <label for="pro" class="glass-radio-label">Pro Plan</label>
                            </div>
                        </div>
                        
                        <div class="glass-range-group">
                            <label class="glass-label">Volume</label>
                            <input type="range" class="glass-range" min="0" max="100" value="50">
                            <div class="glass-range-value">50%</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cards Section -->
        <section class="demo-section">
            <h2>Card Components</h2>
            <div class="demo-grid">
                <div class="glass-card">
                    <div class="glass-card-header">
                        <h3 class="glass-card-title">Feature Card</h3>
                        <span class="glass-badge glass-badge-primary">New</span>
                    </div>
                    <div class="glass-card-body">
                        <p>This is a glass card with Apple's authentic material design, featuring proper vibrancy and depth.</p>
                        <div class="glass-progress" style="margin: var(--glass-space-medium) 0;">
                            <div class="glass-progress-bar" style="width: 75%;"></div>
                        </div>
                    </div>
                    <div class="glass-card-footer">
                        <button class="glass-btn glass-btn-primary glass-btn-sm">Learn More</button>
                        <button class="glass-btn glass-btn-ghost glass-btn-sm">Share</button>
                    </div>
                </div>

                <div class="glass-card glass-card-hover">
                    <div class="glass-card-body">
                        <div style="display: flex; align-items: center; gap: var(--glass-space-medium); margin-bottom: var(--glass-space-medium);">
                            <div class="glass-avatar glass-avatar-lg">
                                <span class="glass-avatar-initials">JD</span>
                            </div>
                            <div>
                                <h4 style="margin: 0; font-weight: 600;">John Doe</h4>
                                <p style="margin: 0; font-size: 0.875rem; color: rgba(0, 0, 0, 0.6);">Product Designer</p>
                            </div>
                        </div>
                        <p>"The Apple liquid glass design system provides an incredible foundation for building modern, beautiful interfaces."</p>
                        <div style="display: flex; gap: var(--glass-space-small);">
                            <span class="glass-badge glass-badge-success">Verified</span>
                            <span class="glass-badge">Designer</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Utilities Section -->
        <section class="demo-section">
            <h2>Utility Components</h2>
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Badges & Indicators</h3>
                    <p>Status indicators and labels with glass styling.</p>
                    <div class="component-showcase">
                        <div style="display: flex; gap: var(--glass-space-medium); flex-wrap: wrap;">
                            <span class="glass-badge">Default</span>
                            <span class="glass-badge glass-badge-primary">Primary</span>
                            <span class="glass-badge glass-badge-success">Success</span>
                            <span class="glass-badge glass-badge-warning">Warning</span>
                            <span class="glass-badge glass-badge-danger">Danger</span>
                            <span class="glass-badge glass-badge-dot glass-badge-success">Online</span>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Loading States</h3>
                    <p>Spinners and skeleton loaders for async content.</p>
                    <div class="component-showcase">
                        <div style="display: flex; align-items: center; gap: var(--glass-space-large);">
                            <div class="glass-spinner"></div>
                            <div class="glass-spinner glass-spinner-lg"></div>
                        </div>
                        <div class="glass-skeleton glass-skeleton-title"></div>
                        <div class="glass-skeleton glass-skeleton-text"></div>
                        <div class="glass-skeleton glass-skeleton-text" style="width: 80%;"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Bottom Tab Bar (Mobile) -->
    <nav class="glass-tabbar">
        <div class="glass-tabbar-nav">
            <a href="#" class="glass-tabbar-item active">
                <div class="glass-tabbar-icon">🏠</div>
                <span class="glass-tabbar-label">Home</span>
            </a>
            <a href="#" class="glass-tabbar-item">
                <div class="glass-tabbar-icon">🔍</div>
                <span class="glass-tabbar-label">Search</span>
            </a>
            <a href="#" class="glass-tabbar-item">
                <div class="glass-tabbar-icon">❤️</div>
                <span class="glass-tabbar-label">Favorites</span>
            </a>
            <a href="#" class="glass-tabbar-item">
                <div class="glass-tabbar-icon">👤</div>
                <span class="glass-tabbar-label">Profile</span>
            </a>
        </div>
    </nav>

    <!-- Toast Container -->
    <div class="glass-toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script type="module" src="../src/index.js"></script>
    <script>
        // Navigation Toggle
        const navToggle = document.getElementById('navToggle');
        const navOverlay = document.getElementById('navOverlay');
        const navMobileMenu = document.getElementById('navMobileMenu');

        navToggle.addEventListener('click', () => {
            navToggle.classList.toggle('active');
            navOverlay.classList.toggle('active');
            navMobileMenu.classList.toggle('active');
        });

        navOverlay.addEventListener('click', () => {
            navToggle.classList.remove('active');
            navOverlay.classList.remove('active');
            navMobileMenu.classList.remove('active');
        });

        // Show welcome toast
        setTimeout(() => {
            showToast('Welcome!', 'Explore all the Apple liquid glass components in this demo.', 'info');
        }, 1000);

        // Toast functionality
        function showToast(title, message, type = 'info') {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = 'glass-toast';
            
            const icons = {
                success: '✅',
                warning: '⚠️',
                error: '❌',
                info: 'ℹ️'
            };
            
            toast.innerHTML = `
                <div class="glass-toast-content">
                    <div class="glass-toast-icon">${icons[type] || icons.info}</div>
                    <div class="glass-toast-message">
                        <div class="glass-toast-title">${title}</div>
                        <div class="glass-toast-description">${message}</div>
                    </div>
                    <button class="glass-toast-close">×</button>
                </div>
            `;
            
            container.appendChild(toast);
            
            // Animate in
            setTimeout(() => toast.classList.add('active'), 100);
            
            // Auto remove
            setTimeout(() => {
                toast.classList.remove('active');
                setTimeout(() => container.removeChild(toast), 300);
            }, 5000);
            
            // Close button
            toast.querySelector('.glass-toast-close').addEventListener('click', () => {
                toast.classList.remove('active');
                setTimeout(() => container.removeChild(toast), 300);
            });
        }

        // Enhanced cursor tracking for vibrancy and parallax effects
        document.addEventListener('mousemove', (e) => {
            const xPercent = (e.clientX / window.innerWidth - 0.5) * 10;
            const yPercent = (e.clientY / window.innerHeight - 0.5) * 10;

            // Parallax background effect
            const parallaxElements = document.querySelectorAll('.glass-parallax');
            parallaxElements.forEach(el => {
                el.style.backgroundPosition = `calc(50% + ${xPercent}px) calc(50% + ${yPercent}px)`;
            });

            // Cursor tracking for vibrancy effects
            const elements = document.querySelectorAll('.glass-cursor-vibrancy, .glass-cursor-highlight, .has-cursor-effect');
            elements.forEach(el => {
                const rect = el.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;

                el.style.setProperty('--cursor-x', `${x}%`);
                el.style.setProperty('--cursor-y', `${y}%`);
            });

            // Enhanced glass container effects
            const glassContainers = document.querySelectorAll('.glass-container, .glass-liquid');
            glassContainers.forEach(container => {
                const rect = container.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const deltaX = (e.clientX - centerX) / rect.width;
                const deltaY = (e.clientY - centerY) / rect.height;

                // Subtle 3D tilt effect
                const tiltX = deltaY * 5;
                const tiltY = deltaX * -5;

                container.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg) translateZ(0)`;
            });
        });

        // Reset transforms when mouse leaves
        document.addEventListener('mouseleave', () => {
            const glassContainers = document.querySelectorAll('.glass-container, .glass-liquid');
            glassContainers.forEach(container => {
                container.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0)';
            });
        });

        // Add dynamic SVG filter animation
        function animateFilters() {
            const filters = document.querySelectorAll('feTurbulence');
            filters.forEach((filter, index) => {
                const baseFreq = parseFloat(filter.getAttribute('baseFrequency').split(' ')[0]);
                const time = Date.now() * 0.0001;
                const newFreq = baseFreq + Math.sin(time + index) * 0.002;
                filter.setAttribute('baseFrequency', `${newFreq} ${newFreq}`);
            });
            requestAnimationFrame(animateFilters);
        }

        // Start filter animation
        animateFilters();
    </script>
</body>
</html>
