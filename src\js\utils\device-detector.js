/**
 * Device Detector Utility
 * Detects device capabilities and features for optimal glass effects
 */

export class DeviceDetector {
  constructor() {
    this.userAgent = navigator.userAgent.toLowerCase()
    this.capabilities = this.detectCapabilities()
  }

  /**
   * Check if device is mobile
   */
  isMobile() {
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent)
  }

  /**
   * Check if device is tablet
   */
  isTablet() {
    return /ipad|android(?!.*mobile)/i.test(this.userAgent)
  }

  /**
   * Check if device is desktop
   */
  isDesktop() {
    return !this.isMobile() && !this.isTablet()
  }

  /**
   * Check if device is iOS
   */
  isIOS() {
    return /iphone|ipad|ipod/i.test(this.userAgent)
  }

  /**
   * Check if device is Android
   */
  isAndroid() {
    return /android/i.test(this.userAgent)
  }

  /**
   * Check if device supports touch
   */
  supportsTouch() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  /**
   * Check if device supports device orientation
   */
  supportsDeviceOrientation() {
    return 'DeviceOrientationEvent' in window
  }

  /**
   * Check if device supports device motion
   */
  supportsDeviceMotion() {
    return 'DeviceMotionEvent' in window
  }

  /**
   * Check if browser supports backdrop-filter
   */
  supportsBackdropFilter() {
    return CSS.supports('backdrop-filter', 'blur(1px)') || 
           CSS.supports('-webkit-backdrop-filter', 'blur(1px)')
  }

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }

  /**
   * Get device pixel ratio
   */
  getPixelRatio() {
    return window.devicePixelRatio || 1
  }

  /**
   * Get viewport dimensions
   */
  getViewport() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    }
  }

  /**
   * Check if device has high refresh rate
   */
  hasHighRefreshRate() {
    // Estimate based on requestAnimationFrame timing
    return new Promise((resolve) => {
      let frames = 0
      const start = performance.now()
      
      const checkFrame = () => {
        frames++
        if (frames < 60) {
          requestAnimationFrame(checkFrame)
        } else {
          const duration = performance.now() - start
          const fps = Math.round(frames / (duration / 1000))
          resolve(fps > 60)
        }
      }
      
      requestAnimationFrame(checkFrame)
    })
  }

  /**
   * Detect all device capabilities
   */
  detectCapabilities() {
    return {
      isMobile: this.isMobile(),
      isTablet: this.isTablet(),
      isDesktop: this.isDesktop(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      supportsTouch: this.supportsTouch(),
      supportsDeviceOrientation: this.supportsDeviceOrientation(),
      supportsDeviceMotion: this.supportsDeviceMotion(),
      supportsBackdropFilter: this.supportsBackdropFilter(),
      prefersReducedMotion: this.prefersReducedMotion(),
      pixelRatio: this.getPixelRatio(),
      viewport: this.getViewport(),
    }
  }

  /**
   * Get optimal glass effect settings based on device
   */
  getOptimalSettings() {
    const settings = {
      blurIntensity: 'medium',
      animationDuration: 'normal',
      enableCursorEffects: false,
      enableMotionEffects: false,
      enableParallax: false,
      enableHighFrameRate: false,
    }

    // Desktop optimizations
    if (this.isDesktop()) {
      settings.enableCursorEffects = true
      settings.blurIntensity = 'high'
      settings.enableHighFrameRate = true
    }

    // Mobile optimizations
    if (this.isMobile()) {
      settings.enableMotionEffects = this.supportsDeviceOrientation()
      settings.blurIntensity = 'low'
      settings.animationDuration = 'fast'
    }

    // High-end device optimizations
    if (this.getPixelRatio() > 2) {
      settings.enableParallax = true
    }

    // Reduced motion preferences
    if (this.prefersReducedMotion()) {
      settings.animationDuration = 'none'
      settings.enableMotionEffects = false
      settings.enableParallax = false
    }

    // Backdrop filter fallback
    if (!this.supportsBackdropFilter()) {
      settings.blurIntensity = 'none'
    }

    return settings
  }

  /**
   * Add device classes to document
   */
  addDeviceClasses() {
    const classes = []
    
    if (this.isMobile()) classes.push('device-mobile')
    if (this.isTablet()) classes.push('device-tablet')
    if (this.isDesktop()) classes.push('device-desktop')
    if (this.isIOS()) classes.push('device-ios')
    if (this.isAndroid()) classes.push('device-android')
    if (this.supportsTouch()) classes.push('supports-touch')
    if (this.supportsBackdropFilter()) classes.push('supports-backdrop-filter')
    if (this.prefersReducedMotion()) classes.push('prefers-reduced-motion')
    
    document.documentElement.classList.add(...classes)
  }

  /**
   * Listen for viewport changes
   */
  onViewportChange(callback) {
    let timeout
    const handleResize = () => {
      clearTimeout(timeout)
      timeout = setTimeout(() => {
        this.capabilities.viewport = this.getViewport()
        callback(this.capabilities.viewport)
      }, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }
}
