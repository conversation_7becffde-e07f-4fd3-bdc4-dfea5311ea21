/* Playground Specific Styles */

/* Control Panel */
.component-controls {
    transition: all 0.3s ease;
}

.component-controls.hidden {
    display: none;
}

.control-group {
    margin-bottom: 1rem;
}

.control-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.control-group input,
.control-group select,
.control-group textarea {
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.control-group input:focus,
.control-group select:focus,
.control-group textarea:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.control-group input[type="range"] {
    padding: 0;
    height: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
    appearance: none;
    cursor: pointer;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Preview Container */
#preview-container {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

#preview-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
    z-index: -1;
}

/* Dynamic Preview Styles */
.preview-element {
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

/* Code Generation */
#generated-code {
    white-space: pre-wrap;
    word-break: break-word;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .control-panel {
        position: static;
    }
    
    .playground-layout {
        grid-template-columns: 1fr;
    }
}

/* Animation for control changes */
.control-change-animation {
    animation: controlChange 0.3s ease;
}

@keyframes controlChange {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* Custom Scrollbar for Controls */
.control-panel {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.control-panel::-webkit-scrollbar {
    width: 6px;
}

.control-panel::-webkit-scrollbar-track {
    background: transparent;
}

.control-panel::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Interactive Elements */
.interactive-preview {
    cursor: pointer;
    transition: all 0.2s ease;
}

.interactive-preview:hover {
    transform: translateY(-2px);
}

/* Glass Effect Preview Backgrounds */
.glass-preview-bg {
    background: 
        linear-gradient(135deg, 
            rgba(59, 130, 246, 0.1) 0%, 
            rgba(147, 51, 234, 0.1) 50%, 
            rgba(236, 72, 153, 0.1) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

/* Success States */
.success-state {
    border-color: rgba(34, 197, 94, 0.5) !important;
    background: rgba(34, 197, 94, 0.1) !important;
}

.success-state::after {
    content: '✓';
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    color: #22c55e;
    font-weight: bold;
    font-size: 0.875rem;
}

/* Loading States */
.loading-state {
    position: relative;
    overflow: hidden;
}

.loading-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Tooltip Styles */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* Color Picker Styles */
.color-picker {
    width: 100%;
    height: 2.5rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: none;
    cursor: pointer;
}

.color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 0.5rem;
}

/* Advanced Controls */
.advanced-controls {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.advanced-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
}

.advanced-toggle input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Export Options */
.export-options {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.export-btn {
    flex: 1;
    padding: 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.export-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .control-group {
        margin-bottom: 1.5rem;
    }
    
    .control-group input,
    .control-group select,
    .control-group textarea {
        padding: 1rem;
        font-size: 1rem;
    }
    
    #preview-container {
        min-height: 150px;
        padding: 1rem;
    }
    
    .export-options {
        flex-direction: column;
    }
    
    .export-btn {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}
