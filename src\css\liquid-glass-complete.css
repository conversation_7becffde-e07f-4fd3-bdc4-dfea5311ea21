/* Apple Liquid Glass UI - Complete CSS */

/* CSS Variables */
:root {
  /* Apple Material Vibrancy Effects */
  --glass-vibrancy-ultra-thin: rgba(255, 255, 255, 0.9);
  --glass-vibrancy-thin: rgba(255, 255, 255, 0.8);
  --glass-vibrancy-regular: rgba(255, 255, 255, 0.7);
  --glass-vibrancy-thick: rgba(255, 255, 255, 0.6);
  --glass-vibrancy-heavy: rgba(255, 255, 255, 0.5);
  --glass-vibrancy-ultra-heavy: rgba(255, 255, 255, 0.4);

  /* Apple Blur Levels */
  --glass-blur-ultra: 1px;
  --glass-blur-thin: 3px;
  --glass-blur-regular: 6px;
  --glass-blur-thick: 10px;
  --glass-blur-heavy: 15px;
  --glass-blur-ultra-heavy: 25px;

  /* Apple Material Backgrounds */
  --glass-material-ultra-thin: rgba(255, 255, 255, 0.9);
  --glass-material-thin: rgba(255, 255, 255, 0.8);
  --glass-material-regular: rgba(255, 255, 255, 0.7);
  --glass-material-thick: rgba(255, 255, 255, 0.6);
  --glass-material-heavy: rgba(255, 255, 255, 0.5);
  --glass-material-ultra-heavy: rgba(255, 255, 255, 0.4);

  /* Apple Borders */
  --glass-border-ultra-thin: 0.5px solid rgba(255, 255, 255, 0.3);
  --glass-border-thin: 0.5px solid rgba(255, 255, 255, 0.4);
  --glass-border-regular: 0.5px solid rgba(255, 255, 255, 0.5);
  --glass-border-thick: 1px solid rgba(255, 255, 255, 0.6);

  /* Apple Shadows */
  --glass-shadow-ultra-thin: 0 0.5px 1px rgba(0, 0, 0, 0.04), 0 0.5px 2px rgba(0, 0, 0, 0.02);
  --glass-shadow-thin: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
  --glass-shadow-regular: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.06);
  --glass-shadow-thick: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.08);
  --glass-shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.1);

  /* Apple Highlights */
  --glass-highlight-top: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);

  /* Apple Border Radius */
  --glass-radius-ultra-small: 0.25rem;
  --glass-radius-small: 0.5rem;
  --glass-radius-medium: 0.75rem;
  --glass-radius-large: 1rem;
  --glass-radius-extra-large: 1.25rem;
  --glass-radius-ultra-large: 1.5rem;
  --glass-radius-pill: 50rem;

  /* Apple Animation */
  --glass-ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --glass-ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --glass-ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --glass-duration-instant: 100ms;
  --glass-duration-fast: 200ms;
  --glass-duration-normal: 300ms;
  --glass-duration-slow: 500ms;

  /* Apple Spacing */
  --glass-space-ultra-small: 0.125rem;
  --glass-space-small: 0.25rem;
  --glass-space-medium: 0.5rem;
  --glass-space-large: 1rem;
  --glass-space-extra-large: 1.5rem;
  --glass-space-ultra-large: 2rem;
}

/* Core Glass Effects */
.glass {
  position: relative !important;
  background: var(--glass-material-regular, rgba(255, 255, 255, 0.7)) !important;
  backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  border: var(--glass-border-regular, 0.5px solid rgba(255, 255, 255, 0.5)) !important;
  border-radius: var(--glass-radius-large, 1rem) !important;
  box-shadow: var(--glass-shadow-regular, 0 2px 8px rgba(0, 0, 0, 0.08)) !important;
  overflow: hidden !important;
  transition: all var(--glass-duration-normal, 300ms) var(--glass-ease-out-expo, ease-out) !important;
}

.glass::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 50% !important;
  background: var(--glass-highlight-top, linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%)) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.glass > * {
  position: relative !important;
  z-index: 2 !important;
}

.glass:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: var(--glass-shadow-heavy, 0 8px 32px rgba(0, 0, 0, 0.12)) !important;
}

/* Glass Variants */
.glass-thin {
  background: var(--glass-material-thin, rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(var(--glass-blur-thin, 3px)) saturate(190%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-thin, 3px)) saturate(190%) !important;
  border: var(--glass-border-thin, 0.5px solid rgba(255, 255, 255, 0.4)) !important;
  box-shadow: var(--glass-shadow-thin, 0 1px 3px rgba(0, 0, 0, 0.06)) !important;
}

.glass-thick {
  background: var(--glass-material-thick, rgba(255, 255, 255, 0.6)) !important;
  backdrop-filter: blur(var(--glass-blur-thick, 10px)) saturate(170%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-thick, 10px)) saturate(170%) !important;
  border: var(--glass-border-thick, 1px solid rgba(255, 255, 255, 0.6)) !important;
  box-shadow: var(--glass-shadow-thick, 0 4px 16px rgba(0, 0, 0, 0.1)) !important;
}

/* Advanced Layered Glass System */
.glass-container {
  position: relative !important;
  display: flex !important;
  border-radius: var(--glass-radius-large, 1rem) !important;
  overflow: hidden !important;
  box-shadow: var(--glass-shadow-regular, 0 2px 8px rgba(0, 0, 0, 0.08)) !important;
  transition: all var(--glass-duration-normal, 300ms) var(--glass-ease-spring, ease-out) !important;
}

.glass-filter {
  position: absolute !important;
  inset: 0 !important;
  z-index: 0 !important;
  backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  filter: url(#liquid-glass-distortion) !important;
  isolation: isolate !important;
}

.glass-overlay {
  position: absolute !important;
  inset: 0 !important;
  z-index: 1 !important;
  background: var(--glass-material-regular, rgba(255, 255, 255, 0.7)) !important;
}

.glass-specular {
  position: absolute !important;
  inset: 0 !important;
  z-index: 2 !important;
  border-radius: inherit !important;
  overflow: hidden !important;
  box-shadow: inset 1px 1px 0 rgba(255, 255, 255, 0.25), inset 0 0 5px rgba(255, 255, 255, 0.25) !important;
}

.glass-content {
  position: relative !important;
  z-index: 3 !important;
  display: flex !important;
  align-items: center !important;
  padding: var(--glass-space-large, 1rem) !important;
  width: 100% !important;
}

/* Glass Buttons */
.glass-btn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: var(--glass-space-medium, 0.5rem) !important;
  padding: var(--glass-space-medium, 0.5rem) var(--glass-space-large, 1rem) !important;
  border: var(--glass-border-regular, 0.5px solid rgba(255, 255, 255, 0.5)) !important;
  border-radius: var(--glass-radius-large, 1rem) !important;
  background: var(--glass-material-regular, rgba(255, 255, 255, 0.7)) !important;
  backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  color: rgba(0, 0, 0, 0.85) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
  text-decoration: none !important;
  cursor: pointer !important;
  user-select: none !important;
  transition: all var(--glass-duration-normal, 300ms) var(--glass-ease-out-expo, ease-out) !important;
  box-shadow: var(--glass-shadow-regular, 0 2px 8px rgba(0, 0, 0, 0.08)) !important;
  overflow: hidden !important;
}

.glass-btn::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 50% !important;
  background: var(--glass-highlight-top, linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%)) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.glass-btn > * {
  position: relative !important;
  z-index: 2 !important;
}

.glass-btn:hover {
  background: var(--glass-material-thick, rgba(255, 255, 255, 0.6)) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: var(--glass-shadow-thick, 0 4px 16px rgba(0, 0, 0, 0.1)) !important;
}

.glass-btn:active {
  transform: translateY(0) scale(0.98) !important;
  transition: all var(--glass-duration-instant, 100ms) ease-out !important;
}

/* Button Variants */
.glass-btn-primary {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.8), rgba(0, 122, 255, 0.6)) !important;
  color: white !important;
  border: 0.5px solid rgba(0, 122, 255, 0.8) !important;
}

.glass-btn-secondary {
  background: var(--glass-material-thin, rgba(255, 255, 255, 0.8)) !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

.glass-btn-ghost {
  background: var(--glass-material-ultra-thin, rgba(255, 255, 255, 0.9)) !important;
  border: var(--glass-border-ultra-thin, 0.5px solid rgba(255, 255, 255, 0.3)) !important;
}

.glass-btn-lg {
  padding: var(--glass-space-large, 1rem) var(--glass-space-extra-large, 1.5rem) !important;
  font-size: 1rem !important;
}

/* Liquid Glass with Parallax Effect */
.glass-liquid {
  position: relative !important;
  background: var(--glass-material-regular, rgba(255, 255, 255, 0.7)) !important;
  backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  -webkit-backdrop-filter: blur(var(--glass-blur-regular, 6px)) saturate(180%) !important;
  border: var(--glass-border-regular, 0.5px solid rgba(255, 255, 255, 0.5)) !important;
  border-radius: var(--glass-radius-large, 1rem) !important;
  box-shadow: var(--glass-shadow-regular, 0 2px 8px rgba(0, 0, 0, 0.08)) !important;
  overflow: hidden !important;
  transition: all var(--glass-duration-normal, 300ms) var(--glass-ease-spring, ease-out) !important;
}

.glass-liquid::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: var(--glass-highlight-top, linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%)) !important;
  filter: url(#liquid-glass-distortion) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.glass-liquid > * {
  position: relative !important;
  z-index: 2 !important;
}

/* Parallax Background Movement */
.glass-parallax {
  background-attachment: fixed !important;
  background-position: center center !important;
  transition: background-position var(--glass-duration-fast, 200ms) ease-out !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass-container {
    min-height: 80px !important;
  }
  
  .glass-btn {
    padding: var(--glass-space-small, 0.25rem) var(--glass-space-medium, 0.5rem) !important;
    font-size: 0.8rem !important;
  }
  
  .glass-btn-lg {
    padding: var(--glass-space-medium, 0.5rem) var(--glass-space-large, 1rem) !important;
    font-size: 0.9rem !important;
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .glass,
  .glass-thin,
  .glass-thick,
  .glass-btn,
  .glass-liquid {
    background: rgba(255, 255, 255, 0.95) !important;
  }
}
