<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple Liquid Glass Demo - Liquid Glass UI</title>
    <link rel="stylesheet" href="../src/css/index.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .demo-section {
            padding: 2rem;
            border-radius: var(--glass-radius-extra-large);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: rgba(0, 0, 0, 0.9);
            letter-spacing: -0.02em;
        }
        
        .demo-description {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.7);
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .glass-card-demo {
            padding: 1.5rem;
            border-radius: var(--glass-radius-large);
            margin-bottom: 1rem;
        }
        
        .glass-card-demo h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.9);
        }
        
        .glass-card-demo p {
            margin: 0;
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.7);
            line-height: 1.5;
        }
        
        .intensity-demo {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .intensity-item {
            padding: 1rem;
            border-radius: var(--glass-radius-medium);
            text-align: center;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.8);
        }
        
        .hero-section {
            text-align: center;
            padding: 4rem 2rem;
            border-radius: var(--glass-radius-ultra-large);
            margin-bottom: 3rem;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.03em;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            color: rgba(0, 0, 0, 0.7);
            margin-bottom: 2rem;
            font-weight: 400;
        }
        
        .interactive-demo {
            padding: 2rem;
            border-radius: var(--glass-radius-large);
            text-align: center;
            cursor: pointer;
            transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
        }
        
        .interactive-demo:hover {
            transform: translateY(-4px) scale(1.02);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section glass-regular">
        <h1 class="hero-title">Apple Liquid Glass</h1>
        <p class="hero-subtitle">Authentic Apple-style glass effects with vibrancy, depth, and fluid interactions</p>
        <div class="button-grid" style="max-width: 400px; margin: 0 auto;">
            <button class="glass-btn glass-btn-primary">Primary</button>
            <button class="glass-btn glass-btn-secondary">Secondary</button>
        </div>
    </div>

    <div class="demo-container">
        <!-- Material Variants -->
        <div class="demo-section glass-thin">
            <h2 class="demo-title">Material Variants</h2>
            <p class="demo-description">Apple's authentic material system with different opacity and blur levels</p>
            
            <div class="intensity-demo">
                <div class="intensity-item glass-ultra-thin">Ultra Thin</div>
                <div class="intensity-item glass-thin">Thin</div>
                <div class="intensity-item glass-regular">Regular</div>
                <div class="intensity-item glass-thick">Thick</div>
                <div class="intensity-item glass-heavy">Heavy</div>
            </div>
        </div>

        <!-- Interactive Buttons -->
        <div class="demo-section glass-regular">
            <h2 class="demo-title">Interactive Buttons</h2>
            <p class="demo-description">Fluid animations with Apple's signature ease curves and micro-interactions</p>
            
            <div class="button-grid">
                <button class="glass-btn glass-btn-primary">Primary</button>
                <button class="glass-btn glass-btn-secondary">Secondary</button>
                <button class="glass-btn glass-btn-success">Success</button>
                <button class="glass-btn glass-btn-warning">Warning</button>
                <button class="glass-btn glass-btn-danger">Danger</button>
                <button class="glass-btn glass-btn-ghost">Ghost</button>
            </div>
        </div>

        <!-- Glass Cards -->
        <div class="demo-section glass-thick">
            <h2 class="demo-title">Glass Cards</h2>
            <p class="demo-description">Cards with Apple's signature glass material and reflection effects</p>
            
            <div class="glass-card-demo glass-regular glass-rounded-large">
                <h3>Regular Glass</h3>
                <p>Standard glass effect with balanced opacity and blur for most use cases.</p>
            </div>
            
            <div class="glass-card-demo glass-heavy glass-rounded-large">
                <h3>Heavy Glass</h3>
                <p>Stronger glass effect with increased blur and reduced transparency.</p>
            </div>
        </div>

        <!-- Vibrancy Effects -->
        <div class="demo-section glass-vibrancy">
            <h2 class="demo-title">Vibrancy Effects</h2>
            <p class="demo-description">Dynamic color adaptation and saturation enhancement like Apple's native materials</p>
            
            <div class="interactive-demo glass-vibrancy-light">
                <h3>Light Vibrancy</h3>
                <p>Enhanced saturation and brightness for vibrant backgrounds</p>
            </div>
        </div>

        <!-- Cursor Effects -->
        <div class="demo-section glass-regular has-cursor-effect glass-cursor-vibrancy">
            <h2 class="demo-title">Cursor Interactions</h2>
            <p class="demo-description">Hover to see Apple-style cursor-based vibrancy and lighting effects</p>
            
            <div class="interactive-demo glass-thick glass-cursor-highlight">
                <h3>Interactive Surface</h3>
                <p>Move your cursor over this area to see the vibrancy effect</p>
            </div>
        </div>

        <!-- Shadow Depth -->
        <div class="demo-section glass-heavy">
            <h2 class="demo-title">Layered Shadows</h2>
            <p class="demo-description">Multiple shadow layers create authentic depth perception like Apple's design system</p>
            
            <div class="glass-card-demo glass-regular glass-shadow-heavy">
                <h3>Heavy Shadows</h3>
                <p>Deep shadows with multiple layers for enhanced depth perception.</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="../src/index.js"></script>
    <script>
        // Add cursor tracking for vibrancy effects
        document.addEventListener('mousemove', (e) => {
            const elements = document.querySelectorAll('.glass-cursor-vibrancy, .glass-cursor-highlight');
            elements.forEach(el => {
                const rect = el.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;
                
                el.style.setProperty('--cursor-x', `${x}%`);
                el.style.setProperty('--cursor-y', `${y}%`);
            });
        });
    </script>
</body>
</html>
