/**
 * Glass Card Component
 * Enhanced card with glass effects and interactions
 */

export class GlassCard {
  constructor(element, options = {}) {
    this.element = element
    this.options = {
      enableHover: true,
      enableClick: true,
      enableLoading: true,
      hoverEffect: 'lift', // 'lift', 'glow', 'scale'
      ...options
    }

    this.isLoading = false
    
    this.init()
  }

  /**
   * Initialize card component
   */
  init() {
    this.setupElement()
    this.bindEvents()
    this.setupAccessibility()
  }

  /**
   * Setup card element
   */
  setupElement() {
    // Add base glass card class if not present
    if (!this.element.classList.contains('glass-card')) {
      this.element.classList.add('glass-card')
    }

    // Add component identifier
    this.element.setAttribute('data-glass-card', 'initialized')

    // Setup initial state
    this.updateState()
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Click events for interactive cards
    if (this.options.enableClick && this.element.classList.contains('glass-card-interactive')) {
      this.element.addEventListener('click', this.handleClick.bind(this))
    }

    // Mouse events for hover effects
    if (this.options.enableHover) {
      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))
    }

    // Focus events for accessibility
    this.element.addEventListener('focus', this.handleFocus.bind(this))
    this.element.addEventListener('blur', this.handleBlur.bind(this))

    // Keyboard events for interactive cards
    if (this.element.classList.contains('glass-card-interactive')) {
      this.element.addEventListener('keydown', this.handleKeyDown.bind(this))
    }
  }

  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    // Make interactive cards focusable and add role
    if (this.element.classList.contains('glass-card-interactive')) {
      if (!this.element.hasAttribute('tabindex')) {
        this.element.setAttribute('tabindex', '0')
      }
      if (!this.element.getAttribute('role')) {
        this.element.setAttribute('role', 'button')
      }
    }

    // Add ARIA attributes for loading state
    if (this.options.enableLoading) {
      this.element.setAttribute('aria-busy', 'false')
    }
  }

  /**
   * Handle click events
   */
  handleClick(event) {
    if (this.isLoading) {
      event.preventDefault()
      return
    }

    // Dispatch custom event
    this.dispatchEvent('glass-card-click', { originalEvent: event })
  }

  /**
   * Handle mouse enter
   */
  handleMouseEnter(event) {
    this.element.classList.add('glass-card-hover')
    
    // Apply hover effect based on options
    switch (this.options.hoverEffect) {
      case 'glow':
        this.element.classList.add('glass-card-glow')
        break
      case 'scale':
        this.element.classList.add('glass-card-scale')
        break
      default:
        // 'lift' is handled by CSS
        break
    }

    this.dispatchEvent('glass-card-hover', { state: 'enter', originalEvent: event })
  }

  /**
   * Handle mouse leave
   */
  handleMouseLeave(event) {
    this.element.classList.remove('glass-card-hover', 'glass-card-glow', 'glass-card-scale')
    this.dispatchEvent('glass-card-hover', { state: 'leave', originalEvent: event })
  }

  /**
   * Handle focus
   */
  handleFocus(event) {
    this.element.classList.add('glass-card-focus')
    this.dispatchEvent('glass-card-focus', { state: 'focus', originalEvent: event })
  }

  /**
   * Handle blur
   */
  handleBlur(event) {
    this.element.classList.remove('glass-card-focus')
    this.dispatchEvent('glass-card-focus', { state: 'blur', originalEvent: event })
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event) {
    // Activate card with Enter or Space
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      this.element.click()
    }
  }

  /**
   * Set loading state
   */
  setLoading(loading = true) {
    this.isLoading = loading
    
    if (loading) {
      this.element.classList.add('glass-card-loading')
      this.element.setAttribute('aria-busy', 'true')
    } else {
      this.element.classList.remove('glass-card-loading')
      this.element.setAttribute('aria-busy', 'false')
    }

    this.updateState()
    this.dispatchEvent('glass-card-loading', { loading })
  }

  /**
   * Set card variant
   */
  setVariant(variant) {
    // Remove existing variant classes
    const variantClasses = [
      'glass-card-elevated',
      'glass-card-subtle',
      'glass-card-outlined',
      'glass-card-filled'
    ]
    
    this.element.classList.remove(...variantClasses)
    
    // Add new variant class
    if (variant && variant !== 'default') {
      this.element.classList.add(`glass-card-${variant}`)
    }

    this.dispatchEvent('glass-card-variant', { variant })
  }

  /**
   * Set card size
   */
  setSize(size) {
    // Remove existing size classes
    const sizeClasses = ['glass-card-sm', 'glass-card-lg']
    this.element.classList.remove(...sizeClasses)
    
    // Add new size class
    if (size && size !== 'default') {
      this.element.classList.add(`glass-card-${size}`)
    }

    this.dispatchEvent('glass-card-size', { size })
  }

  /**
   * Add badge to card
   */
  addBadge(text, options = {}) {
    const badgeOptions = {
      position: 'top-right',
      variant: 'default',
      ...options
    }

    // Remove existing badge
    this.removeBadge()

    const badge = document.createElement('div')
    badge.className = `glass-card-badge glass-card-badge-${badgeOptions.position}`
    badge.textContent = text
    
    if (badgeOptions.variant !== 'default') {
      badge.classList.add(`glass-card-badge-${badgeOptions.variant}`)
    }

    this.element.appendChild(badge)
    this.dispatchEvent('glass-card-badge-added', { text, options: badgeOptions })
  }

  /**
   * Remove badge from card
   */
  removeBadge() {
    const existingBadge = this.element.querySelector('.glass-card-badge')
    if (existingBadge) {
      existingBadge.remove()
      this.dispatchEvent('glass-card-badge-removed')
    }
  }

  /**
   * Update card content
   */
  updateContent(content) {
    const body = this.element.querySelector('.glass-card-body')
    if (body) {
      if (typeof content === 'string') {
        body.innerHTML = content
      } else if (content instanceof HTMLElement) {
        body.innerHTML = ''
        body.appendChild(content)
      }
      this.dispatchEvent('glass-card-content-updated', { content })
    }
  }

  /**
   * Update card state
   */
  updateState() {
    const state = {
      loading: this.isLoading,
      interactive: this.element.classList.contains('glass-card-interactive'),
      focused: this.element.classList.contains('glass-card-focus'),
      hovered: this.element.classList.contains('glass-card-hover')
    }

    this.element.setAttribute('data-state', JSON.stringify(state))
  }

  /**
   * Dispatch custom event
   */
  dispatchEvent(eventName, detail = {}) {
    const event = new CustomEvent(eventName, {
      detail: {
        card: this,
        element: this.element,
        ...detail
      },
      bubbles: true,
      cancelable: true
    })

    this.element.dispatchEvent(event)
  }

  /**
   * Add animation
   */
  addAnimation(animationType, duration = 1000) {
    const animationClass = `glass-card-animate-${animationType}`
    this.element.classList.add(animationClass)

    setTimeout(() => {
      this.element.classList.remove(animationClass)
    }, duration)
  }

  /**
   * Update options
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
  }

  /**
   * Get card state
   */
  getState() {
    return {
      loading: this.isLoading,
      interactive: this.element.classList.contains('glass-card-interactive'),
      variant: this.getVariant(),
      size: this.getSize()
    }
  }

  /**
   * Get current variant
   */
  getVariant() {
    const variants = ['elevated', 'subtle', 'outlined', 'filled']
    return variants.find(variant => this.element.classList.contains(`glass-card-${variant}`)) || 'default'
  }

  /**
   * Get current size
   */
  getSize() {
    const sizes = ['sm', 'lg']
    return sizes.find(size => this.element.classList.contains(`glass-card-${size}`)) || 'default'
  }

  /**
   * Destroy card component
   */
  destroy() {
    // Remove component identifier
    this.element.removeAttribute('data-glass-card')
    
    // Remove state classes
    this.element.classList.remove('glass-card-hover', 'glass-card-focus', 'glass-card-loading', 'glass-card-glow', 'glass-card-scale')
    
    // Remove badge if present
    this.removeBadge()
  }
}
