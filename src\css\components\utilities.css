/* Apple Liquid Glass UI - Utility Components */

/* Badges */
.glass-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--glass-space-small) var(--glass-space-medium);
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-ultra-thin);
  border-radius: var(--glass-radius-pill);
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.8);
  letter-spacing: -0.01em;
  white-space: nowrap;
}

.glass-badge-primary {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border-color: rgba(0, 122, 255, 0.2);
}

.glass-badge-success {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
  border-color: rgba(52, 199, 89, 0.2);
}

.glass-badge-warning {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
  border-color: rgba(255, 149, 0, 0.2);
}

.glass-badge-danger {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  border-color: rgba(255, 59, 48, 0.2);
}

.glass-badge-dot {
  position: relative;
  padding-left: calc(var(--glass-space-medium) + 12px);
}

.glass-badge-dot::before {
  content: '';
  position: absolute;
  left: var(--glass-space-medium);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* Progress Bar */
.glass-progress {
  width: 100%;
  height: 8px;
  background: var(--glass-material-thin);
  border-radius: var(--glass-radius-small);
  overflow: hidden;
  position: relative;
}

.glass-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #5856D6);
  border-radius: var(--glass-radius-small);
  transition: width var(--glass-duration-normal) var(--glass-ease-out-expo);
  position: relative;
}

.glass-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: var(--glass-radius-small);
}

.glass-progress-indeterminate .glass-progress-bar {
  width: 30% !important;
  animation: glass-progress-indeterminate 2s infinite linear;
}

@keyframes glass-progress-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* Avatar */
.glass-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-ultra-thin);
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.glass-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  z-index: 2;
}

.glass-avatar-sm {
  width: 32px;
  height: 32px;
  font-size: 0.75rem;
}

.glass-avatar-md {
  width: 40px;
  height: 40px;
  font-size: 0.875rem;
}

.glass-avatar-lg {
  width: 48px;
  height: 48px;
  font-size: 1rem;
}

.glass-avatar-xl {
  width: 64px;
  height: 64px;
  font-size: 1.25rem;
}

.glass-avatar-initials {
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
  position: relative;
  z-index: 2;
}

/* Divider */
.glass-divider {
  height: 1px;
  background: var(--glass-border-ultra-thin);
  margin: var(--glass-space-large) 0;
  border: none;
}

.glass-divider-vertical {
  width: 1px;
  height: auto;
  background: var(--glass-border-ultra-thin);
  margin: 0 var(--glass-space-large);
  align-self: stretch;
}

.glass-divider-text {
  position: relative;
  text-align: center;
  margin: var(--glass-space-large) 0;
}

.glass-divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--glass-border-ultra-thin);
}

.glass-divider-text span {
  background: var(--glass-material-thin);
  padding: 0 var(--glass-space-medium);
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.6);
  position: relative;
  z-index: 1;
}

/* Loading Spinner */
.glass-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--glass-material-thin);
  border-top: 2px solid #007AFF;
  border-radius: 50%;
  animation: glass-spin 1s linear infinite;
}

.glass-spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

.glass-spinner-lg {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

@keyframes glass-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Skeleton Loader */
.glass-skeleton {
  background: var(--glass-material-thin);
  border-radius: var(--glass-radius-small);
  position: relative;
  overflow: hidden;
}

.glass-skeleton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: glass-skeleton-shimmer 1.5s infinite;
}

@keyframes glass-skeleton-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.glass-skeleton-text {
  height: 1em;
  margin: 0.5em 0;
}

.glass-skeleton-title {
  height: 1.5em;
  width: 60%;
  margin: 0.5em 0;
}

.glass-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

/* Toast Notification */
.glass-toast-container {
  position: fixed;
  top: var(--glass-space-large);
  right: var(--glass-space-large);
  z-index: var(--glass-z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--glass-space-medium);
  pointer-events: none;
}

.glass-toast {
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(180%);
  border: var(--glass-border-regular);
  border-radius: var(--glass-radius-large);
  box-shadow: var(--glass-shadow-heavy);
  padding: var(--glass-space-medium) var(--glass-space-large);
  min-width: 300px;
  max-width: 400px;
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  transform: translateX(100%);
  transition: all var(--glass-duration-normal) var(--glass-ease-spring);
}

.glass-toast.active {
  transform: translateX(0);
}

.glass-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass-toast-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-start;
  gap: var(--glass-space-medium);
}

.glass-toast-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.glass-toast-message {
  flex: 1;
}

.glass-toast-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.9);
  margin: 0 0 4px 0;
}

.glass-toast-description {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.7);
  margin: 0;
  line-height: 1.4;
}

.glass-toast-close {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
  padding: 2px;
  border-radius: var(--glass-radius-small);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass-toast-close:hover {
  background: var(--glass-material-thin);
  color: rgba(0, 0, 0, 0.8);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .glass-toast-container {
    top: var(--glass-space-medium);
    right: var(--glass-space-medium);
    left: var(--glass-space-medium);
  }
  
  .glass-toast {
    min-width: auto;
    max-width: none;
  }
}
