<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass UI - Design System</title>
    <link rel="stylesheet" href="../src/css/index.css">
    <link rel="stylesheet" href="./docs.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 min-h-screen">
    <!-- Navigation -->
    <nav class="glass-nav fixed top-0 left-0 right-0 z-50 glass backdrop-blur-xl bg-white/10 dark:bg-gray-900/10 border-b border-white/20 dark:border-gray-700/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="glass-logo flex items-center space-x-2">
                        <div class="w-8 h-8 rounded-lg glass bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">LG</span>
                        </div>
                        <span class="font-bold text-xl text-gray-900 dark:text-white">Liquid Glass UI</span>
                    </a>
                    <span class="text-gray-500 dark:text-gray-400">/</span>
                    <span class="text-gray-700 dark:text-gray-300">Design System</span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button data-theme-toggle class="glass-btn glass-btn-ghost p-2">
                        <svg class="w-5 h-5 theme-icon-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <svg class="w-5 h-5 theme-icon-dark hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    
                    <a href="index.html" class="glass-btn glass-btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Docs
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-24 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                    <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                        Design System
                    </span>
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    Complete design tokens, color palettes, typography, and spacing guidelines for the Liquid Glass UI framework.
                </p>
            </div>

            <!-- Design Tokens -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Design Tokens</h2>
                
                <!-- Glass Effects -->
                <div class="glass-card p-8 mb-8">
                    <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Glass Effects</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Our glass effects are built using CSS custom properties for consistent and customizable styling.
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">Blur Levels</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 rounded-lg bg-black/10 backdrop-blur-[2px] border border-white/20">
                                    <span class="text-sm font-medium">XS</span>
                                    <code class="text-xs">2px</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-black/10 backdrop-blur-sm border border-white/20">
                                    <span class="text-sm font-medium">SM</span>
                                    <code class="text-xs">4px</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-black/10 backdrop-blur-md border border-white/20">
                                    <span class="text-sm font-medium">MD</span>
                                    <code class="text-xs">8px</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-black/10 backdrop-blur-lg border border-white/20">
                                    <span class="text-sm font-medium">LG</span>
                                    <code class="text-xs">12px</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-black/10 backdrop-blur-xl border border-white/20">
                                    <span class="text-sm font-medium">XL</span>
                                    <code class="text-xs">16px</code>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">Opacity Levels</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/5 border border-white/20">
                                    <span class="text-sm font-medium">Subtle</span>
                                    <code class="text-xs">0.05</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">Light</span>
                                    <code class="text-xs">0.1</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/15 border border-white/20">
                                    <span class="text-sm font-medium">Medium</span>
                                    <code class="text-xs">0.15</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/20 border border-white/20">
                                    <span class="text-sm font-medium">Strong</span>
                                    <code class="text-xs">0.2</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/25 border border-white/20">
                                    <span class="text-sm font-medium">Intense</span>
                                    <code class="text-xs">0.25</code>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">Border Radius</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 rounded-sm bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">SM</span>
                                    <code class="text-xs">0.375rem</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-md bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">MD</span>
                                    <code class="text-xs">0.5rem</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-lg bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">LG</span>
                                    <code class="text-xs">0.75rem</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-xl bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">XL</span>
                                    <code class="text-xs">1rem</code>
                                </div>
                                <div class="flex items-center justify-between p-3 rounded-2xl bg-white/10 border border-white/20">
                                    <span class="text-sm font-medium">2XL</span>
                                    <code class="text-xs">1.5rem</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">CSS Variables</h4>
                        <div class="code-block">
                            <pre><code class="language-css">:root {
  /* Blur levels */
  --glass-blur-xs: 2px;
  --glass-blur-sm: 4px;
  --glass-blur-md: 8px;
  --glass-blur-lg: 12px;
  --glass-blur-xl: 16px;
  
  /* Opacity levels */
  --glass-opacity-subtle: 0.05;
  --glass-opacity-light: 0.1;
  --glass-opacity-medium: 0.15;
  --glass-opacity-strong: 0.2;
  --glass-opacity-intense: 0.25;
  
  /* Border radius */
  --glass-radius-sm: 0.375rem;
  --glass-radius-md: 0.5rem;
  --glass-radius-lg: 0.75rem;
  --glass-radius-xl: 1rem;
  --glass-radius-2xl: 1.5rem;
  
  /* Animation durations */
  --glass-duration-fast: 150ms;
  --glass-duration-normal: 250ms;
  --glass-duration-slow: 350ms;
}</code></pre>
                            <button class="copy-btn" onclick="copyCode(this)">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Color Palette -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Color Palette</h2>
                
                <div class="glass-card p-8">
                    <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Glass Colors</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Our color system is designed to work beautifully with glass effects and provide excellent contrast.
                    </p>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <!-- Primary Colors -->
                        <div class="color-item">
                            <div class="color-swatch bg-blue-500" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);"></div>
                            <div class="color-name">Primary</div>
                            <div class="color-value">#3b82f6</div>
                        </div>
                        
                        <div class="color-item">
                            <div class="color-swatch bg-gray-500" style="background: linear-gradient(135deg, #6b7280, #374151);"></div>
                            <div class="color-name">Secondary</div>
                            <div class="color-value">#6b7280</div>
                        </div>
                        
                        <div class="color-item">
                            <div class="color-swatch bg-green-500" style="background: linear-gradient(135deg, #10b981, #047857);"></div>
                            <div class="color-name">Success</div>
                            <div class="color-value">#10b981</div>
                        </div>
                        
                        <div class="color-item">
                            <div class="color-swatch bg-yellow-500" style="background: linear-gradient(135deg, #f59e0b, #d97706);"></div>
                            <div class="color-name">Warning</div>
                            <div class="color-value">#f59e0b</div>
                        </div>
                        
                        <div class="color-item">
                            <div class="color-swatch bg-red-500" style="background: linear-gradient(135deg, #ef4444, #dc2626);"></div>
                            <div class="color-name">Danger</div>
                            <div class="color-value">#ef4444</div>
                        </div>
                        
                        <div class="color-item">
                            <div class="color-swatch" style="background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)); border: 1px solid rgba(255,255,255,0.2);"></div>
                            <div class="color-name">Ghost</div>
                            <div class="color-value">transparent</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Typography -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Typography</h2>
                
                <div class="glass-card p-8">
                    <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Font System</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        We use Inter as our primary font with system font fallbacks for optimal performance and readability.
                    </p>
                    
                    <div class="space-y-6">
                        <div class="flex items-center space-x-4 p-4 rounded-lg bg-white/5 border border-white/10">
                            <div class="text-6xl font-bold text-gray-900 dark:text-white">Aa</div>
                            <div>
                                <div class="text-lg font-semibold text-gray-900 dark:text-white">Inter</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Primary font family</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 font-mono">font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;</div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Headings</h4>
                                <div class="space-y-3">
                                    <div class="text-4xl font-bold text-gray-900 dark:text-white">Heading 1</div>
                                    <div class="text-3xl font-bold text-gray-900 dark:text-white">Heading 2</div>
                                    <div class="text-2xl font-semibold text-gray-900 dark:text-white">Heading 3</div>
                                    <div class="text-xl font-semibold text-gray-900 dark:text-white">Heading 4</div>
                                    <div class="text-lg font-medium text-gray-900 dark:text-white">Heading 5</div>
                                    <div class="text-base font-medium text-gray-900 dark:text-white">Heading 6</div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Body Text</h4>
                                <div class="space-y-3">
                                    <div class="text-lg text-gray-900 dark:text-white">Large body text for important content</div>
                                    <div class="text-base text-gray-700 dark:text-gray-300">Regular body text for most content</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">Small text for secondary information</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-500">Extra small text for captions and labels</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Spacing -->
            <section class="mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Spacing System</h2>
                
                <div class="glass-card p-8">
                    <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Spacing Scale</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Our spacing system follows a consistent scale based on rem units for scalability and accessibility.
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Spacing Values</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">0</span>
                                    <span class="text-xs text-gray-500">0px</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">1</span>
                                    <span class="text-xs text-gray-500">0.25rem (4px)</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">2</span>
                                    <span class="text-xs text-gray-500">0.5rem (8px)</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">3</span>
                                    <span class="text-xs text-gray-500">0.75rem (12px)</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">4</span>
                                    <span class="text-xs text-gray-500">1rem (16px)</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">6</span>
                                    <span class="text-xs text-gray-500">1.5rem (24px)</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded bg-white/5">
                                    <span class="text-sm">8</span>
                                    <span class="text-xs text-gray-500">2rem (32px)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Visual Examples</h4>
                            <div class="space-y-3">
                                <div class="bg-blue-500/20 h-4 w-4 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-8 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-12 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-16 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-24 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-32 rounded"></div>
                                <div class="bg-blue-500/20 h-4 w-48 rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="../src/index.js"></script>
    <script src="./docs.js"></script>
</body>
</html>
