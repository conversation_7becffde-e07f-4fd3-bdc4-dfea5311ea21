/* Liquid Glass UI - Base Reset */

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: inherit;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/* Glass-specific base styles */
.glass-container {
  position: relative;
  isolation: isolate;
}

/* Ensure backdrop-filter support */
@supports (backdrop-filter: blur(1px)) {
  .glass-supported {
    backdrop-filter: blur(0);
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .glass-fallback {
    background-color: rgba(255, 255, 255, 0.9);
  }
  
  [data-theme="dark"] .glass-fallback {
    background-color: rgba(0, 0, 0, 0.9);
  }
}
