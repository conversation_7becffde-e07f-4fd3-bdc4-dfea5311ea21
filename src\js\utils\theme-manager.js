/**
 * Theme Manager Utility
 * Manages light/dark theme switching and system preferences
 */

export class ThemeManager {
  constructor(initialTheme = 'auto') {
    this.currentTheme = initialTheme
    this.systemTheme = this.getSystemTheme()
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    this.listeners = new Set()
    
    this.storageKey = 'liquid-glass-ui-theme'
  }

  /**
   * Initialize theme manager
   */
  init() {
    // Load saved theme preference
    const savedTheme = this.loadTheme()
    if (savedTheme) {
      this.currentTheme = savedTheme
    }

    // Apply initial theme
    this.applyTheme()

    // Listen for system theme changes
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange.bind(this))

    // Add theme toggle functionality
    this.initThemeToggles()
  }

  /**
   * Get system theme preference
   */
  getSystemTheme() {
    return this.mediaQuery.matches ? 'dark' : 'light'
  }

  /**
   * Get effective theme (resolves 'auto' to actual theme)
   */
  getEffectiveTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme
    }
    return this.currentTheme
  }

  /**
   * Set theme
   */
  setTheme(theme) {
    if (!['light', 'dark', 'auto'].includes(theme)) {
      console.warn(`Invalid theme: ${theme}. Using 'auto' instead.`)
      theme = 'auto'
    }

    this.currentTheme = theme
    this.applyTheme()
    this.saveTheme()
    this.notifyListeners()
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const effectiveTheme = this.getEffectiveTheme()
    const newTheme = effectiveTheme === 'light' ? 'dark' : 'light'
    this.setTheme(newTheme)
  }

  /**
   * Apply theme to document
   */
  applyTheme() {
    const effectiveTheme = this.getEffectiveTheme()
    
    // Remove existing theme classes
    document.documentElement.classList.remove('theme-light', 'theme-dark')
    document.documentElement.removeAttribute('data-theme')
    
    // Add new theme class and attribute
    document.documentElement.classList.add(`theme-${effectiveTheme}`)
    document.documentElement.setAttribute('data-theme', effectiveTheme)

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(effectiveTheme)

    // Dispatch theme change event
    this.dispatchThemeEvent(effectiveTheme)
  }

  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]')
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta')
      metaThemeColor.name = 'theme-color'
      document.head.appendChild(metaThemeColor)
    }

    const colors = {
      light: '#ffffff',
      dark: '#000000'
    }

    metaThemeColor.content = colors[theme] || colors.light
  }

  /**
   * Dispatch custom theme change event
   */
  dispatchThemeEvent(theme) {
    const event = new CustomEvent('themechange', {
      detail: {
        theme,
        previousTheme: this.previousTheme,
        isSystemTheme: this.currentTheme === 'auto'
      }
    })
    
    this.previousTheme = theme
    document.dispatchEvent(event)
  }

  /**
   * Handle system theme changes
   */
  handleSystemThemeChange(event) {
    this.systemTheme = event.matches ? 'dark' : 'light'
    
    if (this.currentTheme === 'auto') {
      this.applyTheme()
      this.notifyListeners()
    }
  }

  /**
   * Initialize theme toggle buttons
   */
  initThemeToggles() {
    const toggles = document.querySelectorAll('[data-theme-toggle]')
    
    toggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        this.toggleTheme()
      })
    })

    // Initialize theme selectors
    const selectors = document.querySelectorAll('[data-theme-selector]')
    
    selectors.forEach(selector => {
      selector.addEventListener('change', (event) => {
        this.setTheme(event.target.value)
      })
      
      // Set initial value
      selector.value = this.currentTheme
    })
  }

  /**
   * Add theme change listener
   */
  addListener(callback) {
    this.listeners.add(callback)
    
    return () => {
      this.listeners.delete(callback)
    }
  }

  /**
   * Notify all listeners of theme change
   */
  notifyListeners() {
    const effectiveTheme = this.getEffectiveTheme()
    
    this.listeners.forEach(callback => {
      try {
        callback(effectiveTheme, this.currentTheme)
      } catch (error) {
        console.error('Error in theme change listener:', error)
      }
    })
  }

  /**
   * Save theme preference to localStorage
   */
  saveTheme() {
    try {
      localStorage.setItem(this.storageKey, this.currentTheme)
    } catch (error) {
      console.warn('Could not save theme preference:', error)
    }
  }

  /**
   * Load theme preference from localStorage
   */
  loadTheme() {
    try {
      return localStorage.getItem(this.storageKey)
    } catch (error) {
      console.warn('Could not load theme preference:', error)
      return null
    }
  }

  /**
   * Get theme-specific CSS custom property value
   */
  getThemeValue(property) {
    return getComputedStyle(document.documentElement).getPropertyValue(property)
  }

  /**
   * Set theme-specific CSS custom property
   */
  setThemeValue(property, value) {
    document.documentElement.style.setProperty(property, value)
  }

  /**
   * Create theme-aware color scheme
   */
  createColorScheme(lightColor, darkColor) {
    const effectiveTheme = this.getEffectiveTheme()
    return effectiveTheme === 'dark' ? darkColor : lightColor
  }

  /**
   * Destroy theme manager
   */
  destroy() {
    this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange.bind(this))
    this.listeners.clear()
  }

  /**
   * Get current theme info
   */
  getThemeInfo() {
    return {
      current: this.currentTheme,
      effective: this.getEffectiveTheme(),
      system: this.systemTheme,
      isAuto: this.currentTheme === 'auto'
    }
  }
}
