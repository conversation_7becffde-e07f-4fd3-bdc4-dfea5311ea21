/* Liquid Glass UI - Core Glass Effects */

/* Base Glass Effect Classes */
.glass {
  position: relative;
  background: var(--glass-white-800);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-md);
}

.glass-dark {
  background: var(--glass-black-800);
  border: 1px solid var(--glass-border-dark);
}

/* Glass Intensity Variants */
.glass-subtle {
  background: var(--glass-white-900);
  backdrop-filter: blur(var(--glass-blur-sm));
  -webkit-backdrop-filter: blur(var(--glass-blur-sm));
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-sm);
}

.glass-light {
  background: var(--glass-white-800);
  backdrop-filter: blur(var(--glass-blur-md));
  -webkit-backdrop-filter: blur(var(--glass-blur-md));
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow-md);
}

.glass-medium {
  background: var(--glass-white-700);
  backdrop-filter: blur(var(--glass-blur-lg));
  -webkit-backdrop-filter: blur(var(--glass-blur-lg));
  border: 1px solid var(--glass-border-medium);
  box-shadow: var(--glass-shadow-lg);
}

.glass-strong {
  background: var(--glass-white-600);
  backdrop-filter: blur(var(--glass-blur-xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-xl));
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--glass-shadow-xl);
}

.glass-intense {
  background: var(--glass-white-500);
  backdrop-filter: blur(var(--glass-blur-2xl));
  -webkit-backdrop-filter: blur(var(--glass-blur-2xl));
  border: 1px solid var(--glass-border-strong);
  box-shadow: var(--glass-shadow-2xl);
}

/* Dark Theme Variants */
[data-theme="dark"] .glass-subtle {
  background: var(--glass-black-900);
  border: 1px solid var(--glass-border-dark);
}

[data-theme="dark"] .glass-light {
  background: var(--glass-black-800);
  border: 1px solid var(--glass-border-dark);
}

[data-theme="dark"] .glass-medium {
  background: var(--glass-black-700);
  border: 1px solid var(--glass-border-dark-medium);
}

[data-theme="dark"] .glass-strong {
  background: var(--glass-black-600);
  border: 1px solid var(--glass-border-dark-strong);
}

[data-theme="dark"] .glass-intense {
  background: var(--glass-black-500);
  border: 1px solid var(--glass-border-dark-strong);
}

/* Glass Surface Effects */
.glass-frosted {
  backdrop-filter: blur(var(--glass-blur-lg)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-lg)) saturate(180%);
}

.glass-crystal {
  backdrop-filter: blur(var(--glass-blur-sm)) brightness(110%) contrast(120%);
  -webkit-backdrop-filter: blur(var(--glass-blur-sm)) brightness(110%) contrast(120%);
}

.glass-liquid {
  backdrop-filter: blur(var(--glass-blur-md)) hue-rotate(10deg) saturate(150%);
  -webkit-backdrop-filter: blur(var(--glass-blur-md)) hue-rotate(10deg) saturate(150%);
}

/* Glass Border Styles */
.glass-border-none {
  border: none;
}

.glass-border-subtle {
  border: 1px solid var(--glass-border-light);
}

.glass-border-medium {
  border: 1px solid var(--glass-border-medium);
}

.glass-border-strong {
  border: 2px solid var(--glass-border-strong);
}

.glass-border-gradient {
  border: 1px solid transparent;
  background: linear-gradient(var(--glass-white-800), var(--glass-white-800)) padding-box,
              linear-gradient(135deg, var(--glass-border-strong), var(--glass-border-light)) border-box;
}

/* Glass Shadow Variants */
.glass-shadow-none {
  box-shadow: none;
}

.glass-shadow-sm {
  box-shadow: var(--glass-shadow-sm);
}

.glass-shadow-md {
  box-shadow: var(--glass-shadow-md);
}

.glass-shadow-lg {
  box-shadow: var(--glass-shadow-lg);
}

.glass-shadow-xl {
  box-shadow: var(--glass-shadow-xl);
}

.glass-shadow-2xl {
  box-shadow: var(--glass-shadow-2xl);
}

.glass-shadow-inner {
  box-shadow: var(--glass-shadow-inner);
}

.glass-shadow-combined {
  box-shadow: var(--glass-shadow-md), var(--glass-shadow-inner);
}

/* Glass Radius Variants */
.glass-rounded-none {
  border-radius: 0;
}

.glass-rounded-sm {
  border-radius: var(--glass-radius-sm);
}

.glass-rounded-md {
  border-radius: var(--glass-radius-md);
}

.glass-rounded-lg {
  border-radius: var(--glass-radius-lg);
}

.glass-rounded-xl {
  border-radius: var(--glass-radius-xl);
}

.glass-rounded-2xl {
  border-radius: var(--glass-radius-2xl);
}

.glass-rounded-full {
  border-radius: var(--glass-radius-full);
}

/* Glass Overlay Effects */
.glass-overlay {
  position: relative;
  overflow: hidden;
}

.glass-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
  z-index: 1;
}

.glass-overlay > * {
  position: relative;
  z-index: 2;
}

/* Performance Optimizations */
.glass,
.glass-subtle,
.glass-light,
.glass-medium,
.glass-strong,
.glass-intense {
  will-change: backdrop-filter, transform;
  transform: translateZ(0);
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .glass,
  .glass-subtle,
  .glass-light,
  .glass-medium,
  .glass-strong,
  .glass-intense {
    background: rgba(255, 255, 255, 0.9);
  }
  
  [data-theme="dark"] .glass,
  [data-theme="dark"] .glass-subtle,
  [data-theme="dark"] .glass-light,
  [data-theme="dark"] .glass-medium,
  [data-theme="dark"] .glass-strong,
  [data-theme="dark"] .glass-intense {
    background: rgba(0, 0, 0, 0.9);
  }
}
