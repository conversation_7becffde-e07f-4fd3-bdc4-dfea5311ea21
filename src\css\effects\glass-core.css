/* Apple Liquid Glass UI - Authentic Core Glass Effects */

/* Apple Material Base Class */
.glass {
  position: relative;
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  box-shadow: var(--glass-shadow-regular);
  overflow: hidden;
}

/* Apple Material Variants */
.glass-ultra-thin {
  background: var(--glass-material-ultra-thin);
  backdrop-filter: blur(var(--glass-blur-ultra)) saturate(200%);
  -webkit-backdrop-filter: blur(var(--glass-blur-ultra)) saturate(200%);
  border: var(--glass-border-ultra-thin);
  box-shadow: var(--glass-shadow-ultra-thin);
}

.glass-thin {
  background: var(--glass-material-thin);
  backdrop-filter: blur(var(--glass-blur-thin)) saturate(190%);
  -webkit-backdrop-filter: blur(var(--glass-blur-thin)) saturate(190%);
  border: var(--glass-border-thin);
  box-shadow: var(--glass-shadow-thin);
}

.glass-regular {
  background: var(--glass-material-regular);
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%);
  border: var(--glass-border-regular);
  box-shadow: var(--glass-shadow-regular);
}

.glass-thick {
  background: var(--glass-material-thick);
  backdrop-filter: blur(var(--glass-blur-thick)) saturate(170%);
  -webkit-backdrop-filter: blur(var(--glass-blur-thick)) saturate(170%);
  border: var(--glass-border-thick);
  box-shadow: var(--glass-shadow-thick);
}

.glass-heavy {
  background: var(--glass-material-heavy);
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(160%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(160%);
  border: var(--glass-border-thick);
  box-shadow: var(--glass-shadow-heavy);
}

.glass-ultra-heavy {
  background: var(--glass-material-ultra-heavy);
  backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(150%);
  -webkit-backdrop-filter: blur(var(--glass-blur-ultra-heavy)) saturate(150%);
  border: var(--glass-border-thick);
  box-shadow: var(--glass-shadow-heavy);
}

/* Apple Material Reflection Effects */
.glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: var(--glass-highlight-top);
  pointer-events: none;
  z-index: 1;
}

.glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--glass-noise);
  pointer-events: none;
  z-index: 2;
  mix-blend-mode: overlay;
}

/* Ensure content appears above reflections */
.glass > * {
  position: relative;
  z-index: 3;
}

/* Apple Vibrancy Effects */
.glass-vibrancy {
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%) brightness(110%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(180%) brightness(110%);
}

.glass-vibrancy-light {
  backdrop-filter: blur(var(--glass-blur-thin)) saturate(200%) brightness(115%);
  -webkit-backdrop-filter: blur(var(--glass-blur-thin)) saturate(200%) brightness(115%);
}

.glass-vibrancy-heavy {
  backdrop-filter: blur(var(--glass-blur-heavy)) saturate(160%) brightness(105%);
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(160%) brightness(105%);
}

/* Apple Border Styles */
.glass-border-none {
  border: none;
}

.glass-border-ultra-thin {
  border: var(--glass-border-ultra-thin);
}

.glass-border-thin {
  border: var(--glass-border-thin);
}

.glass-border-regular {
  border: var(--glass-border-regular);
}

.glass-border-thick {
  border: var(--glass-border-thick);
}

/* Apple Shadow Variants */
.glass-shadow-none {
  box-shadow: none;
}

.glass-shadow-ultra-thin {
  box-shadow: var(--glass-shadow-ultra-thin);
}

.glass-shadow-thin {
  box-shadow: var(--glass-shadow-thin);
}

.glass-shadow-regular {
  box-shadow: var(--glass-shadow-regular);
}

.glass-shadow-thick {
  box-shadow: var(--glass-shadow-thick);
}

.glass-shadow-heavy {
  box-shadow: var(--glass-shadow-heavy);
}

/* Apple Radius Variants */
.glass-rounded-none {
  border-radius: 0;
}

.glass-rounded-ultra-small {
  border-radius: var(--glass-radius-ultra-small);
}

.glass-rounded-small {
  border-radius: var(--glass-radius-small);
}

.glass-rounded-medium {
  border-radius: var(--glass-radius-medium);
}

.glass-rounded-large {
  border-radius: var(--glass-radius-large);
}

.glass-rounded-extra-large {
  border-radius: var(--glass-radius-extra-large);
}

.glass-rounded-ultra-large {
  border-radius: var(--glass-radius-ultra-large);
}

.glass-rounded-pill {
  border-radius: var(--glass-radius-pill);
}

/* Apple Interactive States */
.glass:hover {
  backdrop-filter: blur(var(--glass-blur-regular)) saturate(200%) brightness(105%);
  -webkit-backdrop-filter: blur(var(--glass-blur-regular)) saturate(200%) brightness(105%);
  transform: translateY(-1px) scale(1.02);
  transition: all var(--glass-duration-fast) var(--glass-ease-out-expo);
}

.glass:active {
  transform: translateY(0) scale(0.98);
  transition: all var(--glass-duration-instant) var(--glass-ease-out-quart);
}

/* Apple Performance Optimizations */
.glass,
.glass-ultra-thin,
.glass-thin,
.glass-regular,
.glass-thick,
.glass-heavy,
.glass-ultra-heavy {
  will-change: backdrop-filter, transform;
  transform: translateZ(0);
  transition: all var(--glass-duration-normal) var(--glass-ease-out-expo);
}

/* Apple Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(1px)) {
  .glass,
  .glass-ultra-thin,
  .glass-thin,
  .glass-regular,
  .glass-thick,
  .glass-heavy,
  .glass-ultra-heavy {
    background: rgba(255, 255, 255, 0.95);
  }

  [data-theme="dark"] .glass,
  [data-theme="dark"] .glass-ultra-thin,
  [data-theme="dark"] .glass-thin,
  [data-theme="dark"] .glass-regular,
  [data-theme="dark"] .glass-thick,
  [data-theme="dark"] .glass-heavy,
  [data-theme="dark"] .glass-ultra-heavy {
    background: rgba(28, 28, 30, 0.95);
  }
}
