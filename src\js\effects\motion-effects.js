/**
 * Motion Effects
 * Creates device orientation-based effects for mobile devices
 */

export class MotionEffects {
  constructor(options = {}) {
    this.options = {
      enableTilt: true,
      enableParallax: true,
      enableLighting: true,
      sensitivity: 1,
      maxTilt: 15,
      smoothing: 0.1,
      ...options
    }

    this.orientation = { alpha: 0, beta: 0, gamma: 0 }
    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }
    this.elements = new Set()
    this.isActive = false
    this.animationFrame = null
    this.permissionGranted = false
  }

  /**
   * Initialize motion effects
   */
  async init() {
    if (this.isActive) return

    // Check if device orientation is supported
    if (!this.isSupported()) {
      console.warn('Device orientation not supported')
      return false
    }

    // Request permission for iOS 13+
    if (typeof DeviceOrientationEvent.requestPermission === 'function') {
      try {
        const permission = await DeviceOrientationEvent.requestPermission()
        this.permissionGranted = permission === 'granted'
      } catch (error) {
        console.warn('Device orientation permission denied:', error)
        return false
      }
    } else {
      this.permissionGranted = true
    }

    if (!this.permissionGranted) {
      return false
    }

    this.isActive = true
    this.bindEvents()
    this.findElements()
    this.startAnimation()
    
    return true
  }

  /**
   * Check if device orientation is supported
   */
  isSupported() {
    return 'DeviceOrientationEvent' in window
  }

  /**
   * Bind device orientation events
   */
  bindEvents() {
    this.handleDeviceOrientation = this.handleDeviceOrientation.bind(this)
    window.addEventListener('deviceorientation', this.handleDeviceOrientation)
  }

  /**
   * Find elements with motion effects
   */
  findElements() {
    const selectors = [
      '[data-motion-effect]',
      '.glass-motion-effect',
      '.motion-tilt',
      '.motion-parallax'
    ]

    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        this.addElement(element)
      })
    })
  }

  /**
   * Add element to motion effects
   */
  addElement(element) {
    if (this.elements.has(element)) return

    this.elements.add(element)
    
    // Add CSS custom properties for motion effects
    element.style.setProperty('--motion-x', '0')
    element.style.setProperty('--motion-y', '0')
    element.style.setProperty('--motion-z', '0')
    element.style.setProperty('--motion-intensity', '0')

    // Add motion effect class
    element.classList.add('has-motion-effect')
  }

  /**
   * Remove element from motion effects
   */
  removeElement(element) {
    if (!this.elements.has(element)) return

    this.elements.delete(element)
    element.classList.remove('has-motion-effect')
    
    // Reset CSS custom properties
    element.style.removeProperty('--motion-x')
    element.style.removeProperty('--motion-y')
    element.style.removeProperty('--motion-z')
    element.style.removeProperty('--motion-intensity')
  }

  /**
   * Handle device orientation change
   */
  handleDeviceOrientation(event) {
    this.orientation.alpha = event.alpha || 0  // Z axis (0-360)
    this.orientation.beta = event.beta || 0    // X axis (-180 to 180)
    this.orientation.gamma = event.gamma || 0  // Y axis (-90 to 90)
  }

  /**
   * Start animation loop
   */
  startAnimation() {
    const animate = () => {
      if (!this.isActive) return

      this.updateOrientation()
      this.updateElements()
      
      this.animationFrame = requestAnimationFrame(animate)
    }

    animate()
  }

  /**
   * Update smooth orientation values
   */
  updateOrientation() {
    this.smoothOrientation.alpha += (this.orientation.alpha - this.smoothOrientation.alpha) * this.options.smoothing
    this.smoothOrientation.beta += (this.orientation.beta - this.smoothOrientation.beta) * this.options.smoothing
    this.smoothOrientation.gamma += (this.orientation.gamma - this.smoothOrientation.gamma) * this.options.smoothing
  }

  /**
   * Update all elements with motion effects
   */
  updateElements() {
    this.elements.forEach(element => {
      this.updateElement(element)
    })
  }

  /**
   * Update individual element
   */
  updateElement(element) {
    const { beta, gamma } = this.smoothOrientation
    
    // Normalize orientation values
    const normalizedX = this.clamp(gamma / 90, -1, 1) * this.options.sensitivity
    const normalizedY = this.clamp(beta / 180, -1, 1) * this.options.sensitivity
    const intensity = Math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY)

    // Update CSS custom properties
    element.style.setProperty('--motion-x', normalizedX.toFixed(3))
    element.style.setProperty('--motion-y', normalizedY.toFixed(3))
    element.style.setProperty('--motion-intensity', Math.min(intensity, 1).toFixed(3))

    // Apply specific effects
    if (this.options.enableTilt) {
      this.applyTiltEffect(element, normalizedX, normalizedY)
    }

    if (this.options.enableParallax) {
      this.applyParallaxEffect(element, normalizedX, normalizedY)
    }

    if (this.options.enableLighting) {
      this.applyLightingEffect(element, normalizedX, normalizedY, intensity)
    }
  }

  /**
   * Apply tilt effect based on device orientation
   */
  applyTiltEffect(element, x, y) {
    const tiltX = y * this.options.maxTilt
    const tiltY = -x * this.options.maxTilt
    
    element.style.setProperty('--motion-tilt-x', `${tiltX}deg`)
    element.style.setProperty('--motion-tilt-y', `${tiltY}deg`)
  }

  /**
   * Apply parallax effect based on device orientation
   */
  applyParallaxEffect(element, x, y) {
    const parallaxX = x * 20
    const parallaxY = y * 20
    
    element.style.setProperty('--motion-parallax-x', `${parallaxX}px`)
    element.style.setProperty('--motion-parallax-y', `${parallaxY}px`)
  }

  /**
   * Apply lighting effect based on device orientation
   */
  applyLightingEffect(element, x, y, intensity) {
    // Create gradient based on tilt direction
    const gradientX = (x + 1) * 50  // Convert -1,1 to 0,100
    const gradientY = (y + 1) * 50  // Convert -1,1 to 0,100
    const lightIntensity = intensity * 0.3
    
    const gradient = `radial-gradient(circle at ${gradientX}% ${gradientY}%, rgba(255, 255, 255, ${lightIntensity}) 0%, transparent 70%)`
    element.style.setProperty('--motion-light', gradient)
  }

  /**
   * Create shake effect
   */
  createShake(element, intensity = 1) {
    const shakeClass = `motion-shake-${Math.floor(intensity * 3) + 1}`
    element.classList.add(shakeClass)
    
    setTimeout(() => {
      element.classList.remove(shakeClass)
    }, 500)
  }

  /**
   * Clamp value between min and max
   */
  clamp(value, min, max) {
    return Math.min(Math.max(value, min), max)
  }

  /**
   * Request permission for device orientation (iOS 13+)
   */
  async requestPermission() {
    if (typeof DeviceOrientationEvent.requestPermission === 'function') {
      try {
        const permission = await DeviceOrientationEvent.requestPermission()
        this.permissionGranted = permission === 'granted'
        return this.permissionGranted
      } catch (error) {
        console.error('Error requesting device orientation permission:', error)
        return false
      }
    }
    
    this.permissionGranted = true
    return true
  }

  /**
   * Calibrate device orientation
   */
  calibrate() {
    // Reset orientation baseline
    this.orientation = { alpha: 0, beta: 0, gamma: 0 }
    this.smoothOrientation = { alpha: 0, beta: 0, gamma: 0 }
  }

  /**
   * Update options
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
  }

  /**
   * Pause motion effects
   */
  pause() {
    this.isActive = false
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }

  /**
   * Resume motion effects
   */
  resume() {
    if (!this.isActive && this.permissionGranted) {
      this.isActive = true
      this.startAnimation()
    }
  }

  /**
   * Destroy motion effects
   */
  destroy() {
    this.pause()
    
    // Remove event listeners
    window.removeEventListener('deviceorientation', this.handleDeviceOrientation)

    // Clean up elements
    this.elements.forEach(element => {
      this.removeElement(element)
    })
    
    this.elements.clear()
  }

  /**
   * Get current orientation
   */
  getOrientation() {
    return { ...this.smoothOrientation }
  }

  /**
   * Check if motion effects are active
   */
  isEnabled() {
    return this.isActive && this.permissionGranted
  }
}
